package com.wingame.account.controller;

import com.wingame.account.common.Result;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * 账户控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/account")
@RequiredArgsConstructor
public class AccountController {
    
    /**
     * 获取账户余额
     */
    @GetMapping("/{userId}/balance")
    public Result<Object> getBalance(@PathVariable String userId) {
        log.info("获取用户 {} 的账户余额", userId);
        
        Object balance = new Object() {
            public final String userId2 = userId;
            public final BigDecimal totalBalance = new BigDecimal("15680.50");
            public final BigDecimal availableBalance = new BigDecimal("14230.50");
            public final BigDecimal frozenBalance = new BigDecimal("1450.00");
            public final BigDecimal coins = new BigDecimal("8500");
            public final BigDecimal points = new BigDecimal("2340");
            public final LocalDateTime lastUpdated = LocalDateTime.now();
        };
        
        return Result.success("获取余额成功", balance);
    }
    
    /**
     * 充值
     */
    @PostMapping("/{userId}/recharge")
    public Result<Object> recharge(@PathVariable String userId, 
                                   @RequestParam BigDecimal amount,
                                   @RequestParam String paymentMethod) {
        log.info("用户 {} 充值 {} 元，支付方式: {}", userId, amount, paymentMethod);
        
        Object rechargeResult = new Object() {
            public final String transactionId = UUID.randomUUID().toString();
            public final String userId2 = userId;
            public final BigDecimal amount2 = amount;
            public final String paymentMethod2 = paymentMethod;
            public final String status = "SUCCESS";
            public final LocalDateTime transactionTime = LocalDateTime.now();
            public final BigDecimal balanceAfter = new BigDecimal("15680.50").add(amount);
            public final String description = "账户充值";
        };
        
        return Result.success("充值成功", rechargeResult);
    }


    /**
     * 健康检查
     */
    @GetMapping("/health")
    public Result<String> health() {
        return Result.success("活动服务运行正常");
    }
}
