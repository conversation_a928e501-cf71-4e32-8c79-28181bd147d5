package com.wingame.account;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;

/**
 * 账户服务启动类
 */
@SpringBootApplication(scanBasePackages = {"com.wingame.account", "com.wingame.common"})
@EnableDiscoveryClient
@EnableFeignClients
public class AccountApplication {
    
    public static void main(String[] args) {
        SpringApplication.run(AccountApplication.class, args);
    }
}
