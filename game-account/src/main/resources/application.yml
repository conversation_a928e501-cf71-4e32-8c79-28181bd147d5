server:
  port: 8085

spring:
  application:
    name: game-account
  profiles:
    active: dev
  config:
    import:
      - optional:nacos:game-account.yml
  cloud:
    nacos:
      discovery:
        server-addr: localhost:8848
        namespace: dev
      config:
        server-addr: localhost:8848
        namespace: dev
        file-extension: yml
        group: DEFAULT_GROUP
  data:
    mongodb:
      host: localhost
      port: 27017
      database: wingame_account
      username: 
      password: 
    redis:
      host: localhost
      port: 6379
      password: 
      database: 4
      timeout: 3000ms
      lettuce:
        pool:
          max-active: 8
          max-wait: -1ms
          max-idle: 8
          min-idle: 0

logging:
  level:
    com.wingame.account: debug
    org.springframework.data.mongodb: debug
