#!/bin/bash

echo "=== SaaS登录接口测试脚本 ==="
echo ""

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 服务配置
USER_SERVICE_URL="http://localhost:8081"
GATEWAY_URL="http://localhost:8080"

# 使用网关还是直接访问用户服务
USE_GATEWAY=true

if [ "$USE_GATEWAY" = true ]; then
    BASE_URL="$GATEWAY_URL/user"
else
    BASE_URL="$USER_SERVICE_URL"
fi

echo -e "${BLUE}测试基础URL: $BASE_URL${NC}"
echo ""

# 测试服务健康状态
test_service_health() {
    echo -e "${CYAN}1. 测试服务健康状态${NC}"
    
    response=$(curl -s -w "HTTP_CODE:%{http_code}" "$BASE_URL/actuator/health")
    http_code=$(echo "$response" | grep "HTTP_CODE:" | cut -d: -f2)
    body=$(echo "$response" | sed '/HTTP_CODE:/d')
    
    if [ "$http_code" = "200" ]; then
        echo -e "${GREEN}✅ 用户服务健康检查通过${NC}"
        echo "响应: $body"
    else
        echo -e "${RED}❌ 用户服务健康检查失败 (HTTP $http_code)${NC}"
        echo "响应: $body"
        return 1
    fi
    
    echo ""
}

# 测试获取网站模板数据
test_website_model() {
    echo -e "${CYAN}2. 测试获取网站模板数据${NC}"
    
    response=$(curl -s -w "HTTP_CODE:%{http_code}" "$BASE_URL/api/login/webSiteModel")
    http_code=$(echo "$response" | grep "HTTP_CODE:" | cut -d: -f2)
    body=$(echo "$response" | sed '/HTTP_CODE:/d')
    
    if [ "$http_code" = "200" ]; then
        echo -e "${GREEN}✅ 获取网站模板数据成功${NC}"
        echo "$body" | python3 -m json.tool 2>/dev/null | head -20
    else
        echo -e "${RED}❌ 获取网站模板数据失败 (HTTP $http_code)${NC}"
        echo "响应: $body"
    fi
    
    echo ""
}

# 测试获取未登录用户数据
test_not_logged_in_data() {
    echo -e "${CYAN}3. 测试获取未登录用户数据${NC}"
    
    response=$(curl -s -w "HTTP_CODE:%{http_code}" "$BASE_URL/api/login/notLoggedIn")
    http_code=$(echo "$response" | grep "HTTP_CODE:" | cut -d: -f2)
    body=$(echo "$response" | sed '/HTTP_CODE:/d')
    
    if [ "$http_code" = "200" ]; then
        echo -e "${GREEN}✅ 获取未登录用户数据成功${NC}"
        echo "$body" | python3 -m json.tool 2>/dev/null
    else
        echo -e "${RED}❌ 获取未登录用户数据失败 (HTTP $http_code)${NC}"
        echo "响应: $body"
    fi
    
    echo ""
}

# 测试获取图形验证码
test_image_verify_code() {
    echo -e "${CYAN}4. 测试获取图形验证码${NC}"
    
    response=$(curl -s -w "HTTP_CODE:%{http_code}" "$BASE_URL/api/login/verifyCode")
    http_code=$(echo "$response" | grep "HTTP_CODE:" | cut -d: -f2)
    body=$(echo "$response" | sed '/HTTP_CODE:/d')
    
    if [ "$http_code" = "200" ]; then
        echo -e "${GREEN}✅ 获取图形验证码成功${NC}"
        
        # 提取验证码key
        verify_code_key=$(echo "$body" | python3 -c "import sys, json; data=json.load(sys.stdin); print(data.get('key', ''))" 2>/dev/null)
        echo "验证码Key: $verify_code_key"
        
        # 保存key供后续使用
        echo "$verify_code_key" > /tmp/verify_code_key.txt
        
        # 显示图片信息（Base64长度）
        image_data=$(echo "$body" | python3 -c "import sys, json; data=json.load(sys.stdin); print(len(data.get('data', '')))" 2>/dev/null)
        echo "图片数据长度: $image_data 字符"
    else
        echo -e "${RED}❌ 获取图形验证码失败 (HTTP $http_code)${NC}"
        echo "响应: $body"
    fi
    
    echo ""
}

# 测试发送邮箱验证码
test_email_verify_code() {
    echo -e "${CYAN}5. 测试发送邮箱验证码${NC}"
    
    # 读取验证码key
    verify_code_key=""
    if [ -f "/tmp/verify_code_key.txt" ]; then
        verify_code_key=$(cat /tmp/verify_code_key.txt)
    fi
    
    # 构建请求数据
    request_data='{
        "type": "email",
        "target": "<EMAIL>",
        "purpose": "register",
        "imageCode": "1234",
        "imageCodeKey": "'$verify_code_key'"
    }'
    
    response=$(curl -s -w "HTTP_CODE:%{http_code}" \
        -H "Content-Type: application/json" \
        -d "$request_data" \
        "$BASE_URL/api/login/verifyCode")
    
    http_code=$(echo "$response" | grep "HTTP_CODE:" | cut -d: -f2)
    body=$(echo "$response" | sed '/HTTP_CODE:/d')
    
    if [ "$http_code" = "200" ]; then
        echo -e "${GREEN}✅ 发送邮箱验证码成功${NC}"
        echo "$body" | python3 -m json.tool 2>/dev/null
    else
        echo -e "${YELLOW}⚠️ 发送邮箱验证码失败 (HTTP $http_code)${NC}"
        echo "响应: $body"
        echo "注意: 这可能是因为图形验证码错误，这是正常的测试结果"
    fi
    
    echo ""
}

# 测试发送手机验证码
test_phone_verify_code() {
    echo -e "${CYAN}6. 测试发送手机验证码${NC}"
    
    # 读取验证码key
    verify_code_key=""
    if [ -f "/tmp/verify_code_key.txt" ]; then
        verify_code_key=$(cat /tmp/verify_code_key.txt)
    fi
    
    response=$(curl -s -w "HTTP_CODE:%{http_code}" \
        -X POST \
        "$BASE_URL/api/login/phoneVerifyCode?phone=13800138000&purpose=register&imageCode=1234&imageCodeKey=$verify_code_key")
    
    http_code=$(echo "$response" | grep "HTTP_CODE:" | cut -d: -f2)
    body=$(echo "$response" | sed '/HTTP_CODE:/d')
    
    if [ "$http_code" = "200" ]; then
        echo -e "${GREEN}✅ 发送手机验证码成功${NC}"
        echo "$body" | python3 -m json.tool 2>/dev/null
    else
        echo -e "${YELLOW}⚠️ 发送手机验证码失败 (HTTP $http_code)${NC}"
        echo "响应: $body"
        echo "注意: 这可能是因为图形验证码错误，这是正常的测试结果"
    fi
    
    echo ""
}

# 测试用户注册
test_user_register() {
    echo -e "${CYAN}7. 测试用户注册${NC}"
    
    # 生成随机用户名
    random_suffix=$(date +%s)
    username="testuser$random_suffix"
    
    request_data='{
        "username": "'$username'",
        "password": "123456",
        "confirmPassword": "123456",
        "email": "test'$random_suffix'@example.com",
        "phone": "138001380'$(echo $random_suffix | tail -c 3)'",
        "nickname": "测试用户'$random_suffix'",
        "agreeTerms": true,
        "registerSource": "web"
    }'
    
    response=$(curl -s -w "HTTP_CODE:%{http_code}" \
        -H "Content-Type: application/json" \
        -d "$request_data" \
        "$BASE_URL/api/login/register")
    
    http_code=$(echo "$response" | grep "HTTP_CODE:" | cut -d: -f2)
    body=$(echo "$response" | sed '/HTTP_CODE:/d')
    
    if [ "$http_code" = "200" ]; then
        echo -e "${GREEN}✅ 用户注册成功${NC}"
        echo "$body" | python3 -m json.tool 2>/dev/null | head -20
        
        # 保存用户信息供登录测试使用
        echo "$username" > /tmp/test_username.txt
        echo "123456" > /tmp/test_password.txt
        
        # 提取access token
        access_token=$(echo "$body" | python3 -c "import sys, json; data=json.load(sys.stdin); print(data.get('data', {}).get('accessToken', ''))" 2>/dev/null)
        if [ -n "$access_token" ]; then
            echo "$access_token" > /tmp/access_token.txt
            echo "Access Token已保存"
        fi
    else
        echo -e "${RED}❌ 用户注册失败 (HTTP $http_code)${NC}"
        echo "响应: $body"
    fi
    
    echo ""
}

# 测试用户登录
test_user_login() {
    echo -e "${CYAN}8. 测试用户登录${NC}"
    
    # 读取测试用户信息
    username=""
    password=""
    if [ -f "/tmp/test_username.txt" ]; then
        username=$(cat /tmp/test_username.txt)
        password=$(cat /tmp/test_password.txt)
    else
        # 使用默认测试用户
        username="admin"
        password="123456"
    fi
    
    request_data='{
        "username": "'$username'",
        "password": "'$password'",
        "deviceType": "web",
        "loginSource": "test",
        "rememberMe": true
    }'
    
    response=$(curl -s -w "HTTP_CODE:%{http_code}" \
        -H "Content-Type: application/json" \
        -d "$request_data" \
        "$BASE_URL/api/login/game")
    
    http_code=$(echo "$response" | grep "HTTP_CODE:" | cut -d: -f2)
    body=$(echo "$response" | sed '/HTTP_CODE:/d')
    
    if [ "$http_code" = "200" ]; then
        echo -e "${GREEN}✅ 用户登录成功${NC}"
        echo "$body" | python3 -m json.tool 2>/dev/null | head -20
        
        # 提取tokens
        access_token=$(echo "$body" | python3 -c "import sys, json; data=json.load(sys.stdin); print(data.get('data', {}).get('accessToken', ''))" 2>/dev/null)
        refresh_token=$(echo "$body" | python3 -c "import sys, json; data=json.load(sys.stdin); print(data.get('data', {}).get('refreshToken', ''))" 2>/dev/null)
        
        if [ -n "$access_token" ]; then
            echo "$access_token" > /tmp/access_token.txt
            echo "Access Token已保存"
        fi
        
        if [ -n "$refresh_token" ]; then
            echo "$refresh_token" > /tmp/refresh_token.txt
            echo "Refresh Token已保存"
        fi
    else
        echo -e "${YELLOW}⚠️ 用户登录失败 (HTTP $http_code)${NC}"
        echo "响应: $body"
        echo "注意: 如果用户不存在，这是正常的测试结果"
    fi
    
    echo ""
}

# 测试token刷新
test_refresh_token() {
    echo -e "${CYAN}9. 测试token刷新${NC}"
    
    refresh_token=""
    if [ -f "/tmp/refresh_token.txt" ]; then
        refresh_token=$(cat /tmp/refresh_token.txt)
    fi
    
    if [ -z "$refresh_token" ]; then
        echo -e "${YELLOW}⚠️ 没有可用的refresh token，跳过测试${NC}"
        echo ""
        return
    fi
    
    response=$(curl -s -w "HTTP_CODE:%{http_code}" \
        -X POST \
        "$BASE_URL/api/login/refresh?refreshToken=$refresh_token")
    
    http_code=$(echo "$response" | grep "HTTP_CODE:" | cut -d: -f2)
    body=$(echo "$response" | sed '/HTTP_CODE:/d')
    
    if [ "$http_code" = "200" ]; then
        echo -e "${GREEN}✅ token刷新成功${NC}"
        echo "$body" | python3 -m json.tool 2>/dev/null | head -15
    else
        echo -e "${YELLOW}⚠️ token刷新失败 (HTTP $http_code)${NC}"
        echo "响应: $body"
    fi
    
    echo ""
}

# 测试用户登出
test_user_logout() {
    echo -e "${CYAN}10. 测试用户登出${NC}"
    
    access_token=""
    if [ -f "/tmp/access_token.txt" ]; then
        access_token=$(cat /tmp/access_token.txt)
    fi
    
    if [ -z "$access_token" ]; then
        echo -e "${YELLOW}⚠️ 没有可用的access token，跳过测试${NC}"
        echo ""
        return
    fi
    
    response=$(curl -s -w "HTTP_CODE:%{http_code}" \
        -X POST \
        -H "Authorization: Bearer $access_token" \
        "$BASE_URL/api/login/logout")
    
    http_code=$(echo "$response" | grep "HTTP_CODE:" | cut -d: -f2)
    body=$(echo "$response" | sed '/HTTP_CODE:/d')
    
    if [ "$http_code" = "200" ]; then
        echo -e "${GREEN}✅ 用户登出成功${NC}"
        echo "$body" | python3 -m json.tool 2>/dev/null
    else
        echo -e "${YELLOW}⚠️ 用户登出失败 (HTTP $http_code)${NC}"
        echo "响应: $body"
    fi
    
    echo ""
}

# 生成测试报告
generate_test_report() {
    echo -e "${BLUE}生成测试报告...${NC}"
    
    report_file="saas_login_test_report.txt"
    
    cat > "$report_file" << EOF
# SaaS登录接口测试报告
生成时间: $(date)
测试基础URL: $BASE_URL

## 测试接口列表
1. GET  /api/login/webSiteModel - 获取网站模板数据
2. GET  /api/login/notLoggedIn - 获取未登录用户数据
3. GET  /api/login/verifyCode - 获取图形验证码
4. POST /api/login/verifyCode - 发送验证码
5. POST /api/login/phoneVerifyCode - 发送手机验证码
6. POST /api/login/register - 用户注册
7. POST /api/login/game - 用户登录
8. POST /api/login/refresh - 刷新token
9. POST /api/login/logout - 用户登出

## 接口功能说明
- 所有接口都基于RESTful设计
- 支持JSON格式的请求和响应
- 包含完整的参数验证和异常处理
- 实现了token认证机制
- 支持图形验证码和短信验证码

## 技术特性
- Spring Boot 3.2.0
- Spring Cloud Gateway路由
- MongoDB数据存储
- Redis缓存验证码
- JWT token认证
- 参数校验和异常处理

## 安全特性
- 密码加密存储
- 验证码防刷机制
- token过期管理
- IP地址记录
- 登录设备追踪
EOF
    
    echo -e "${GREEN}✅ 测试报告已生成: $report_file${NC}"
}

# 清理临时文件
cleanup_temp_files() {
    rm -f /tmp/verify_code_key.txt
    rm -f /tmp/test_username.txt
    rm -f /tmp/test_password.txt
    rm -f /tmp/access_token.txt
    rm -f /tmp/refresh_token.txt
}

# 主函数
main() {
    echo -e "${CYAN}开始SaaS登录接口测试...${NC}"
    echo ""
    
    # 清理之前的临时文件
    cleanup_temp_files
    
    # 执行测试
    test_service_health
    test_website_model
    test_not_logged_in_data
    test_image_verify_code
    test_email_verify_code
    test_phone_verify_code
    test_user_register
    test_user_login
    test_refresh_token
    test_user_logout
    
    # 生成报告
    generate_test_report
    
    # 清理临时文件
    cleanup_temp_files
    
    echo ""
    echo -e "${GREEN}🎉 SaaS登录接口测试完成！${NC}"
    echo ""
    echo -e "${BLUE}测试总结:${NC}"
    echo "• 共测试10个接口功能"
    echo "• 涵盖登录、注册、验证码等核心功能"
    echo "• 包含token认证和刷新机制"
    echo "• 支持多种验证方式"
    echo ""
    echo -e "${BLUE}接口地址:${NC}"
    echo "• 网关访问: http://localhost:8080/user/api/login/*"
    echo "• 直接访问: http://localhost:8081/api/login/*"
}

# 运行主函数
main
