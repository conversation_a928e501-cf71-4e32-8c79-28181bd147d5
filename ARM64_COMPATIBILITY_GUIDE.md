# ARM64 架构兼容性指南

## 问题描述

在Apple Silicon (M1/M2/M3) Mac上运行Docker时，可能遇到以下错误：
```
no matching manifest for linux/arm64/v8 in the manifest list entries: no match for
```

这是因为某些Docker镜像不支持ARM64架构。

## ✅ 解决方案

### 1. **自动架构检测**
项目已经配置了智能启动脚本，会自动检测系统架构并选择合适的配置：

```bash
# 启动基础设施（自动检测架构）
./start-infrastructure.sh
```

### 2. **手动选择配置文件**

#### ARM64架构 (Apple Silicon Mac)
```bash
# 使用ARM64兼容配置
docker-compose -f docker-compose-arm64.yml up -d
```

#### x86_64架构 (Intel Mac/Linux)
```bash
# 使用标准配置
docker-compose -f docker-compose.yml up -d
```

## 📋 配置文件对比

### docker-compose.yml (标准版)
- 包含完整的Nacos + MySQL配置
- 使用 `platform: linux/amd64` 强制使用x86镜像
- 适用于Intel架构或支持Rosetta 2的环境

### docker-compose-arm64.yml (ARM64优化版)
- 简化的Nacos配置（standalone模式，无MySQL依赖）
- 使用ARM64原生支持的镜像
- 启动更快，资源占用更少

## 🔧 各服务的ARM64兼容性

| 服务 | ARM64原生支持 | 解决方案 |
|------|---------------|----------|
| **MongoDB** | ✅ 完全支持 | 使用官方ARM64镜像 |
| **Redis** | ✅ 完全支持 | 使用官方ARM64镜像 |
| **Nacos** | ⚠️ 部分支持 | 简化配置，去除MySQL依赖 |
| **MySQL** | ✅ 完全支持 | 使用官方ARM64镜像 |

## 🚀 推荐启动流程

### 1. **检查Docker环境**
```bash
# 检查Docker是否运行
docker info

# 检查系统架构
uname -m
```

### 2. **启动基础设施**
```bash
# 自动检测并启动（推荐）
./start-infrastructure.sh

# 或手动指定配置文件
docker-compose -f docker-compose-arm64.yml up -d
```

### 3. **验证服务状态**
```bash
# 检查容器状态
docker ps

# 检查服务日志
docker-compose -f docker-compose-arm64.yml logs
```

### 4. **启动微服务**
```bash
./start-services.sh
```

## 🔍 故障排除

### 1. **Nacos启动失败**
```bash
# 查看Nacos日志
docker logs nacos

# 重启Nacos
docker restart nacos

# 等待更长时间（Nacos需要1-2分钟启动）
sleep 120
```

### 2. **端口冲突**
```bash
# 检查端口占用
lsof -i :8848
lsof -i :27017
lsof -i :6379

# 停止冲突的服务
sudo kill -9 <PID>
```

### 3. **镜像拉取失败**
```bash
# 手动拉取镜像
docker pull mongo:6.0
docker pull redis:7.0-alpine
docker pull nacos/nacos-server:v2.2.1

# 使用国内镜像源（如果网络较慢）
# 配置Docker镜像加速器
```

### 4. **强制使用x86镜像**
如果ARM64版本有问题，可以强制使用x86镜像：
```bash
# 启用Rosetta 2模拟
docker run --platform linux/amd64 <image_name>

# 或在docker-compose中指定
platform: linux/amd64
```

## 📊 性能对比

| 配置 | 启动时间 | 内存占用 | CPU占用 | 稳定性 |
|------|----------|----------|---------|--------|
| **ARM64原生** | 快 | 低 | 低 | 高 |
| **x86模拟** | 慢 | 高 | 高 | 中 |

## 💡 最佳实践

### 1. **开发环境**
- 优先使用ARM64原生配置
- 简化不必要的服务依赖
- 使用轻量级镜像

### 2. **生产环境**
- 根据实际部署架构选择配置
- 使用完整的服务配置
- 配置健康检查和监控

### 3. **团队协作**
- 提供多种架构的配置文件
- 文档化架构差异
- 统一开发环境配置

## 🔗 相关资源

- [Docker官方ARM64支持文档](https://docs.docker.com/desktop/mac/apple-silicon/)
- [Nacos Docker部署指南](https://nacos.io/zh-cn/docs/quick-start-docker.html)
- [MongoDB ARM64镜像](https://hub.docker.com/_/mongo)
- [Redis ARM64镜像](https://hub.docker.com/_/redis)

## 📝 更新日志

- **v1.0**: 添加ARM64兼容性支持
- **v1.1**: 优化自动架构检测
- **v1.2**: 简化Nacos配置，提高启动成功率

---

**注意**: 如果遇到其他架构相关问题，请查看Docker日志并根据具体错误信息进行调试。
