#!/bin/bash

echo "=== 快速停止Java服务 ==="
echo ""

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 停止游戏相关的Java服务
echo -e "${BLUE}查找游戏服务进程...${NC}"

# 查找所有game相关的Java进程
game_pids=$(pgrep -f "game-.*jar" 2>/dev/null)

if [ -n "$game_pids" ]; then
    echo -e "${YELLOW}发现运行中的游戏服务:${NC}"
    
    # 显示进程详情
    echo "$game_pids" | while read pid; do
        if [ -n "$pid" ]; then
            cmd=$(ps -p "$pid" -o command= 2>/dev/null | grep -o "game-[^/]*")
            echo "  PID $pid: $cmd"
        fi
    done
    
    echo ""
    echo -e "${BLUE}停止游戏服务...${NC}"
    
    # 优雅停止 (SIGTERM)
    echo "发送TERM信号..."
    echo "$game_pids" | xargs kill -TERM 2>/dev/null
    
    # 等待3秒
    sleep 3
    
    # 检查是否还有进程运行
    remaining_pids=$(pgrep -f "game-.*jar" 2>/dev/null)
    
    if [ -n "$remaining_pids" ]; then
        echo "强制停止剩余进程..."
        echo "$remaining_pids" | xargs kill -KILL 2>/dev/null
        sleep 1
    fi
    
    # 最终检查
    final_pids=$(pgrep -f "game-.*jar" 2>/dev/null)
    
    if [ -z "$final_pids" ]; then
        echo -e "${GREEN}✅ 所有游戏服务已停止${NC}"
    else
        echo -e "${RED}❌ 部分服务停止失败，剩余PID: $final_pids${NC}"
    fi
else
    echo -e "${YELLOW}⚠️ 没有发现运行中的游戏服务${NC}"
fi

echo ""

# 检查最终状态
echo -e "${BLUE}最终状态检查:${NC}"

services=(
    "game-gateway:网关服务"
    "game-user:用户服务"
    "game-hall:大厅服务"
    "game-agentGame:代理游戏服务"
    "game-activity:活动服务"
    "game-account:账户服务"
)

stopped_count=0

for service in "${services[@]}"; do
    IFS=':' read -r service_name service_desc <<< "$service"
    
    if pgrep -f "$service_name.*jar" > /dev/null 2>&1; then
        echo -e "${RED}  ❌ $service_desc 仍在运行${NC}"
    else
        echo -e "${GREEN}  ✅ $service_desc 已停止${NC}"
        ((stopped_count++))
    fi
done

echo ""
echo "停止状态: $stopped_count/${#services[@]} 服务已停止"

# 检查端口占用
echo ""
echo -e "${BLUE}检查端口占用:${NC}"

ports=(8080 8081 8082 8083 8084 8085)
occupied_count=0

for port in "${ports[@]}"; do
    if lsof -i :$port > /dev/null 2>&1; then
        echo -e "${YELLOW}  ⚠️ 端口 $port 仍被占用${NC}"
        ((occupied_count++))
    else
        echo -e "${GREEN}  ✅ 端口 $port 已释放${NC}"
    fi
done

echo ""

if [ $stopped_count -eq ${#services[@]} ] && [ $occupied_count -eq 0 ]; then
    echo -e "${GREEN}🎉 所有Java服务已完全停止！${NC}"
else
    echo -e "${YELLOW}⚠️ 部分服务或端口可能仍在使用中${NC}"
    
    if [ $stopped_count -lt ${#services[@]} ]; then
        echo "  - 仍有 $((${#services[@]} - stopped_count)) 个服务运行"
    fi
    
    if [ $occupied_count -gt 0 ]; then
        echo "  - 仍有 $occupied_count 个端口被占用"
    fi
    
    echo ""
    echo -e "${BLUE}如需强制清理:${NC}"
    echo "• 强制停止所有Java进程: pkill -f java"
    echo "• 清理特定端口: lsof -ti :8080 | xargs kill"
    echo "• 使用完整脚本: ./stop-java-services.sh"
fi

echo ""
echo -e "${BLUE}重新启动服务:${NC}"
echo "• 启动基础设施: ./start-infrastructure.sh"
echo "• 启动微服务: ./start-services.sh"
