server:
  port: 8081

spring:
  application:
    name: game-user
  profiles:
    active: dev
  config:
    import:
      - optional:nacos:game-user.yml
  cloud:
    nacos:
      discovery:
        server-addr: localhost:8848
        namespace: dev
      config:
        server-addr: localhost:8848
        namespace: dev
        file-extension: yml
        group: DEFAULT_GROUP
  data:
    mongodb:
      host: localhost
      port: 27017
      database: wingame_user
      username: admin
      password: admin123
      authentication-database: admin
    redis:
      host: localhost
      port: 6379
      password: redis123
      database: 0
      timeout: 3000ms
      lettuce:
        pool:
          max-active: 8
          max-wait: -1ms
          max-idle: 8
          min-idle: 0

# Actuator配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: always

logging:
  level:
    com.wingame.user: debug
    org.springframework.data.mongodb: debug

# Swagger/OpenAPI配置
springdoc:
  api-docs:
    path: /v3/api-docs
  swagger-ui:
    path: /swagger-ui.html
    enabled: true
  show-actuator: true
