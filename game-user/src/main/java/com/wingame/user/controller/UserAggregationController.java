package com.wingame.user.controller;

import com.wingame.user.common.Result;
import com.wingame.user.dto.UserProfileDTO;
import com.wingame.user.service.UserAggregationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * 用户信息聚合控制器
 * 演示网关层聚合，通过Feign调用账户服务
 */
@Slf4j
@RestController
@RequestMapping("/api/user/aggregation")
@RequiredArgsConstructor
@Tag(name = "用户信息聚合", description = "用户信息聚合API - 演示服务间Feign调用")
public class UserAggregationController {
    
    private final UserAggregationService userAggregationService;
    
    /**
     * 获取用户完整档案信息（聚合用户信息、账户余额、统计信息）
     * 
     * @param userId 用户ID
     * @return 用户完整档案信息
     */
    @GetMapping("/profile/{userId}")
    @Operation(summary = "获取用户完整档案", description = "聚合用户基本信息、账户余额和统计信息")
    public Result<UserProfileDTO> getUserProfile(
            @Parameter(description = "用户ID", required = true)
            @PathVariable String userId) {
        
        log.info("获取用户完整档案信息，用户ID: {}", userId);
        
        try {
            UserProfileDTO profile = userAggregationService.getUserProfile(userId);
            
            log.info("用户档案信息获取成功，用户ID: {}, 聚合状态: {}", 
                    userId, profile.getAggregationStatus());
            
            return Result.success("获取用户档案成功", profile);
            
        } catch (Exception e) {
            log.error("获取用户档案失败，用户ID: {}", userId, e);
            return Result.failed("获取用户档案失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取用户信息和账户余额
     * 
     * @param userId 用户ID
     * @return 用户信息和余额信息
     */
    @GetMapping("/profile/{userId}/balance")
    @Operation(summary = "获取用户信息和余额", description = "聚合用户基本信息和账户余额")
    public Result<UserProfileDTO> getUserWithBalance(
            @Parameter(description = "用户ID", required = true)
            @PathVariable String userId) {
        
        log.info("获取用户信息和余额，用户ID: {}", userId);
        
        try {
            UserProfileDTO profile = userAggregationService.getUserWithBalance(userId);
            
            log.info("用户信息和余额获取成功，用户ID: {}", userId);
            
            return Result.success("获取用户信息和余额成功", profile);
            
        } catch (Exception e) {
            log.error("获取用户信息和余额失败，用户ID: {}", userId, e);
            return Result.failed("获取用户信息和余额失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取用户信息和账户统计
     * 
     * @param userId 用户ID
     * @return 用户信息和统计信息
     */
    @GetMapping("/profile/{userId}/statistics")
    @Operation(summary = "获取用户信息和统计", description = "聚合用户基本信息和账户统计信息")
    public Result<UserProfileDTO> getUserWithStatistics(
            @Parameter(description = "用户ID", required = true)
            @PathVariable String userId) {
        
        log.info("获取用户信息和统计，用户ID: {}", userId);
        
        try {
            UserProfileDTO profile = userAggregationService.getUserWithStatistics(userId);
            
            log.info("用户信息和统计获取成功，用户ID: {}", userId);
            
            return Result.success("获取用户信息和统计成功", profile);
            
        } catch (Exception e) {
            log.error("获取用户信息和统计失败，用户ID: {}", userId, e);
            return Result.failed("获取用户信息和统计失败: " + e.getMessage());
        }
    }
    
    /**
     * 批量获取用户档案信息
     * 
     * @param userIds 用户ID列表（逗号分隔）
     * @return 用户档案信息列表
     */
    @GetMapping("/profiles")
    @Operation(summary = "批量获取用户档案", description = "批量聚合多个用户的档案信息")
    public Result<Object> getBatchUserProfiles(
            @Parameter(description = "用户ID列表，逗号分隔", required = true)
            @RequestParam String userIds) {
        
        log.info("批量获取用户档案信息，用户IDs: {}", userIds);
        
        try {
            String[] userIdArray = userIds.split(",");
            
            if (userIdArray.length > 10) {
                return Result.failed("批量查询用户数量不能超过10个");
            }
            
            java.util.List<UserProfileDTO> profiles = new java.util.ArrayList<>();
            
            for (String userId : userIdArray) {
                userId = userId.trim();
                if (!userId.isEmpty()) {
                    try {
                        UserProfileDTO profile = userAggregationService.getUserProfile(userId);
                        profiles.add(profile);
                    } catch (Exception e) {
                        log.error("获取用户档案失败，用户ID: {}", userId, e);
                        // 继续处理其他用户
                    }
                }
            }
            
            log.info("批量获取用户档案完成，成功获取: {}/{}", profiles.size(), userIdArray.length);
            
            return Result.success("批量获取用户档案成功", profiles);
            
        } catch (Exception e) {
            log.error("批量获取用户档案失败，用户IDs: {}", userIds, e);
            return Result.failed("批量获取用户档案失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取聚合服务健康状态
     * 
     * @return 健康状态信息
     */
    @GetMapping("/health")
    @Operation(summary = "聚合服务健康检查", description = "检查用户聚合服务和依赖服务的健康状态")
    public Result<Object> getAggregationHealth() {
        
        log.info("检查聚合服务健康状态");
        
        try {
            // 测试调用账户服务
            String testUserId = "health_check_user";
            UserProfileDTO testProfile = userAggregationService.getUserWithBalance(testUserId);
            
            Object healthInfo = new Object() {
                public final String service = "user-aggregation-service";
                public final String status = "UP";
                public final java.time.LocalDateTime checkTime = java.time.LocalDateTime.now();
                public final boolean accountServiceAvailable = 
                    testProfile.getBalance() != null && 
                    !"SERVICE_UNAVAILABLE".equals(testProfile.getBalance().getStatus());
                public final String version = "1.0.0";
                public final String description = "用户信息聚合服务 - 演示Feign调用";
            };
            
            return Result.success("聚合服务健康检查通过", healthInfo);
            
        } catch (Exception e) {
            log.error("聚合服务健康检查失败", e);
            
            Object healthInfo = new Object() {
                public final String service = "user-aggregation-service";
                public final String status = "DOWN";
                public final java.time.LocalDateTime checkTime = java.time.LocalDateTime.now();
                public final String error = e.getMessage();
                public final String version = "1.0.0";
            };
            
            Result<Object> result = Result.failed("聚合服务健康检查失败");
            result.setData(healthInfo);
            return result;
        }
    }
}
