package com.wingame.user.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 用户档案聚合信息DTO
 */
@Data
public class UserProfileDTO {
    
    // 用户基本信息
    private String userId;
    private String username;
    private String nickname;
    private String email;
    private String phone;
    private String avatar;
    private String status;
    private LocalDateTime registerTime;
    private LocalDateTime lastLoginTime;
    
    // 账户余额信息
    private AccountBalanceInfo balance;
    
    // 账户统计信息
    private AccountStatisticsInfo statistics;
    
    // 聚合状态信息
    private String aggregationStatus;
    private LocalDateTime aggregationTime;
    
    @Data
    public static class AccountBalanceInfo {
        private BigDecimal totalBalance;
        private BigDecimal availableBalance;
        private BigDecimal frozenBalance;
        private BigDecimal coins;
        private BigDecimal points;
        private LocalDateTime lastUpdated;
        private String status;
    }
    
    @Data
    public static class AccountStatisticsInfo {
        private BigDecimal totalRecharge;
        private BigDecimal totalWithdraw;
        private BigDecimal totalGameWin;
        private BigDecimal totalGameLose;
        private BigDecimal netProfit;
        private Integer rechargeCount;
        private Integer withdrawCount;
        private Integer gameCount;
        private LocalDateTime firstTransactionTime;
        private LocalDateTime lastTransactionTime;
        private String status;
    }
}
