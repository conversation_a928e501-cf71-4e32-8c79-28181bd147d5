package com.wingame.user.feign;

import com.wingame.user.common.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

/**
 * 账户服务Feign客户端
 */
@FeignClient(name = "game-account", fallback = AccountFeignClientFallback.class)
public interface AccountFeignClient {
    
    /**
     * 获取用户账户余额
     */
    @GetMapping("/api/account/{userId}/balance")
    Result<Object> getBalance(@PathVariable("userId") String userId);
    
    /**
     * 获取用户账户统计
     */
    @GetMapping("/api/account/{userId}/statistics")
    Result<Object> getStatistics(@PathVariable("userId") String userId);
}
