package com.wingame.user.feign;

import com.wingame.user.common.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 账户服务Feign客户端降级处理
 */
@Slf4j
@Component
public class AccountFeignClientFallback implements AccountFeignClient {
    
    @Override
    public Result<Object> getBalance(String userId) {
        log.warn("账户服务调用失败，返回默认余额信息，userId: {}", userId);
        
        // 使用Map创建默认余额信息
        Map<String, Object> defaultBalance = new HashMap<>();
        defaultBalance.put("userId", userId);
        defaultBalance.put("totalBalance", BigDecimal.ZERO);
        defaultBalance.put("availableBalance", BigDecimal.ZERO);
        defaultBalance.put("frozenBalance", BigDecimal.ZERO);
        defaultBalance.put("coins", BigDecimal.ZERO);
        defaultBalance.put("points", BigDecimal.ZERO);
        defaultBalance.put("lastUpdated", LocalDateTime.now());
        defaultBalance.put("status", "SERVICE_UNAVAILABLE");
        
        return Result.success("账户服务暂时不可用，返回默认数据", defaultBalance);
    }
    
    @Override
    public Result<Object> getStatistics(String userId) {
        log.warn("账户统计服务调用失败，返回默认统计信息，userId: {}", userId);
        
        // 使用Map创建默认统计信息
        Map<String, Object> defaultStats = new HashMap<>();
        defaultStats.put("userId", userId);
        defaultStats.put("totalRecharge", BigDecimal.ZERO);
        defaultStats.put("totalWithdraw", BigDecimal.ZERO);
        defaultStats.put("totalGameWin", BigDecimal.ZERO);
        defaultStats.put("totalGameLose", BigDecimal.ZERO);
        defaultStats.put("netProfit", BigDecimal.ZERO);
        defaultStats.put("rechargeCount", 0);
        defaultStats.put("withdrawCount", 0);
        defaultStats.put("gameCount", 0);
        defaultStats.put("status", "SERVICE_UNAVAILABLE");
        
        return Result.success("账户统计服务暂时不可用，返回默认数据", defaultStats);
    }
}
