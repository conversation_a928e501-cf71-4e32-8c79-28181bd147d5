package com.wingame.user.feign;

import com.wingame.user.common.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 账户服务Feign客户端降级处理
 */
@Slf4j
@Component
public class AccountFeignClientFallback implements AccountFeignClient {
    
    @Override
    public Result<Object> getBalance(String userId) {
        log.warn("账户服务调用失败，返回默认余额信息，userId: {}", userId);
        
        // 返回默认余额信息
        Object defaultBalance = new Object() {
            public final String userId = userId;
            public final BigDecimal totalBalance = BigDecimal.ZERO;
            public final BigDecimal availableBalance = BigDecimal.ZERO;
            public final BigDecimal frozenBalance = BigDecimal.ZERO;
            public final BigDecimal coins = BigDecimal.ZERO;
            public final BigDecimal points = BigDecimal.ZERO;
            public final LocalDateTime lastUpdated = LocalDateTime.now();
            public final String status = "SERVICE_UNAVAILABLE";
        };
        
        return Result.success("账户服务暂时不可用，返回默认数据", defaultBalance);
    }
    
    @Override
    public Result<Object> getStatistics(String userId) {
        log.warn("账户统计服务调用失败，返回默认统计信息，userId: {}", userId);
        
        // 返回默认统计信息
        Object defaultStats = new Object() {
            public final String userId = userId;
            public final BigDecimal totalRecharge = BigDecimal.ZERO;
            public final BigDecimal totalWithdraw = BigDecimal.ZERO;
            public final BigDecimal totalGameWin = BigDecimal.ZERO;
            public final BigDecimal totalGameLose = BigDecimal.ZERO;
            public final BigDecimal netProfit = BigDecimal.ZERO;
            public final int rechargeCount = 0;
            public final int withdrawCount = 0;
            public final int gameCount = 0;
            public final String status = "SERVICE_UNAVAILABLE";
        };
        
        return Result.success("账户统计服务暂时不可用，返回默认数据", defaultStats);
    }
}
