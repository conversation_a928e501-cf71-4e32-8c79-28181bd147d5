package com.wingame.user.service;

import com.wingame.user.dto.UserProfileDTO;

/**
 * 用户聚合服务接口
 */
public interface UserAggregationService {
    
    /**
     * 获取用户完整档案信息（聚合用户信息和账户信息）
     */
    UserProfileDTO getUserProfile(String userId);
    
    /**
     * 获取用户基本信息和余额信息
     */
    UserProfileDTO getUserWithBalance(String userId);
    
    /**
     * 获取用户基本信息和统计信息
     */
    UserProfileDTO getUserWithStatistics(String userId);
}
