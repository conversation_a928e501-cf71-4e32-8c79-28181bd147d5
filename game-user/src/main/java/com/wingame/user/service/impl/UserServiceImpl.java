package com.wingame.user.service.impl;

import com.wingame.common.exception.BusinessException;
import com.wingame.common.result.ResultCode;
import com.wingame.user.dto.UserLoginDTO;
import com.wingame.user.dto.UserRegisterDTO;
import com.wingame.user.entity.User;
import com.wingame.user.repository.UserRepository;
import com.wingame.user.service.UserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.DigestUtils;

import java.time.LocalDateTime;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * 用户服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserServiceImpl implements UserService {
    
    private final UserRepository userRepository;
    private final RedisTemplate<String, Object> redisTemplate;
    
    private static final String USER_CACHE_PREFIX = "user:";
    private static final long USER_CACHE_EXPIRE = 30; // 30分钟
    
    @Override
    public User register(UserRegisterDTO registerDTO) {
        // 验证密码确认
        if (!registerDTO.getPassword().equals(registerDTO.getConfirmPassword())) {
            throw new BusinessException("两次输入的密码不一致");
        }
        
        // 检查用户名是否已存在
        if (userRepository.existsByUsername(registerDTO.getUsername())) {
            throw new BusinessException(ResultCode.USER_ALREADY_EXISTS);
        }
        
        // 检查邮箱是否已存在
        if (registerDTO.getEmail() != null && userRepository.existsByEmail(registerDTO.getEmail())) {
            throw new BusinessException("邮箱已被注册");
        }
        
        // 检查手机号是否已存在
        if (registerDTO.getPhone() != null && userRepository.existsByPhone(registerDTO.getPhone())) {
            throw new BusinessException("手机号已被注册");
        }
        
        // 创建用户
        User user = new User();
        user.setUsername(registerDTO.getUsername());
        user.setPassword(encryptPassword(registerDTO.getPassword()));
        user.setNickname(registerDTO.getNickname());
        user.setEmail(registerDTO.getEmail());
        user.setPhone(registerDTO.getPhone());
        user.setStatus(1); // 启用状态
        user.setLevel(1); // 初始等级
        user.setExperience(0L); // 初始经验
        user.setCreateTime(LocalDateTime.now());
        user.setUpdateTime(LocalDateTime.now());
        
        User savedUser = userRepository.save(user);
        log.info("用户注册成功: {}", savedUser.getUsername());
        
        return savedUser;
    }
    
    @Override
    public User login(UserLoginDTO loginDTO) {
        Optional<User> userOpt = userRepository.findByUsername(loginDTO.getUsername());
        if (userOpt.isEmpty()) {
            throw new BusinessException(ResultCode.USER_NOT_FOUND);
        }
        
        User user = userOpt.get();
        
        // 检查账户状态
        if (user.getStatus() == 0) {
            throw new BusinessException(ResultCode.USER_ACCOUNT_DISABLED);
        }
        
        // 验证密码
        if (!encryptPassword(loginDTO.getPassword()).equals(user.getPassword())) {
            throw new BusinessException(ResultCode.USER_PASSWORD_ERROR);
        }
        
        // 缓存用户信息
        cacheUser(user);
        
        log.info("用户登录成功: {}", user.getUsername());
        return user;
    }
    
    @Override
    public User getUserById(String userId) {
        // 先从缓存获取
        User cachedUser = getCachedUser(userId);
        if (cachedUser != null) {
            return cachedUser;
        }
        
        // 从数据库获取
        Optional<User> userOpt = userRepository.findById(userId);
        if (userOpt.isEmpty()) {
            throw new BusinessException(ResultCode.USER_NOT_FOUND);
        }
        
        User user = userOpt.get();
        cacheUser(user);
        return user;
    }
    
    @Override
    public User getUserByUsername(String username) {
        Optional<User> userOpt = userRepository.findByUsername(username);
        if (userOpt.isEmpty()) {
            throw new BusinessException(ResultCode.USER_NOT_FOUND);
        }
        return userOpt.get();
    }
    
    @Override
    public User updateUser(User user) {
        user.setUpdateTime(LocalDateTime.now());
        User updatedUser = userRepository.save(user);
        
        // 更新缓存
        cacheUser(updatedUser);
        
        return updatedUser;
    }
    
    @Override
    public void updateLoginInfo(String userId, String loginIp) {
        Optional<User> userOpt = userRepository.findById(userId);
        if (userOpt.isPresent()) {
            User user = userOpt.get();
            user.setLastLoginTime(LocalDateTime.now());
            user.setLastLoginIp(loginIp);
            updateUser(user);
        }
    }
    
    @Override
    public boolean existsByUsername(String username) {
        return userRepository.existsByUsername(username);
    }
    
    @Override
    public boolean existsByEmail(String email) {
        return userRepository.existsByEmail(email);
    }
    
    @Override
    public boolean existsByPhone(String phone) {
        return userRepository.existsByPhone(phone);
    }
    
    /**
     * 密码加密
     */
    private String encryptPassword(String password) {
        return DigestUtils.md5DigestAsHex((password + "wingame_salt").getBytes());
    }
    
    /**
     * 缓存用户信息
     */
    private void cacheUser(User user) {
        String key = USER_CACHE_PREFIX + user.getId();
        redisTemplate.opsForValue().set(key, user, USER_CACHE_EXPIRE, TimeUnit.MINUTES);
    }
    
    /**
     * 获取缓存的用户信息
     */
    private User getCachedUser(String userId) {
        String key = USER_CACHE_PREFIX + userId;
        return (User) redisTemplate.opsForValue().get(key);
    }
}
