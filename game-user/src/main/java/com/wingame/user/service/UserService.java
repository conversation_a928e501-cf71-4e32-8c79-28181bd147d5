package com.wingame.user.service;

import com.wingame.user.entity.User;
import com.wingame.user.dto.UserRegisterDTO;
import com.wingame.user.dto.UserLoginDTO;

/**
 * 用户服务接口
 */
public interface UserService {

    /**
     * 用户注册
     */
    User register(UserRegisterDTO registerDTO);

    /**
     * 用户登录
     */
    User login(UserLoginDTO loginDTO);

    /**
     * 根据ID获取用户信息
     */
    User getUserById(String userId);

    /**
     * 根据用户名获取用户信息
     */
    User getUserByUsername(String username);

    /**
     * 更新用户信息
     */
    User updateUser(User user);

    /**
     * 更新用户登录信息
     */
    void updateLoginInfo(String userId, String loginIp);

    /**
     * 检查用户名是否存在
     */
    boolean existsByUsername(String username);

    /**
     * 检查邮箱是否存在
     */
    boolean existsByEmail(String email);

    /**
     * 检查手机号是否存在
     */
    boolean existsByPhone(String phone);
}
