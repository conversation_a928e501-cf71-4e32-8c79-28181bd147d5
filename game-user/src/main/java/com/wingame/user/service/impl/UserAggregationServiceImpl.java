package com.wingame.user.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.wingame.user.common.Result;
import com.wingame.user.dto.UserProfileDTO;
import com.wingame.user.feign.AccountFeignClient;
import com.wingame.user.service.UserAggregationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * 用户聚合服务实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserAggregationServiceImpl implements UserAggregationService {
    
    private final AccountFeignClient accountFeignClient;
    private final ObjectMapper objectMapper;
    
    @Override
    public UserProfileDTO getUserProfile(String userId) {
        log.info("开始聚合用户档案信息，userId: {}", userId);
        
        UserProfileDTO profile = new UserProfileDTO();
        profile.setUserId(userId);
        profile.setAggregationTime(LocalDateTime.now());
        
        try {
            // 1. 获取用户基本信息（模拟数据）
            setUserBasicInfo(profile, userId);
            
            // 2. 并行调用账户服务获取余额和统计信息
            CompletableFuture<UserProfileDTO.AccountBalanceInfo> balanceFuture = 
                CompletableFuture.supplyAsync(() -> getAccountBalance(userId));
            
            CompletableFuture<UserProfileDTO.AccountStatisticsInfo> statsFuture = 
                CompletableFuture.supplyAsync(() -> getAccountStatistics(userId));
            
            // 3. 等待所有异步调用完成（设置超时时间）
            UserProfileDTO.AccountBalanceInfo balance = balanceFuture.get(3, TimeUnit.SECONDS);
            UserProfileDTO.AccountStatisticsInfo statistics = statsFuture.get(3, TimeUnit.SECONDS);
            
            profile.setBalance(balance);
            profile.setStatistics(statistics);
            profile.setAggregationStatus("SUCCESS");
            
            log.info("用户档案信息聚合成功，userId: {}", userId);
            
        } catch (Exception e) {
            log.error("用户档案信息聚合失败，userId: {}, error: {}", userId, e.getMessage());
            profile.setAggregationStatus("PARTIAL_FAILURE");
            
            // 设置默认值
            if (profile.getBalance() == null) {
                profile.setBalance(getDefaultBalance(userId));
            }
            if (profile.getStatistics() == null) {
                profile.setStatistics(getDefaultStatistics(userId));
            }
        }
        
        return profile;
    }
    
    @Override
    public UserProfileDTO getUserWithBalance(String userId) {
        log.info("获取用户信息和余额，userId: {}", userId);
        
        UserProfileDTO profile = new UserProfileDTO();
        profile.setUserId(userId);
        profile.setAggregationTime(LocalDateTime.now());
        
        // 设置用户基本信息
        setUserBasicInfo(profile, userId);
        
        // 获取账户余额
        UserProfileDTO.AccountBalanceInfo balance = getAccountBalance(userId);
        profile.setBalance(balance);
        
        profile.setAggregationStatus("SUCCESS");
        return profile;
    }
    
    @Override
    public UserProfileDTO getUserWithStatistics(String userId) {
        log.info("获取用户信息和统计，userId: {}", userId);
        
        UserProfileDTO profile = new UserProfileDTO();
        profile.setUserId(userId);
        profile.setAggregationTime(LocalDateTime.now());
        
        // 设置用户基本信息
        setUserBasicInfo(profile, userId);
        
        // 获取账户统计
        UserProfileDTO.AccountStatisticsInfo statistics = getAccountStatistics(userId);
        profile.setStatistics(statistics);
        
        profile.setAggregationStatus("SUCCESS");
        return profile;
    }
    
    /**
     * 设置用户基本信息（模拟数据）
     */
    private void setUserBasicInfo(UserProfileDTO profile, String userId) {
        profile.setUsername("user_" + userId);
        profile.setNickname("用户" + userId);
        profile.setEmail(userId + "@wingame.com");
        profile.setPhone("138****" + userId.substring(Math.max(0, userId.length() - 4)));
        profile.setAvatar("https://avatar.example.com/" + userId + ".jpg");
        profile.setStatus("ACTIVE");
        profile.setRegisterTime(LocalDateTime.now().minusDays(30));
        profile.setLastLoginTime(LocalDateTime.now().minusHours(2));
    }
    
    /**
     * 获取账户余额信息
     */
    private UserProfileDTO.AccountBalanceInfo getAccountBalance(String userId) {
        try {
            log.info("调用账户服务获取余额信息，userId: {}", userId);
            Result<Object> result = accountFeignClient.getBalance(userId);
            
            if (result != null && result.getData() != null) {
                return convertToBalanceInfo(result.getData());
            }
        } catch (Exception e) {
            log.error("调用账户服务获取余额失败，userId: {}, error: {}", userId, e.getMessage());
        }
        
        return getDefaultBalance(userId);
    }
    
    /**
     * 获取账户统计信息
     */
    private UserProfileDTO.AccountStatisticsInfo getAccountStatistics(String userId) {
        try {
            log.info("调用账户服务获取统计信息，userId: {}", userId);
            Result<Object> result = accountFeignClient.getAccountStatistics(userId);
            
            if (result != null && result.getData() != null) {
                return convertToStatisticsInfo(result.getData());
            }
        } catch (Exception e) {
            log.error("调用账户服务获取统计失败，userId: {}, error: {}", userId, e.getMessage());
        }
        
        return getDefaultStatistics(userId);
    }
    
    /**
     * 转换余额信息
     */
    private UserProfileDTO.AccountBalanceInfo convertToBalanceInfo(Object data) {
        try {
            Map<String, Object> dataMap = objectMapper.convertValue(data, Map.class);
            
            UserProfileDTO.AccountBalanceInfo balance = new UserProfileDTO.AccountBalanceInfo();
            balance.setTotalBalance(new BigDecimal(dataMap.getOrDefault("totalBalance", "0").toString()));
            balance.setAvailableBalance(new BigDecimal(dataMap.getOrDefault("availableBalance", "0").toString()));
            balance.setFrozenBalance(new BigDecimal(dataMap.getOrDefault("frozenBalance", "0").toString()));
            balance.setCoins(new BigDecimal(dataMap.getOrDefault("coins", "0").toString()));
            balance.setPoints(new BigDecimal(dataMap.getOrDefault("points", "0").toString()));
            balance.setStatus("ACTIVE");
            balance.setLastUpdated(LocalDateTime.now());
            
            return balance;
        } catch (Exception e) {
            log.error("转换余额信息失败: {}", e.getMessage());
            return getDefaultBalance(null);
        }
    }
    
    /**
     * 转换统计信息
     */
    private UserProfileDTO.AccountStatisticsInfo convertToStatisticsInfo(Object data) {
        try {
            Map<String, Object> dataMap = objectMapper.convertValue(data, Map.class);
            
            UserProfileDTO.AccountStatisticsInfo stats = new UserProfileDTO.AccountStatisticsInfo();
            stats.setTotalRecharge(new BigDecimal(dataMap.getOrDefault("totalRecharge", "0").toString()));
            stats.setTotalWithdraw(new BigDecimal(dataMap.getOrDefault("totalWithdraw", "0").toString()));
            stats.setTotalGameWin(new BigDecimal(dataMap.getOrDefault("totalGameWin", "0").toString()));
            stats.setTotalGameLose(new BigDecimal(dataMap.getOrDefault("totalGameLose", "0").toString()));
            stats.setNetProfit(new BigDecimal(dataMap.getOrDefault("netProfit", "0").toString()));
            stats.setRechargeCount((Integer) dataMap.getOrDefault("rechargeCount", 0));
            stats.setWithdrawCount((Integer) dataMap.getOrDefault("withdrawCount", 0));
            stats.setGameCount((Integer) dataMap.getOrDefault("gameCount", 0));
            stats.setStatus("ACTIVE");
            
            return stats;
        } catch (Exception e) {
            log.error("转换统计信息失败: {}", e.getMessage());
            return getDefaultStatistics(null);
        }
    }
    
    /**
     * 获取默认余额信息
     */
    private UserProfileDTO.AccountBalanceInfo getDefaultBalance(String userId) {
        UserProfileDTO.AccountBalanceInfo balance = new UserProfileDTO.AccountBalanceInfo();
        balance.setTotalBalance(BigDecimal.ZERO);
        balance.setAvailableBalance(BigDecimal.ZERO);
        balance.setFrozenBalance(BigDecimal.ZERO);
        balance.setCoins(BigDecimal.ZERO);
        balance.setPoints(BigDecimal.ZERO);
        balance.setStatus("SERVICE_UNAVAILABLE");
        balance.setLastUpdated(LocalDateTime.now());
        return balance;
    }
    
    /**
     * 获取默认统计信息
     */
    private UserProfileDTO.AccountStatisticsInfo getDefaultStatistics(String userId) {
        UserProfileDTO.AccountStatisticsInfo stats = new UserProfileDTO.AccountStatisticsInfo();
        stats.setTotalRecharge(BigDecimal.ZERO);
        stats.setTotalWithdraw(BigDecimal.ZERO);
        stats.setTotalGameWin(BigDecimal.ZERO);
        stats.setTotalGameLose(BigDecimal.ZERO);
        stats.setNetProfit(BigDecimal.ZERO);
        stats.setRechargeCount(0);
        stats.setWithdrawCount(0);
        stats.setGameCount(0);
        stats.setStatus("SERVICE_UNAVAILABLE");
        return stats;
    }
}
