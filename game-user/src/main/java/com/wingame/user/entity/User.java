package com.wingame.user.entity;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.time.LocalDateTime;

/**
 * 用户实体类
 */
@Data
@Document(collection = "users")
public class User {
    
    @Id
    private String id;
    
    @Field("username")
    private String username;
    
    @Field("password")
    private String password;
    
    @Field("nickname")
    private String nickname;
    
    @Field("email")
    private String email;
    
    @Field("phone")
    private String phone;
    
    @Field("avatar")
    private String avatar;
    
    @Field("status")
    private Integer status; // 0-禁用 1-启用
    
    @Field("level")
    private Integer level; // 用户等级
    
    @Field("experience")
    private Long experience; // 经验值
    
    @Field("last_login_time")
    private LocalDateTime lastLoginTime;
    
    @Field("last_login_ip")
    private String lastLoginIp;
    
    @Field("create_time")
    private LocalDateTime createTime;
    
    @Field("update_time")
    private LocalDateTime updateTime;
}
