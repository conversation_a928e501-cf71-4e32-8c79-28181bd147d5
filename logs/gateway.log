
  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/
 :: Spring Boot ::               (v2.7.14)

2025-06-24 13:41:01.941  INFO 14536 --- [           main] com.wingame.gateway.GatewayApplication   : Starting GatewayApplication v1.0-SNAPSHOT using Java 21.0.7 on WangXiaoShundeMacBook-Pro.local with PID 14536 (/Users/<USER>/wingame/saas_spring/game-gateway/target/game-gateway-1.0-SNAPSHOT.jar started by wangxiaoshun in /Users/<USER>/wingame/saas_spring/game-gateway)
2025-06-24 13:41:01.942 DEBUG 14536 --- [           main] com.wingame.gateway.GatewayApplication   : Running with Spring Boot v2.7.14, Spring v5.3.29
2025-06-24 13:41:01.942  INFO 14536 --- [           main] com.wingame.gateway.GatewayApplication   : The following 1 profile is active: "dev"
2025-06-24 13:41:01.962  WARN 14536 --- [           main] c.a.c.n.c.NacosConfigDataLoader          : [Nacos Config] config[dataId=game-gateway.yml, group=DEFAULT_GROUP] is empty
2025-06-24 13:41:02.417  INFO 14536 --- [           main] o.s.cloud.context.scope.GenericScope     : BeanFactory id=0dadcd8a-223e-3528-b7ae-a6ccaf48aa09
2025-06-24 13:41:02.485  INFO 14536 --- [           main] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-24 13:41:02.487  INFO 14536 --- [           main] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-24 13:41:02.487  INFO 14536 --- [           main] trationDelegate$BeanPostProcessorChecker : Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-24 13:41:02.761 DEBUG 14536 --- [           main] o.s.c.gateway.config.GatewayProperties   : Routes supplied from Gateway Properties: [RouteDefinition{id='game-user', predicates=[PredicateDefinition{name='Path', args={_genkey_0=/user/**}}], filters=[FilterDefinition{name='StripPrefix', args={_genkey_0=1}}], uri=lb://game-user, order=0, metadata={}}, RouteDefinition{id='game-hall', predicates=[PredicateDefinition{name='Path', args={_genkey_0=/hall/**}}], filters=[FilterDefinition{name='StripPrefix', args={_genkey_0=1}}], uri=lb://game-hall, order=0, metadata={}}, RouteDefinition{id='game-agentgame', predicates=[PredicateDefinition{name='Path', args={_genkey_0=/agentgame/**}}], filters=[FilterDefinition{name='StripPrefix', args={_genkey_0=1}}], uri=lb://game-agentgame, order=0, metadata={}}, RouteDefinition{id='game-activity', predicates=[PredicateDefinition{name='Path', args={_genkey_0=/activity/**}}], filters=[FilterDefinition{name='StripPrefix', args={_genkey_0=1}}], uri=lb://game-activity, order=0, metadata={}}, RouteDefinition{id='game-account', predicates=[PredicateDefinition{name='Path', args={_genkey_0=/account/**}}], filters=[FilterDefinition{name='StripPrefix', args={_genkey_0=1}}], uri=lb://game-account, order=0, metadata={}}]
2025-06-24 13:41:02.914  INFO 14536 --- [           main] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [After]
2025-06-24 13:41:02.914  INFO 14536 --- [           main] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Before]
2025-06-24 13:41:02.914  INFO 14536 --- [           main] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Between]
2025-06-24 13:41:02.914  INFO 14536 --- [           main] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Cookie]
2025-06-24 13:41:02.914  INFO 14536 --- [           main] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Header]
2025-06-24 13:41:02.914  INFO 14536 --- [           main] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Host]
2025-06-24 13:41:02.914  INFO 14536 --- [           main] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Method]
2025-06-24 13:41:02.914  INFO 14536 --- [           main] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Path]
2025-06-24 13:41:02.914  INFO 14536 --- [           main] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Query]
2025-06-24 13:41:02.914  INFO 14536 --- [           main] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [ReadBody]
2025-06-24 13:41:02.914  INFO 14536 --- [           main] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [RemoteAddr]
2025-06-24 13:41:02.914  INFO 14536 --- [           main] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [XForwardedRemoteAddr]
2025-06-24 13:41:02.914  INFO 14536 --- [           main] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Weight]
2025-06-24 13:41:02.914  INFO 14536 --- [           main] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [CloudFoundryRouteService]
2025-06-24 13:41:02.930  INFO 14536 --- [           main] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 4 endpoint(s) beneath base path '/actuator'
2025-06-24 13:41:02.961  INFO 14536 --- [oundedElastic-1] c.a.n.p.a.s.c.ClientAuthPluginManager    : [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-06-24 13:41:02.961  INFO 14536 --- [oundedElastic-1] c.a.n.p.a.s.c.ClientAuthPluginManager    : [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-06-24 13:41:03.214 DEBUG 14536 --- [oundedElastic-4] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-account applying {pattern=/game-account/**} to Path
2025-06-24 13:41:03.221 DEBUG 14536 --- [oundedElastic-3] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-account applying {pattern=/game-account/**} to Path
