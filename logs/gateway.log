
  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/
 :: Spring Boot ::               (v2.7.14)

2025-06-24 14:56:55.246  INFO 21607 --- [           main] com.wingame.gateway.GatewayApplication   : Starting GatewayApplication v1.0-SNAPSHOT using Java 21.0.7 on WangXiaoShundeMacBook-Pro.local with PID 21607 (/Users/<USER>/wingame/saas_spring/game-gateway/target/game-gateway-1.0-SNAPSHOT.jar started by wangxiaoshun in /Users/<USER>/wingame/saas_spring/game-gateway)
2025-06-24 14:56:55.247 DEBUG 21607 --- [           main] com.wingame.gateway.GatewayApplication   : Running with Spring Boot v2.7.14, Spring v5.3.29
2025-06-24 14:56:55.248  INFO 21607 --- [           main] com.wingame.gateway.GatewayApplication   : The following 1 profile is active: "dev"
2025-06-24 14:56:55.273  WARN 21607 --- [           main] c.a.c.n.c.NacosConfigDataLoader          : [Nacos Config] config[dataId=game-gateway.yml, group=DEFAULT_GROUP] is empty
2025-06-24 14:56:55.841  INFO 21607 --- [           main] o.s.cloud.context.scope.GenericScope     : BeanFactory id=df71f0e5-18fd-3a2a-8837-817f6b8d7b89
2025-06-24 14:56:55.903  INFO 21607 --- [           main] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-24 14:56:55.904  INFO 21607 --- [           main] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-24 14:56:55.905  INFO 21607 --- [           main] trationDelegate$BeanPostProcessorChecker : Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-24 14:56:56.252 DEBUG 21607 --- [           main] o.s.c.gateway.config.GatewayProperties   : Routes supplied from Gateway Properties: [RouteDefinition{id='game-user', predicates=[PredicateDefinition{name='Path', args={_genkey_0=/user/**}}], filters=[FilterDefinition{name='StripPrefix', args={_genkey_0=1}}], uri=lb://game-user, order=0, metadata={}}, RouteDefinition{id='game-hall', predicates=[PredicateDefinition{name='Path', args={_genkey_0=/hall/**}}], filters=[FilterDefinition{name='StripPrefix', args={_genkey_0=1}}], uri=lb://game-hall, order=0, metadata={}}, RouteDefinition{id='game-agentgame', predicates=[PredicateDefinition{name='Path', args={_genkey_0=/agentgame/**}}], filters=[FilterDefinition{name='StripPrefix', args={_genkey_0=1}}], uri=lb://game-agentgame, order=0, metadata={}}, RouteDefinition{id='game-activity', predicates=[PredicateDefinition{name='Path', args={_genkey_0=/activity/**}}], filters=[FilterDefinition{name='StripPrefix', args={_genkey_0=1}}], uri=lb://game-activity, order=0, metadata={}}, RouteDefinition{id='game-account', predicates=[PredicateDefinition{name='Path', args={_genkey_0=/account/**}}], filters=[FilterDefinition{name='StripPrefix', args={_genkey_0=1}}], uri=lb://game-account, order=0, metadata={}}]
2025-06-24 14:56:56.389  INFO 21607 --- [           main] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [After]
2025-06-24 14:56:56.390  INFO 21607 --- [           main] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Before]
2025-06-24 14:56:56.390  INFO 21607 --- [           main] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Between]
2025-06-24 14:56:56.390  INFO 21607 --- [           main] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Cookie]
2025-06-24 14:56:56.390  INFO 21607 --- [           main] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Header]
2025-06-24 14:56:56.390  INFO 21607 --- [           main] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Host]
2025-06-24 14:56:56.390  INFO 21607 --- [           main] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Method]
2025-06-24 14:56:56.390  INFO 21607 --- [           main] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Path]
2025-06-24 14:56:56.390  INFO 21607 --- [           main] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Query]
2025-06-24 14:56:56.390  INFO 21607 --- [           main] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [ReadBody]
2025-06-24 14:56:56.390  INFO 21607 --- [           main] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [RemoteAddr]
2025-06-24 14:56:56.390  INFO 21607 --- [           main] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [XForwardedRemoteAddr]
2025-06-24 14:56:56.390  INFO 21607 --- [           main] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Weight]
2025-06-24 14:56:56.390  INFO 21607 --- [           main] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [CloudFoundryRouteService]
2025-06-24 14:56:56.409  INFO 21607 --- [           main] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 4 endpoint(s) beneath base path '/actuator'
2025-06-24 14:56:56.437 DEBUG 21607 --- [           main] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-user applying {_genkey_0=/user/**} to Path
2025-06-24 14:56:56.457 DEBUG 21607 --- [           main] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-user applying filter {_genkey_0=1} to StripPrefix
2025-06-24 14:56:56.460 DEBUG 21607 --- [           main] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-user
2025-06-24 14:56:56.461 DEBUG 21607 --- [           main] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-hall applying {_genkey_0=/hall/**} to Path
2025-06-24 14:56:56.461 DEBUG 21607 --- [           main] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-hall applying filter {_genkey_0=1} to StripPrefix
2025-06-24 14:56:56.461 DEBUG 21607 --- [           main] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-hall
2025-06-24 14:56:56.461 DEBUG 21607 --- [           main] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-agentgame applying {_genkey_0=/agentgame/**} to Path
2025-06-24 14:56:56.462 DEBUG 21607 --- [           main] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-agentgame applying filter {_genkey_0=1} to StripPrefix
2025-06-24 14:56:56.462 DEBUG 21607 --- [           main] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-agentgame
2025-06-24 14:56:56.462 DEBUG 21607 --- [           main] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-activity applying {_genkey_0=/activity/**} to Path
2025-06-24 14:56:56.462 DEBUG 21607 --- [           main] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-activity applying filter {_genkey_0=1} to StripPrefix
2025-06-24 14:56:56.462 DEBUG 21607 --- [           main] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-activity
2025-06-24 14:56:56.462 DEBUG 21607 --- [           main] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-account applying {_genkey_0=/account/**} to Path
2025-06-24 14:56:56.463 DEBUG 21607 --- [           main] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-account applying filter {_genkey_0=1} to StripPrefix
2025-06-24 14:56:56.463 DEBUG 21607 --- [           main] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-account
2025-06-24 14:56:56.465 DEBUG 21607 --- [           main] o.s.c.g.filter.GatewayMetricsFilter      : New routes count: 5
2025-06-24 14:56:56.472  INFO 21607 --- [           main] o.s.c.openfeign.FeignClientFactoryBean   : For 'game-user' URL not provided. Will try picking an instance via load-balancing.
2025-06-24 14:56:56.494 DEBUG 21607 --- [           main] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-user applying {_genkey_0=/user/**} to Path
2025-06-24 14:56:56.495 DEBUG 21607 --- [           main] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-user applying filter {_genkey_0=1} to StripPrefix
2025-06-24 14:56:56.495 DEBUG 21607 --- [           main] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-user
2025-06-24 14:56:56.495 DEBUG 21607 --- [           main] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-hall applying {_genkey_0=/hall/**} to Path
2025-06-24 14:56:56.495 DEBUG 21607 --- [           main] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-hall applying filter {_genkey_0=1} to StripPrefix
2025-06-24 14:56:56.495 DEBUG 21607 --- [           main] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-hall
2025-06-24 14:56:56.495 DEBUG 21607 --- [           main] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-agentgame applying {_genkey_0=/agentgame/**} to Path
2025-06-24 14:56:56.496 DEBUG 21607 --- [           main] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-agentgame applying filter {_genkey_0=1} to StripPrefix
2025-06-24 14:56:56.496 DEBUG 21607 --- [           main] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-agentgame
2025-06-24 14:56:56.496 DEBUG 21607 --- [           main] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-activity applying {_genkey_0=/activity/**} to Path
2025-06-24 14:56:56.496 DEBUG 21607 --- [           main] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-activity applying filter {_genkey_0=1} to StripPrefix
2025-06-24 14:56:56.496 DEBUG 21607 --- [           main] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-activity
2025-06-24 14:56:56.496 DEBUG 21607 --- [           main] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-account applying {_genkey_0=/account/**} to Path
2025-06-24 14:56:56.497 DEBUG 21607 --- [           main] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-account applying filter {_genkey_0=1} to StripPrefix
2025-06-24 14:56:56.497 DEBUG 21607 --- [           main] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-account
2025-06-24 14:56:56.497 DEBUG 21607 --- [           main] o.s.c.g.filter.GatewayMetricsFilter      : New routes count: 5
2025-06-24 14:56:56.498  INFO 21607 --- [           main] o.s.c.openfeign.FeignClientFactoryBean   : For 'game-account' URL not provided. Will try picking an instance via load-balancing.
2025-06-24 14:56:56.697  WARN 21607 --- [           main] iguration$LoadBalancerCaffeineWarnLogger : Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-06-24 14:56:56.738  INFO 21607 --- [           main] o.s.b.web.embedded.netty.NettyWebServer  : Netty started on port 8080
2025-06-24 14:56:56.747  INFO 21607 --- [           main] c.a.n.p.a.s.c.ClientAuthPluginManager    : [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-06-24 14:56:56.747  INFO 21607 --- [           main] c.a.n.p.a.s.c.ClientAuthPluginManager    : [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-06-24 14:56:56.872  INFO 21607 --- [           main] c.a.c.n.registry.NacosServiceRegistry    : nacos registry, DEFAULT_GROUP game-gateway ************:8080 register finished
2025-06-24 14:56:56.873 DEBUG 21607 --- [           main] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-user applying {_genkey_0=/user/**} to Path
2025-06-24 14:56:56.873 DEBUG 21607 --- [           main] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-user applying filter {_genkey_0=1} to StripPrefix
2025-06-24 14:56:56.874 DEBUG 21607 --- [           main] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-user
2025-06-24 14:56:56.874 DEBUG 21607 --- [           main] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-hall applying {_genkey_0=/hall/**} to Path
2025-06-24 14:56:56.874 DEBUG 21607 --- [           main] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-hall applying filter {_genkey_0=1} to StripPrefix
2025-06-24 14:56:56.874 DEBUG 21607 --- [           main] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-hall
2025-06-24 14:56:56.874 DEBUG 21607 --- [           main] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-agentgame applying {_genkey_0=/agentgame/**} to Path
2025-06-24 14:56:56.875 DEBUG 21607 --- [           main] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-agentgame applying filter {_genkey_0=1} to StripPrefix
2025-06-24 14:56:56.875 DEBUG 21607 --- [           main] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-agentgame
2025-06-24 14:56:56.875 DEBUG 21607 --- [           main] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-activity applying {_genkey_0=/activity/**} to Path
2025-06-24 14:56:56.875 DEBUG 21607 --- [           main] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-activity applying filter {_genkey_0=1} to StripPrefix
2025-06-24 14:56:56.876 DEBUG 21607 --- [           main] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-activity
2025-06-24 14:56:56.876 DEBUG 21607 --- [           main] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-account applying {_genkey_0=/account/**} to Path
2025-06-24 14:56:56.876 DEBUG 21607 --- [           main] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-account applying filter {_genkey_0=1} to StripPrefix
2025-06-24 14:56:56.877 DEBUG 21607 --- [           main] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-account
2025-06-24 14:56:56.877 DEBUG 21607 --- [           main] o.s.c.g.filter.GatewayMetricsFilter      : New routes count: 5
2025-06-24 14:56:56.885 DEBUG 21607 --- [           main] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-user applying {_genkey_0=/user/**} to Path
2025-06-24 14:56:56.885 DEBUG 21607 --- [           main] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-user applying filter {_genkey_0=1} to StripPrefix
2025-06-24 14:56:56.885 DEBUG 21607 --- [           main] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-user
2025-06-24 14:56:56.886 DEBUG 21607 --- [           main] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-hall applying {_genkey_0=/hall/**} to Path
2025-06-24 14:56:56.886 DEBUG 21607 --- [           main] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-hall applying filter {_genkey_0=1} to StripPrefix
2025-06-24 14:56:56.886 DEBUG 21607 --- [           main] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-hall
2025-06-24 14:56:56.886 DEBUG 21607 --- [           main] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-agentgame applying {_genkey_0=/agentgame/**} to Path
2025-06-24 14:56:56.886 DEBUG 21607 --- [           main] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-agentgame applying filter {_genkey_0=1} to StripPrefix
2025-06-24 14:56:56.886 DEBUG 21607 --- [           main] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-agentgame
2025-06-24 14:56:56.886 DEBUG 21607 --- [           main] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-activity applying {_genkey_0=/activity/**} to Path
2025-06-24 14:56:56.887 DEBUG 21607 --- [           main] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-activity applying filter {_genkey_0=1} to StripPrefix
2025-06-24 14:56:56.887 DEBUG 21607 --- [           main] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-activity
2025-06-24 14:56:56.887 DEBUG 21607 --- [           main] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-account applying {_genkey_0=/account/**} to Path
2025-06-24 14:56:56.887 DEBUG 21607 --- [           main] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-account applying filter {_genkey_0=1} to StripPrefix
2025-06-24 14:56:56.887 DEBUG 21607 --- [           main] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-account
2025-06-24 14:56:56.887 DEBUG 21607 --- [           main] o.s.c.g.filter.GatewayMetricsFilter      : New routes count: 5
2025-06-24 14:56:56.888  INFO 21607 --- [           main] com.wingame.gateway.GatewayApplication   : Started GatewayApplication in 7.528 seconds (JVM running for 7.737)
2025-06-24 14:56:56.897  INFO 21607 --- [           main] c.a.c.n.refresh.NacosContextRefresher    : [Nacos Config] Listening config: dataId=game-gateway.yml, group=DEFAULT_GROUP
2025-06-24 14:56:57.442 DEBUG 21607 --- [ctor-http-nio-5] o.s.c.g.h.RoutePredicateHandlerMapping   : Route matched: game-user
2025-06-24 14:56:57.442 DEBUG 21607 --- [ctor-http-nio-5] o.s.c.g.h.RoutePredicateHandlerMapping   : Mapping [Exchange: GET http://localhost:8080/user/api/user/aggregation/health] to Route{id='game-user', uri=lb://game-user, order=0, predicate=Paths: [/user/**], match trailing slash: true, gatewayFilters=[[[StripPrefix parts = 1], order = 1]], metadata={}}
2025-06-24 14:56:57.442 DEBUG 21607 --- [ctor-http-nio-5] o.s.c.g.h.RoutePredicateHandlerMapping   : [a1e79160-4] Mapped to org.springframework.cloud.gateway.handler.FilteringWebHandler@6ba838f1
2025-06-24 14:56:57.442 DEBUG 21607 --- [ctor-http-nio-5] o.s.c.g.handler.FilteringWebHandler      : Sorted gatewayFilterFactories: [[GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.RemoveCachedBodyFilter@21ab988f}, order = -**********], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.AdaptCachedBodyGlobalFilter@34237b90}, order = -**********], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.NettyWriteResponseFilter@2d6aca33}, order = -1], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.ForwardPathFilter@35f8a9d3}, order = 0], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.GatewayMetricsFilter@6b1e7ad3}, order = 0], [[StripPrefix parts = 1], order = 1], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.RouteToRequestUrlFilter@29314cc9}, order = 10000], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.ReactiveLoadBalancerClientFilter@62ddd21b}, order = 10150], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.LoadBalancerServiceInstanceCookieFilter@16c3ca31}, order = 10151], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.WebsocketRoutingFilter@48ea2003}, order = **********], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.NettyRoutingFilter@2d195ee4}, order = **********], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.ForwardRoutingFilter@4e38d975}, order = **********]]
2025-06-24 14:56:57.461 DEBUG 21607 --- [ctor-http-nio-5] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-user applying {_genkey_0=/user/**} to Path
2025-06-24 14:56:57.462 DEBUG 21607 --- [ctor-http-nio-5] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-user applying filter {_genkey_0=1} to StripPrefix
2025-06-24 14:56:57.462 DEBUG 21607 --- [ctor-http-nio-5] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-user
2025-06-24 14:56:57.462 DEBUG 21607 --- [ctor-http-nio-5] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-hall applying {_genkey_0=/hall/**} to Path
2025-06-24 14:56:57.463 DEBUG 21607 --- [ctor-http-nio-5] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-hall applying filter {_genkey_0=1} to StripPrefix
2025-06-24 14:56:57.463 DEBUG 21607 --- [ctor-http-nio-5] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-hall
2025-06-24 14:56:57.463 DEBUG 21607 --- [ctor-http-nio-5] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-agentgame applying {_genkey_0=/agentgame/**} to Path
2025-06-24 14:56:57.463 DEBUG 21607 --- [ctor-http-nio-5] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-agentgame applying filter {_genkey_0=1} to StripPrefix
2025-06-24 14:56:57.463 DEBUG 21607 --- [ctor-http-nio-5] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-agentgame
2025-06-24 14:56:57.463 DEBUG 21607 --- [ctor-http-nio-5] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-activity applying {_genkey_0=/activity/**} to Path
2025-06-24 14:56:57.464 DEBUG 21607 --- [ctor-http-nio-5] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-activity applying filter {_genkey_0=1} to StripPrefix
2025-06-24 14:56:57.464 DEBUG 21607 --- [ctor-http-nio-5] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-activity
2025-06-24 14:56:57.464 DEBUG 21607 --- [ctor-http-nio-5] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-account applying {_genkey_0=/account/**} to Path
2025-06-24 14:56:57.464 DEBUG 21607 --- [ctor-http-nio-5] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-account applying filter {_genkey_0=1} to StripPrefix
2025-06-24 14:56:57.464 DEBUG 21607 --- [ctor-http-nio-5] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-account
2025-06-24 14:56:57.465 DEBUG 21607 --- [ctor-http-nio-5] o.s.c.g.filter.GatewayMetricsFilter      : New routes count: 5
2025-06-24 14:56:57.503 ERROR 21607 --- [oundedElastic-2] i.n.r.d.DnsServerAddressStreamProviders  : Unable to load io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'. Use DEBUG level to see the full stack: java.lang.UnsatisfiedLinkError: failed to load the required native library
2025-06-24 14:56:57.538 DEBUG 21607 --- [ctor-http-nio-8] o.s.c.g.h.RoutePredicateHandlerMapping   : Route matched: game-account
2025-06-24 14:56:57.538 DEBUG 21607 --- [ctor-http-nio-8] o.s.c.g.h.RoutePredicateHandlerMapping   : Mapping [Exchange: GET http://localhost:8080/account/api/account/test_user/balance] to Route{id='game-account', uri=lb://game-account, order=0, predicate=Paths: [/account/**], match trailing slash: true, gatewayFilters=[[[StripPrefix parts = 1], order = 1]], metadata={}}
2025-06-24 14:56:57.538 DEBUG 21607 --- [ctor-http-nio-8] o.s.c.g.h.RoutePredicateHandlerMapping   : [506035c3-5] Mapped to org.springframework.cloud.gateway.handler.FilteringWebHandler@6ba838f1
2025-06-24 14:56:57.538 DEBUG 21607 --- [ctor-http-nio-8] o.s.c.g.handler.FilteringWebHandler      : Sorted gatewayFilterFactories: [[GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.RemoveCachedBodyFilter@21ab988f}, order = -**********], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.AdaptCachedBodyGlobalFilter@34237b90}, order = -**********], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.NettyWriteResponseFilter@2d6aca33}, order = -1], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.ForwardPathFilter@35f8a9d3}, order = 0], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.GatewayMetricsFilter@6b1e7ad3}, order = 0], [[StripPrefix parts = 1], order = 1], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.RouteToRequestUrlFilter@29314cc9}, order = 10000], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.ReactiveLoadBalancerClientFilter@62ddd21b}, order = 10150], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.LoadBalancerServiceInstanceCookieFilter@16c3ca31}, order = 10151], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.WebsocketRoutingFilter@48ea2003}, order = **********], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.NettyRoutingFilter@2d195ee4}, order = **********], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.ForwardRoutingFilter@4e38d975}, order = **********]]
2025-06-24 14:56:57.545 DEBUG 21607 --- [ctor-http-nio-8] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-user applying {_genkey_0=/user/**} to Path
2025-06-24 14:56:57.546 DEBUG 21607 --- [ctor-http-nio-8] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-user applying filter {_genkey_0=1} to StripPrefix
2025-06-24 14:56:57.547 DEBUG 21607 --- [ctor-http-nio-8] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-user
2025-06-24 14:56:57.547 DEBUG 21607 --- [ctor-http-nio-8] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-hall applying {_genkey_0=/hall/**} to Path
2025-06-24 14:56:57.547 DEBUG 21607 --- [ctor-http-nio-8] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-hall applying filter {_genkey_0=1} to StripPrefix
2025-06-24 14:56:57.547 DEBUG 21607 --- [ctor-http-nio-8] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-hall
2025-06-24 14:56:57.547 DEBUG 21607 --- [ctor-http-nio-8] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-agentgame applying {_genkey_0=/agentgame/**} to Path
2025-06-24 14:56:57.547 DEBUG 21607 --- [ctor-http-nio-8] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-agentgame applying filter {_genkey_0=1} to StripPrefix
2025-06-24 14:56:57.548 DEBUG 21607 --- [ctor-http-nio-8] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-agentgame
2025-06-24 14:56:57.548 DEBUG 21607 --- [ctor-http-nio-8] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-activity applying {_genkey_0=/activity/**} to Path
2025-06-24 14:56:57.548 DEBUG 21607 --- [ctor-http-nio-8] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-activity applying filter {_genkey_0=1} to StripPrefix
2025-06-24 14:56:57.548 DEBUG 21607 --- [ctor-http-nio-8] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-activity
2025-06-24 14:56:57.548 DEBUG 21607 --- [ctor-http-nio-8] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-account applying {_genkey_0=/account/**} to Path
2025-06-24 14:56:57.548 DEBUG 21607 --- [ctor-http-nio-8] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-account applying filter {_genkey_0=1} to StripPrefix
2025-06-24 14:56:57.548 DEBUG 21607 --- [ctor-http-nio-8] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-account
2025-06-24 14:56:57.549 DEBUG 21607 --- [ctor-http-nio-8] o.s.c.g.filter.GatewayMetricsFilter      : New routes count: 5
2025-06-24 14:57:14.975 DEBUG 21607 --- [tor-http-nio-10] o.s.c.g.h.RoutePredicateHandlerMapping   : Route matched: game-user
2025-06-24 14:57:14.975 DEBUG 21607 --- [tor-http-nio-10] o.s.c.g.h.RoutePredicateHandlerMapping   : Mapping [Exchange: GET http://localhost:8080/user/api/user/aggregation/health] to Route{id='game-user', uri=lb://game-user, order=0, predicate=Paths: [/user/**], match trailing slash: true, gatewayFilters=[[[StripPrefix parts = 1], order = 1]], metadata={}}
2025-06-24 14:57:14.975 DEBUG 21607 --- [tor-http-nio-10] o.s.c.g.h.RoutePredicateHandlerMapping   : [48ece87a-6] Mapped to org.springframework.cloud.gateway.handler.FilteringWebHandler@6ba838f1
2025-06-24 14:57:14.975 DEBUG 21607 --- [tor-http-nio-10] o.s.c.g.handler.FilteringWebHandler      : Sorted gatewayFilterFactories: [[GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.RemoveCachedBodyFilter@21ab988f}, order = -**********], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.AdaptCachedBodyGlobalFilter@34237b90}, order = -**********], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.NettyWriteResponseFilter@2d6aca33}, order = -1], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.ForwardPathFilter@35f8a9d3}, order = 0], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.GatewayMetricsFilter@6b1e7ad3}, order = 0], [[StripPrefix parts = 1], order = 1], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.RouteToRequestUrlFilter@29314cc9}, order = 10000], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.ReactiveLoadBalancerClientFilter@62ddd21b}, order = 10150], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.LoadBalancerServiceInstanceCookieFilter@16c3ca31}, order = 10151], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.WebsocketRoutingFilter@48ea2003}, order = **********], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.NettyRoutingFilter@2d195ee4}, order = **********], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.ForwardRoutingFilter@4e38d975}, order = **********]]
2025-06-24 14:57:35.677 DEBUG 21607 --- [ctor-http-nio-1] o.s.c.g.h.RoutePredicateHandlerMapping   : Route matched: game-user
2025-06-24 14:57:35.677 DEBUG 21607 --- [ctor-http-nio-1] o.s.c.g.h.RoutePredicateHandlerMapping   : Mapping [Exchange: GET http://localhost:8080/user/api/user/aggregation/profile/demo_user_123] to Route{id='game-user', uri=lb://game-user, order=0, predicate=Paths: [/user/**], match trailing slash: true, gatewayFilters=[[[StripPrefix parts = 1], order = 1]], metadata={}}
2025-06-24 14:57:35.677 DEBUG 21607 --- [ctor-http-nio-1] o.s.c.g.h.RoutePredicateHandlerMapping   : [bacb8198-7] Mapped to org.springframework.cloud.gateway.handler.FilteringWebHandler@6ba838f1
2025-06-24 14:57:35.677 DEBUG 21607 --- [ctor-http-nio-1] o.s.c.g.handler.FilteringWebHandler      : Sorted gatewayFilterFactories: [[GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.RemoveCachedBodyFilter@21ab988f}, order = -**********], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.AdaptCachedBodyGlobalFilter@34237b90}, order = -**********], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.NettyWriteResponseFilter@2d6aca33}, order = -1], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.ForwardPathFilter@35f8a9d3}, order = 0], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.GatewayMetricsFilter@6b1e7ad3}, order = 0], [[StripPrefix parts = 1], order = 1], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.RouteToRequestUrlFilter@29314cc9}, order = 10000], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.ReactiveLoadBalancerClientFilter@62ddd21b}, order = 10150], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.LoadBalancerServiceInstanceCookieFilter@16c3ca31}, order = 10151], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.WebsocketRoutingFilter@48ea2003}, order = **********], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.NettyRoutingFilter@2d195ee4}, order = **********], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.ForwardRoutingFilter@4e38d975}, order = **********]]
2025-06-24 14:57:58.200 DEBUG 21607 --- [ctor-http-nio-3] o.s.c.g.h.RoutePredicateHandlerMapping   : Route matched: game-user
2025-06-24 14:57:58.201 DEBUG 21607 --- [ctor-http-nio-3] o.s.c.g.h.RoutePredicateHandlerMapping   : Mapping [Exchange: GET http://localhost:8080/user/api/user/aggregation/health] to Route{id='game-user', uri=lb://game-user, order=0, predicate=Paths: [/user/**], match trailing slash: true, gatewayFilters=[[[StripPrefix parts = 1], order = 1]], metadata={}}
2025-06-24 14:57:58.201 DEBUG 21607 --- [ctor-http-nio-3] o.s.c.g.h.RoutePredicateHandlerMapping   : [0de00b14-9] Mapped to org.springframework.cloud.gateway.handler.FilteringWebHandler@6ba838f1
2025-06-24 14:57:58.201 DEBUG 21607 --- [ctor-http-nio-3] o.s.c.g.handler.FilteringWebHandler      : Sorted gatewayFilterFactories: [[GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.RemoveCachedBodyFilter@21ab988f}, order = -**********], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.AdaptCachedBodyGlobalFilter@34237b90}, order = -**********], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.NettyWriteResponseFilter@2d6aca33}, order = -1], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.ForwardPathFilter@35f8a9d3}, order = 0], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.GatewayMetricsFilter@6b1e7ad3}, order = 0], [[StripPrefix parts = 1], order = 1], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.RouteToRequestUrlFilter@29314cc9}, order = 10000], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.ReactiveLoadBalancerClientFilter@62ddd21b}, order = 10150], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.LoadBalancerServiceInstanceCookieFilter@16c3ca31}, order = 10151], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.WebsocketRoutingFilter@48ea2003}, order = **********], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.NettyRoutingFilter@2d195ee4}, order = **********], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.ForwardRoutingFilter@4e38d975}, order = **********]]
2025-06-24 14:57:58.247 DEBUG 21607 --- [ctor-http-nio-4] o.s.c.g.h.RoutePredicateHandlerMapping   : Route matched: game-user
2025-06-24 14:57:58.247 DEBUG 21607 --- [ctor-http-nio-4] o.s.c.g.h.RoutePredicateHandlerMapping   : Mapping [Exchange: GET http://localhost:8080/user/api/user/aggregation/profile/demo_user_123] to Route{id='game-user', uri=lb://game-user, order=0, predicate=Paths: [/user/**], match trailing slash: true, gatewayFilters=[[[StripPrefix parts = 1], order = 1]], metadata={}}
2025-06-24 14:57:58.247 DEBUG 21607 --- [ctor-http-nio-4] o.s.c.g.h.RoutePredicateHandlerMapping   : [74e829df-10] Mapped to org.springframework.cloud.gateway.handler.FilteringWebHandler@6ba838f1
2025-06-24 14:57:58.247 DEBUG 21607 --- [ctor-http-nio-4] o.s.c.g.handler.FilteringWebHandler      : Sorted gatewayFilterFactories: [[GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.RemoveCachedBodyFilter@21ab988f}, order = -**********], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.AdaptCachedBodyGlobalFilter@34237b90}, order = -**********], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.NettyWriteResponseFilter@2d6aca33}, order = -1], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.ForwardPathFilter@35f8a9d3}, order = 0], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.GatewayMetricsFilter@6b1e7ad3}, order = 0], [[StripPrefix parts = 1], order = 1], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.RouteToRequestUrlFilter@29314cc9}, order = 10000], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.ReactiveLoadBalancerClientFilter@62ddd21b}, order = 10150], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.LoadBalancerServiceInstanceCookieFilter@16c3ca31}, order = 10151], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.WebsocketRoutingFilter@48ea2003}, order = **********], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.NettyRoutingFilter@2d195ee4}, order = **********], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.ForwardRoutingFilter@4e38d975}, order = **********]]
2025-06-24 14:57:58.291 DEBUG 21607 --- [ctor-http-nio-5] o.s.c.g.h.RoutePredicateHandlerMapping   : Route matched: game-user
2025-06-24 14:57:58.291 DEBUG 21607 --- [ctor-http-nio-5] o.s.c.g.h.RoutePredicateHandlerMapping   : Mapping [Exchange: GET http://localhost:8080/user/api/user/aggregation/profile/demo_user_123/balance] to Route{id='game-user', uri=lb://game-user, order=0, predicate=Paths: [/user/**], match trailing slash: true, gatewayFilters=[[[StripPrefix parts = 1], order = 1]], metadata={}}
2025-06-24 14:57:58.291 DEBUG 21607 --- [ctor-http-nio-5] o.s.c.g.h.RoutePredicateHandlerMapping   : [8032c1ab-11] Mapped to org.springframework.cloud.gateway.handler.FilteringWebHandler@6ba838f1
2025-06-24 14:57:58.291 DEBUG 21607 --- [ctor-http-nio-5] o.s.c.g.handler.FilteringWebHandler      : Sorted gatewayFilterFactories: [[GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.RemoveCachedBodyFilter@21ab988f}, order = -**********], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.AdaptCachedBodyGlobalFilter@34237b90}, order = -**********], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.NettyWriteResponseFilter@2d6aca33}, order = -1], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.ForwardPathFilter@35f8a9d3}, order = 0], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.GatewayMetricsFilter@6b1e7ad3}, order = 0], [[StripPrefix parts = 1], order = 1], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.RouteToRequestUrlFilter@29314cc9}, order = 10000], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.ReactiveLoadBalancerClientFilter@62ddd21b}, order = 10150], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.LoadBalancerServiceInstanceCookieFilter@16c3ca31}, order = 10151], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.WebsocketRoutingFilter@48ea2003}, order = **********], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.NettyRoutingFilter@2d195ee4}, order = **********], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.ForwardRoutingFilter@4e38d975}, order = **********]]
2025-06-24 14:57:58.334 DEBUG 21607 --- [ctor-http-nio-6] o.s.c.g.h.RoutePredicateHandlerMapping   : Route matched: game-user
2025-06-24 14:57:58.334 DEBUG 21607 --- [ctor-http-nio-6] o.s.c.g.h.RoutePredicateHandlerMapping   : Mapping [Exchange: GET http://localhost:8080/user/api/user/aggregation/profile/demo_user_123/statistics] to Route{id='game-user', uri=lb://game-user, order=0, predicate=Paths: [/user/**], match trailing slash: true, gatewayFilters=[[[StripPrefix parts = 1], order = 1]], metadata={}}
2025-06-24 14:57:58.334 DEBUG 21607 --- [ctor-http-nio-6] o.s.c.g.h.RoutePredicateHandlerMapping   : [3aab6bd7-12] Mapped to org.springframework.cloud.gateway.handler.FilteringWebHandler@6ba838f1
2025-06-24 14:57:58.334 DEBUG 21607 --- [ctor-http-nio-6] o.s.c.g.handler.FilteringWebHandler      : Sorted gatewayFilterFactories: [[GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.RemoveCachedBodyFilter@21ab988f}, order = -**********], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.AdaptCachedBodyGlobalFilter@34237b90}, order = -**********], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.NettyWriteResponseFilter@2d6aca33}, order = -1], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.ForwardPathFilter@35f8a9d3}, order = 0], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.GatewayMetricsFilter@6b1e7ad3}, order = 0], [[StripPrefix parts = 1], order = 1], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.RouteToRequestUrlFilter@29314cc9}, order = 10000], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.ReactiveLoadBalancerClientFilter@62ddd21b}, order = 10150], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.LoadBalancerServiceInstanceCookieFilter@16c3ca31}, order = 10151], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.WebsocketRoutingFilter@48ea2003}, order = **********], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.NettyRoutingFilter@2d195ee4}, order = **********], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.ForwardRoutingFilter@4e38d975}, order = **********]]
2025-06-24 14:57:58.376 DEBUG 21607 --- [ctor-http-nio-7] o.s.c.g.h.RoutePredicateHandlerMapping   : Route matched: game-user
2025-06-24 14:57:58.376 DEBUG 21607 --- [ctor-http-nio-7] o.s.c.g.h.RoutePredicateHandlerMapping   : Mapping [Exchange: GET http://localhost:8080/user/api/user/aggregation/profiles?userIds=user001,user002,user003] to Route{id='game-user', uri=lb://game-user, order=0, predicate=Paths: [/user/**], match trailing slash: true, gatewayFilters=[[[StripPrefix parts = 1], order = 1]], metadata={}}
2025-06-24 14:57:58.376 DEBUG 21607 --- [ctor-http-nio-7] o.s.c.g.h.RoutePredicateHandlerMapping   : [ea52ed3e-13] Mapped to org.springframework.cloud.gateway.handler.FilteringWebHandler@6ba838f1
2025-06-24 14:57:58.376 DEBUG 21607 --- [ctor-http-nio-7] o.s.c.g.handler.FilteringWebHandler      : Sorted gatewayFilterFactories: [[GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.RemoveCachedBodyFilter@21ab988f}, order = -**********], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.AdaptCachedBodyGlobalFilter@34237b90}, order = -**********], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.NettyWriteResponseFilter@2d6aca33}, order = -1], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.ForwardPathFilter@35f8a9d3}, order = 0], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.GatewayMetricsFilter@6b1e7ad3}, order = 0], [[StripPrefix parts = 1], order = 1], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.RouteToRequestUrlFilter@29314cc9}, order = 10000], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.ReactiveLoadBalancerClientFilter@62ddd21b}, order = 10150], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.LoadBalancerServiceInstanceCookieFilter@16c3ca31}, order = 10151], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.WebsocketRoutingFilter@48ea2003}, order = **********], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.NettyRoutingFilter@2d195ee4}, order = **********], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.ForwardRoutingFilter@4e38d975}, order = **********]]
2025-06-24 14:57:58.426 DEBUG 21607 --- [ctor-http-nio-8] o.s.c.g.h.RoutePredicateHandlerMapping   : Route matched: game-account
2025-06-24 14:57:58.426 DEBUG 21607 --- [ctor-http-nio-8] o.s.c.g.h.RoutePredicateHandlerMapping   : Mapping [Exchange: GET http://localhost:8080/account/api/account/demo_user_123/balance] to Route{id='game-account', uri=lb://game-account, order=0, predicate=Paths: [/account/**], match trailing slash: true, gatewayFilters=[[[StripPrefix parts = 1], order = 1]], metadata={}}
2025-06-24 14:57:58.426 DEBUG 21607 --- [ctor-http-nio-8] o.s.c.g.h.RoutePredicateHandlerMapping   : [b5abb80f-14] Mapped to org.springframework.cloud.gateway.handler.FilteringWebHandler@6ba838f1
2025-06-24 14:57:58.426 DEBUG 21607 --- [ctor-http-nio-8] o.s.c.g.handler.FilteringWebHandler      : Sorted gatewayFilterFactories: [[GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.RemoveCachedBodyFilter@21ab988f}, order = -**********], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.AdaptCachedBodyGlobalFilter@34237b90}, order = -**********], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.NettyWriteResponseFilter@2d6aca33}, order = -1], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.ForwardPathFilter@35f8a9d3}, order = 0], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.GatewayMetricsFilter@6b1e7ad3}, order = 0], [[StripPrefix parts = 1], order = 1], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.RouteToRequestUrlFilter@29314cc9}, order = 10000], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.ReactiveLoadBalancerClientFilter@62ddd21b}, order = 10150], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.LoadBalancerServiceInstanceCookieFilter@16c3ca31}, order = 10151], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.WebsocketRoutingFilter@48ea2003}, order = **********], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.NettyRoutingFilter@2d195ee4}, order = **********], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.ForwardRoutingFilter@4e38d975}, order = **********]]
2025-06-24 14:57:58.465 DEBUG 21607 --- [tor-http-nio-10] o.s.c.g.h.RoutePredicateHandlerMapping   : Route matched: game-user
2025-06-24 14:57:58.465 DEBUG 21607 --- [tor-http-nio-10] o.s.c.g.h.RoutePredicateHandlerMapping   : Mapping [Exchange: GET http://localhost:8080/user/api/user/aggregation/profile/demo_user_123/balance] to Route{id='game-user', uri=lb://game-user, order=0, predicate=Paths: [/user/**], match trailing slash: true, gatewayFilters=[[[StripPrefix parts = 1], order = 1]], metadata={}}
2025-06-24 14:57:58.465 DEBUG 21607 --- [tor-http-nio-10] o.s.c.g.h.RoutePredicateHandlerMapping   : [16303cb6-15] Mapped to org.springframework.cloud.gateway.handler.FilteringWebHandler@6ba838f1
2025-06-24 14:57:58.465 DEBUG 21607 --- [tor-http-nio-10] o.s.c.g.handler.FilteringWebHandler      : Sorted gatewayFilterFactories: [[GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.RemoveCachedBodyFilter@21ab988f}, order = -**********], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.AdaptCachedBodyGlobalFilter@34237b90}, order = -**********], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.NettyWriteResponseFilter@2d6aca33}, order = -1], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.ForwardPathFilter@35f8a9d3}, order = 0], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.GatewayMetricsFilter@6b1e7ad3}, order = 0], [[StripPrefix parts = 1], order = 1], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.RouteToRequestUrlFilter@29314cc9}, order = 10000], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.ReactiveLoadBalancerClientFilter@62ddd21b}, order = 10150], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.LoadBalancerServiceInstanceCookieFilter@16c3ca31}, order = 10151], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.WebsocketRoutingFilter@48ea2003}, order = **********], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.NettyRoutingFilter@2d195ee4}, order = **********], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.ForwardRoutingFilter@4e38d975}, order = **********]]
2025-06-24 14:57:58.506 DEBUG 21607 --- [ctor-http-nio-1] o.s.c.g.h.RoutePredicateHandlerMapping   : Route matched: game-user
2025-06-24 14:57:58.507 DEBUG 21607 --- [ctor-http-nio-1] o.s.c.g.h.RoutePredicateHandlerMapping   : Mapping [Exchange: GET http://localhost:8080/user/api/user/aggregation/profile/fallback_test_user/balance] to Route{id='game-user', uri=lb://game-user, order=0, predicate=Paths: [/user/**], match trailing slash: true, gatewayFilters=[[[StripPrefix parts = 1], order = 1]], metadata={}}
2025-06-24 14:57:58.507 DEBUG 21607 --- [ctor-http-nio-1] o.s.c.g.h.RoutePredicateHandlerMapping   : [5ec284db-16] Mapped to org.springframework.cloud.gateway.handler.FilteringWebHandler@6ba838f1
2025-06-24 14:57:58.507 DEBUG 21607 --- [ctor-http-nio-1] o.s.c.g.handler.FilteringWebHandler      : Sorted gatewayFilterFactories: [[GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.RemoveCachedBodyFilter@21ab988f}, order = -**********], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.AdaptCachedBodyGlobalFilter@34237b90}, order = -**********], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.NettyWriteResponseFilter@2d6aca33}, order = -1], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.ForwardPathFilter@35f8a9d3}, order = 0], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.GatewayMetricsFilter@6b1e7ad3}, order = 0], [[StripPrefix parts = 1], order = 1], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.RouteToRequestUrlFilter@29314cc9}, order = 10000], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.ReactiveLoadBalancerClientFilter@62ddd21b}, order = 10150], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.LoadBalancerServiceInstanceCookieFilter@16c3ca31}, order = 10151], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.WebsocketRoutingFilter@48ea2003}, order = **********], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.NettyRoutingFilter@2d195ee4}, order = **********], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.ForwardRoutingFilter@4e38d975}, order = **********]]
2025-06-24 14:57:58.555 DEBUG 21607 --- [ctor-http-nio-2] o.s.c.g.h.RoutePredicateHandlerMapping   : Route matched: game-user
2025-06-24 14:57:58.555 DEBUG 21607 --- [ctor-http-nio-6] o.s.c.g.h.RoutePredicateHandlerMapping   : Route matched: game-user
2025-06-24 14:57:58.555 DEBUG 21607 --- [ctor-http-nio-3] o.s.c.g.h.RoutePredicateHandlerMapping   : Route matched: game-user
2025-06-24 14:57:58.555 DEBUG 21607 --- [ctor-http-nio-6] o.s.c.g.h.RoutePredicateHandlerMapping   : Mapping [Exchange: GET http://localhost:8080/user/api/user/aggregation/profile/concurrent_user_4] to Route{id='game-user', uri=lb://game-user, order=0, predicate=Paths: [/user/**], match trailing slash: true, gatewayFilters=[[[StripPrefix parts = 1], order = 1]], metadata={}}
2025-06-24 14:57:58.555 DEBUG 21607 --- [ctor-http-nio-2] o.s.c.g.h.RoutePredicateHandlerMapping   : Mapping [Exchange: GET http://localhost:8080/user/api/user/aggregation/profile/concurrent_user_3] to Route{id='game-user', uri=lb://game-user, order=0, predicate=Paths: [/user/**], match trailing slash: true, gatewayFilters=[[[StripPrefix parts = 1], order = 1]], metadata={}}
2025-06-24 14:57:58.555 DEBUG 21607 --- [ctor-http-nio-6] o.s.c.g.h.RoutePredicateHandlerMapping   : [6b146714-20] Mapped to org.springframework.cloud.gateway.handler.FilteringWebHandler@6ba838f1
2025-06-24 14:57:58.555 DEBUG 21607 --- [ctor-http-nio-5] o.s.c.g.h.RoutePredicateHandlerMapping   : Route matched: game-user
2025-06-24 14:57:58.555 DEBUG 21607 --- [ctor-http-nio-3] o.s.c.g.h.RoutePredicateHandlerMapping   : Mapping [Exchange: GET http://localhost:8080/user/api/user/aggregation/profile/concurrent_user_5] to Route{id='game-user', uri=lb://game-user, order=0, predicate=Paths: [/user/**], match trailing slash: true, gatewayFilters=[[[StripPrefix parts = 1], order = 1]], metadata={}}
2025-06-24 14:57:58.555 DEBUG 21607 --- [ctor-http-nio-5] o.s.c.g.h.RoutePredicateHandlerMapping   : Mapping [Exchange: GET http://localhost:8080/user/api/user/aggregation/profile/concurrent_user_1] to Route{id='game-user', uri=lb://game-user, order=0, predicate=Paths: [/user/**], match trailing slash: true, gatewayFilters=[[[StripPrefix parts = 1], order = 1]], metadata={}}
2025-06-24 14:57:58.555 DEBUG 21607 --- [ctor-http-nio-6] o.s.c.g.handler.FilteringWebHandler      : Sorted gatewayFilterFactories: [[GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.RemoveCachedBodyFilter@21ab988f}, order = -**********], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.AdaptCachedBodyGlobalFilter@34237b90}, order = -**********], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.NettyWriteResponseFilter@2d6aca33}, order = -1], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.ForwardPathFilter@35f8a9d3}, order = 0], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.GatewayMetricsFilter@6b1e7ad3}, order = 0], [[StripPrefix parts = 1], order = 1], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.RouteToRequestUrlFilter@29314cc9}, order = 10000], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.ReactiveLoadBalancerClientFilter@62ddd21b}, order = 10150], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.LoadBalancerServiceInstanceCookieFilter@16c3ca31}, order = 10151], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.WebsocketRoutingFilter@48ea2003}, order = **********], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.NettyRoutingFilter@2d195ee4}, order = **********], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.ForwardRoutingFilter@4e38d975}, order = **********]]
2025-06-24 14:57:58.555 DEBUG 21607 --- [ctor-http-nio-4] o.s.c.g.h.RoutePredicateHandlerMapping   : Route matched: game-user
2025-06-24 14:57:58.555 DEBUG 21607 --- [ctor-http-nio-2] o.s.c.g.h.RoutePredicateHandlerMapping   : [ed6c7fc8-19] Mapped to org.springframework.cloud.gateway.handler.FilteringWebHandler@6ba838f1
2025-06-24 14:57:58.555 DEBUG 21607 --- [ctor-http-nio-5] o.s.c.g.h.RoutePredicateHandlerMapping   : [8a4e5638-21] Mapped to org.springframework.cloud.gateway.handler.FilteringWebHandler@6ba838f1
2025-06-24 14:57:58.555 DEBUG 21607 --- [ctor-http-nio-3] o.s.c.g.h.RoutePredicateHandlerMapping   : [fec17822-17] Mapped to org.springframework.cloud.gateway.handler.FilteringWebHandler@6ba838f1
2025-06-24 14:57:58.556 DEBUG 21607 --- [ctor-http-nio-4] o.s.c.g.h.RoutePredicateHandlerMapping   : Mapping [Exchange: GET http://localhost:8080/user/api/user/aggregation/profile/concurrent_user_2] to Route{id='game-user', uri=lb://game-user, order=0, predicate=Paths: [/user/**], match trailing slash: true, gatewayFilters=[[[StripPrefix parts = 1], order = 1]], metadata={}}
2025-06-24 14:57:58.556 DEBUG 21607 --- [ctor-http-nio-4] o.s.c.g.h.RoutePredicateHandlerMapping   : [2251b7f7-18] Mapped to org.springframework.cloud.gateway.handler.FilteringWebHandler@6ba838f1
2025-06-24 14:57:58.556 DEBUG 21607 --- [ctor-http-nio-2] o.s.c.g.handler.FilteringWebHandler      : Sorted gatewayFilterFactories: [[GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.RemoveCachedBodyFilter@21ab988f}, order = -**********], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.AdaptCachedBodyGlobalFilter@34237b90}, order = -**********], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.NettyWriteResponseFilter@2d6aca33}, order = -1], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.ForwardPathFilter@35f8a9d3}, order = 0], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.GatewayMetricsFilter@6b1e7ad3}, order = 0], [[StripPrefix parts = 1], order = 1], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.RouteToRequestUrlFilter@29314cc9}, order = 10000], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.ReactiveLoadBalancerClientFilter@62ddd21b}, order = 10150], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.LoadBalancerServiceInstanceCookieFilter@16c3ca31}, order = 10151], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.WebsocketRoutingFilter@48ea2003}, order = **********], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.NettyRoutingFilter@2d195ee4}, order = **********], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.ForwardRoutingFilter@4e38d975}, order = **********]]
2025-06-24 14:57:58.556 DEBUG 21607 --- [ctor-http-nio-5] o.s.c.g.handler.FilteringWebHandler      : Sorted gatewayFilterFactories: [[GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.RemoveCachedBodyFilter@21ab988f}, order = -**********], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.AdaptCachedBodyGlobalFilter@34237b90}, order = -**********], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.NettyWriteResponseFilter@2d6aca33}, order = -1], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.ForwardPathFilter@35f8a9d3}, order = 0], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.GatewayMetricsFilter@6b1e7ad3}, order = 0], [[StripPrefix parts = 1], order = 1], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.RouteToRequestUrlFilter@29314cc9}, order = 10000], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.ReactiveLoadBalancerClientFilter@62ddd21b}, order = 10150], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.LoadBalancerServiceInstanceCookieFilter@16c3ca31}, order = 10151], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.WebsocketRoutingFilter@48ea2003}, order = **********], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.NettyRoutingFilter@2d195ee4}, order = **********], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.ForwardRoutingFilter@4e38d975}, order = **********]]
2025-06-24 14:57:58.556 DEBUG 21607 --- [ctor-http-nio-4] o.s.c.g.handler.FilteringWebHandler      : Sorted gatewayFilterFactories: [[GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.RemoveCachedBodyFilter@21ab988f}, order = -**********], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.AdaptCachedBodyGlobalFilter@34237b90}, order = -**********], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.NettyWriteResponseFilter@2d6aca33}, order = -1], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.ForwardPathFilter@35f8a9d3}, order = 0], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.GatewayMetricsFilter@6b1e7ad3}, order = 0], [[StripPrefix parts = 1], order = 1], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.RouteToRequestUrlFilter@29314cc9}, order = 10000], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.ReactiveLoadBalancerClientFilter@62ddd21b}, order = 10150], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.LoadBalancerServiceInstanceCookieFilter@16c3ca31}, order = 10151], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.WebsocketRoutingFilter@48ea2003}, order = **********], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.NettyRoutingFilter@2d195ee4}, order = **********], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.ForwardRoutingFilter@4e38d975}, order = **********]]
2025-06-24 14:57:58.556 DEBUG 21607 --- [ctor-http-nio-3] o.s.c.g.handler.FilteringWebHandler      : Sorted gatewayFilterFactories: [[GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.RemoveCachedBodyFilter@21ab988f}, order = -**********], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.AdaptCachedBodyGlobalFilter@34237b90}, order = -**********], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.NettyWriteResponseFilter@2d6aca33}, order = -1], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.ForwardPathFilter@35f8a9d3}, order = 0], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.GatewayMetricsFilter@6b1e7ad3}, order = 0], [[StripPrefix parts = 1], order = 1], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.RouteToRequestUrlFilter@29314cc9}, order = 10000], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.ReactiveLoadBalancerClientFilter@62ddd21b}, order = 10150], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.LoadBalancerServiceInstanceCookieFilter@16c3ca31}, order = 10151], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.WebsocketRoutingFilter@48ea2003}, order = **********], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.NettyRoutingFilter@2d195ee4}, order = **********], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.ForwardRoutingFilter@4e38d975}, order = **********]]
2025-06-24 14:57:58.577 DEBUG 21607 --- [ctor-http-nio-7] o.s.c.g.h.RoutePredicateHandlerMapping   : Route matched: game-user
2025-06-24 14:57:58.577 DEBUG 21607 --- [ctor-http-nio-7] o.s.c.g.h.RoutePredicateHandlerMapping   : Mapping [Exchange: GET http://localhost:8080/user/api/user/aggregation/profile/normal_user] to Route{id='game-user', uri=lb://game-user, order=0, predicate=Paths: [/user/**], match trailing slash: true, gatewayFilters=[[[StripPrefix parts = 1], order = 1]], metadata={}}
2025-06-24 14:57:58.577 DEBUG 21607 --- [ctor-http-nio-7] o.s.c.g.h.RoutePredicateHandlerMapping   : [9a38ba45-22] Mapped to org.springframework.cloud.gateway.handler.FilteringWebHandler@6ba838f1
2025-06-24 14:57:58.577 DEBUG 21607 --- [ctor-http-nio-7] o.s.c.g.handler.FilteringWebHandler      : Sorted gatewayFilterFactories: [[GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.RemoveCachedBodyFilter@21ab988f}, order = -**********], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.AdaptCachedBodyGlobalFilter@34237b90}, order = -**********], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.NettyWriteResponseFilter@2d6aca33}, order = -1], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.ForwardPathFilter@35f8a9d3}, order = 0], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.GatewayMetricsFilter@6b1e7ad3}, order = 0], [[StripPrefix parts = 1], order = 1], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.RouteToRequestUrlFilter@29314cc9}, order = 10000], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.ReactiveLoadBalancerClientFilter@62ddd21b}, order = 10150], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.LoadBalancerServiceInstanceCookieFilter@16c3ca31}, order = 10151], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.WebsocketRoutingFilter@48ea2003}, order = **********], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.NettyRoutingFilter@2d195ee4}, order = **********], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.ForwardRoutingFilter@4e38d975}, order = **********]]
2025-06-24 14:57:58.629 DEBUG 21607 --- [ctor-http-nio-8] o.s.c.g.h.RoutePredicateHandlerMapping   : Route matched: game-user
2025-06-24 14:57:58.629 DEBUG 21607 --- [ctor-http-nio-8] o.s.c.g.h.RoutePredicateHandlerMapping   : Mapping [Exchange: GET http://localhost:8080/user/api/user/aggregation/profile/non_existent_user] to Route{id='game-user', uri=lb://game-user, order=0, predicate=Paths: [/user/**], match trailing slash: true, gatewayFilters=[[[StripPrefix parts = 1], order = 1]], metadata={}}
2025-06-24 14:57:58.629 DEBUG 21607 --- [ctor-http-nio-8] o.s.c.g.h.RoutePredicateHandlerMapping   : [bdef4507-23] Mapped to org.springframework.cloud.gateway.handler.FilteringWebHandler@6ba838f1
2025-06-24 14:57:58.629 DEBUG 21607 --- [ctor-http-nio-8] o.s.c.g.handler.FilteringWebHandler      : Sorted gatewayFilterFactories: [[GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.RemoveCachedBodyFilter@21ab988f}, order = -**********], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.AdaptCachedBodyGlobalFilter@34237b90}, order = -**********], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.NettyWriteResponseFilter@2d6aca33}, order = -1], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.ForwardPathFilter@35f8a9d3}, order = 0], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.GatewayMetricsFilter@6b1e7ad3}, order = 0], [[StripPrefix parts = 1], order = 1], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.RouteToRequestUrlFilter@29314cc9}, order = 10000], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.ReactiveLoadBalancerClientFilter@62ddd21b}, order = 10150], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.LoadBalancerServiceInstanceCookieFilter@16c3ca31}, order = 10151], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.WebsocketRoutingFilter@48ea2003}, order = **********], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.NettyRoutingFilter@2d195ee4}, order = **********], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.ForwardRoutingFilter@4e38d975}, order = **********]]
2025-06-24 14:57:58.670 DEBUG 21607 --- [ctor-http-nio-9] o.s.c.g.h.RoutePredicateHandlerMapping   : Route matched: game-user
2025-06-24 14:57:58.671 DEBUG 21607 --- [ctor-http-nio-9] o.s.c.g.h.RoutePredicateHandlerMapping   : Mapping [Exchange: GET http://localhost:8080/user/api/user/aggregation/profile/user@] to Route{id='game-user', uri=lb://game-user, order=0, predicate=Paths: [/user/**], match trailing slash: true, gatewayFilters=[[[StripPrefix parts = 1], order = 1]], metadata={}}
2025-06-24 14:57:58.671 DEBUG 21607 --- [ctor-http-nio-9] o.s.c.g.h.RoutePredicateHandlerMapping   : [bf1ef9fe-24] Mapped to org.springframework.cloud.gateway.handler.FilteringWebHandler@6ba838f1
2025-06-24 14:57:58.671 DEBUG 21607 --- [ctor-http-nio-9] o.s.c.g.handler.FilteringWebHandler      : Sorted gatewayFilterFactories: [[GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.RemoveCachedBodyFilter@21ab988f}, order = -**********], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.AdaptCachedBodyGlobalFilter@34237b90}, order = -**********], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.NettyWriteResponseFilter@2d6aca33}, order = -1], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.ForwardPathFilter@35f8a9d3}, order = 0], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.GatewayMetricsFilter@6b1e7ad3}, order = 0], [[StripPrefix parts = 1], order = 1], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.RouteToRequestUrlFilter@29314cc9}, order = 10000], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.ReactiveLoadBalancerClientFilter@62ddd21b}, order = 10150], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.LoadBalancerServiceInstanceCookieFilter@16c3ca31}, order = 10151], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.WebsocketRoutingFilter@48ea2003}, order = **********], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.NettyRoutingFilter@2d195ee4}, order = **********], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.ForwardRoutingFilter@4e38d975}, order = **********]]
2025-06-24 14:57:58.717 DEBUG 21607 --- [tor-http-nio-10] o.s.c.g.h.RoutePredicateHandlerMapping   : Route matched: game-user
2025-06-24 14:57:58.717 DEBUG 21607 --- [tor-http-nio-10] o.s.c.g.h.RoutePredicateHandlerMapping   : Mapping [Exchange: GET http://localhost:8080/user/api/user/aggregation/profile/perf_test_user_1] to Route{id='game-user', uri=lb://game-user, order=0, predicate=Paths: [/user/**], match trailing slash: true, gatewayFilters=[[[StripPrefix parts = 1], order = 1]], metadata={}}
2025-06-24 14:57:58.717 DEBUG 21607 --- [tor-http-nio-10] o.s.c.g.h.RoutePredicateHandlerMapping   : [e8f1825b-25] Mapped to org.springframework.cloud.gateway.handler.FilteringWebHandler@6ba838f1
2025-06-24 14:57:58.717 DEBUG 21607 --- [tor-http-nio-10] o.s.c.g.handler.FilteringWebHandler      : Sorted gatewayFilterFactories: [[GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.RemoveCachedBodyFilter@21ab988f}, order = -**********], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.AdaptCachedBodyGlobalFilter@34237b90}, order = -**********], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.NettyWriteResponseFilter@2d6aca33}, order = -1], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.ForwardPathFilter@35f8a9d3}, order = 0], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.GatewayMetricsFilter@6b1e7ad3}, order = 0], [[StripPrefix parts = 1], order = 1], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.RouteToRequestUrlFilter@29314cc9}, order = 10000], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.ReactiveLoadBalancerClientFilter@62ddd21b}, order = 10150], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.LoadBalancerServiceInstanceCookieFilter@16c3ca31}, order = 10151], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.WebsocketRoutingFilter@48ea2003}, order = **********], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.NettyRoutingFilter@2d195ee4}, order = **********], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.ForwardRoutingFilter@4e38d975}, order = **********]]
2025-06-24 14:57:58.732 DEBUG 21607 --- [ctor-http-nio-1] o.s.c.g.h.RoutePredicateHandlerMapping   : Route matched: game-user
2025-06-24 14:57:58.732 DEBUG 21607 --- [ctor-http-nio-1] o.s.c.g.h.RoutePredicateHandlerMapping   : Mapping [Exchange: GET http://localhost:8080/user/api/user/aggregation/profile/perf_test_user_2] to Route{id='game-user', uri=lb://game-user, order=0, predicate=Paths: [/user/**], match trailing slash: true, gatewayFilters=[[[StripPrefix parts = 1], order = 1]], metadata={}}
2025-06-24 14:57:58.732 DEBUG 21607 --- [ctor-http-nio-1] o.s.c.g.h.RoutePredicateHandlerMapping   : [6551085b-26] Mapped to org.springframework.cloud.gateway.handler.FilteringWebHandler@6ba838f1
2025-06-24 14:57:58.732 DEBUG 21607 --- [ctor-http-nio-1] o.s.c.g.handler.FilteringWebHandler      : Sorted gatewayFilterFactories: [[GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.RemoveCachedBodyFilter@21ab988f}, order = -**********], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.AdaptCachedBodyGlobalFilter@34237b90}, order = -**********], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.NettyWriteResponseFilter@2d6aca33}, order = -1], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.ForwardPathFilter@35f8a9d3}, order = 0], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.GatewayMetricsFilter@6b1e7ad3}, order = 0], [[StripPrefix parts = 1], order = 1], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.RouteToRequestUrlFilter@29314cc9}, order = 10000], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.ReactiveLoadBalancerClientFilter@62ddd21b}, order = 10150], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.LoadBalancerServiceInstanceCookieFilter@16c3ca31}, order = 10151], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.WebsocketRoutingFilter@48ea2003}, order = **********], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.NettyRoutingFilter@2d195ee4}, order = **********], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.ForwardRoutingFilter@4e38d975}, order = **********]]
2025-06-24 14:57:58.747 DEBUG 21607 --- [ctor-http-nio-2] o.s.c.g.h.RoutePredicateHandlerMapping   : Route matched: game-user
2025-06-24 14:57:58.747 DEBUG 21607 --- [ctor-http-nio-2] o.s.c.g.h.RoutePredicateHandlerMapping   : Mapping [Exchange: GET http://localhost:8080/user/api/user/aggregation/profile/perf_test_user_3] to Route{id='game-user', uri=lb://game-user, order=0, predicate=Paths: [/user/**], match trailing slash: true, gatewayFilters=[[[StripPrefix parts = 1], order = 1]], metadata={}}
2025-06-24 14:57:58.748 DEBUG 21607 --- [ctor-http-nio-2] o.s.c.g.h.RoutePredicateHandlerMapping   : [d0e1e4d3-27] Mapped to org.springframework.cloud.gateway.handler.FilteringWebHandler@6ba838f1
2025-06-24 14:57:58.748 DEBUG 21607 --- [ctor-http-nio-2] o.s.c.g.handler.FilteringWebHandler      : Sorted gatewayFilterFactories: [[GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.RemoveCachedBodyFilter@21ab988f}, order = -**********], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.AdaptCachedBodyGlobalFilter@34237b90}, order = -**********], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.NettyWriteResponseFilter@2d6aca33}, order = -1], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.ForwardPathFilter@35f8a9d3}, order = 0], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.GatewayMetricsFilter@6b1e7ad3}, order = 0], [[StripPrefix parts = 1], order = 1], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.RouteToRequestUrlFilter@29314cc9}, order = 10000], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.ReactiveLoadBalancerClientFilter@62ddd21b}, order = 10150], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.LoadBalancerServiceInstanceCookieFilter@16c3ca31}, order = 10151], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.WebsocketRoutingFilter@48ea2003}, order = **********], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.NettyRoutingFilter@2d195ee4}, order = **********], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.ForwardRoutingFilter@4e38d975}, order = **********]]
2025-06-24 14:57:58.762 DEBUG 21607 --- [ctor-http-nio-3] o.s.c.g.h.RoutePredicateHandlerMapping   : Route matched: game-user
2025-06-24 14:57:58.762 DEBUG 21607 --- [ctor-http-nio-3] o.s.c.g.h.RoutePredicateHandlerMapping   : Mapping [Exchange: GET http://localhost:8080/user/api/user/aggregation/profile/perf_test_user_4] to Route{id='game-user', uri=lb://game-user, order=0, predicate=Paths: [/user/**], match trailing slash: true, gatewayFilters=[[[StripPrefix parts = 1], order = 1]], metadata={}}
2025-06-24 14:57:58.762 DEBUG 21607 --- [ctor-http-nio-3] o.s.c.g.h.RoutePredicateHandlerMapping   : [e22ac67b-28] Mapped to org.springframework.cloud.gateway.handler.FilteringWebHandler@6ba838f1
2025-06-24 14:57:58.762 DEBUG 21607 --- [ctor-http-nio-3] o.s.c.g.handler.FilteringWebHandler      : Sorted gatewayFilterFactories: [[GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.RemoveCachedBodyFilter@21ab988f}, order = -**********], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.AdaptCachedBodyGlobalFilter@34237b90}, order = -**********], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.NettyWriteResponseFilter@2d6aca33}, order = -1], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.ForwardPathFilter@35f8a9d3}, order = 0], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.GatewayMetricsFilter@6b1e7ad3}, order = 0], [[StripPrefix parts = 1], order = 1], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.RouteToRequestUrlFilter@29314cc9}, order = 10000], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.ReactiveLoadBalancerClientFilter@62ddd21b}, order = 10150], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.LoadBalancerServiceInstanceCookieFilter@16c3ca31}, order = 10151], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.WebsocketRoutingFilter@48ea2003}, order = **********], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.NettyRoutingFilter@2d195ee4}, order = **********], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.ForwardRoutingFilter@4e38d975}, order = **********]]
2025-06-24 14:57:58.777 DEBUG 21607 --- [ctor-http-nio-4] o.s.c.g.h.RoutePredicateHandlerMapping   : Route matched: game-user
2025-06-24 14:57:58.777 DEBUG 21607 --- [ctor-http-nio-4] o.s.c.g.h.RoutePredicateHandlerMapping   : Mapping [Exchange: GET http://localhost:8080/user/api/user/aggregation/profile/perf_test_user_5] to Route{id='game-user', uri=lb://game-user, order=0, predicate=Paths: [/user/**], match trailing slash: true, gatewayFilters=[[[StripPrefix parts = 1], order = 1]], metadata={}}
2025-06-24 14:57:58.777 DEBUG 21607 --- [ctor-http-nio-4] o.s.c.g.h.RoutePredicateHandlerMapping   : [b29c1c43-29] Mapped to org.springframework.cloud.gateway.handler.FilteringWebHandler@6ba838f1
2025-06-24 14:57:58.777 DEBUG 21607 --- [ctor-http-nio-4] o.s.c.g.handler.FilteringWebHandler      : Sorted gatewayFilterFactories: [[GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.RemoveCachedBodyFilter@21ab988f}, order = -**********], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.AdaptCachedBodyGlobalFilter@34237b90}, order = -**********], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.NettyWriteResponseFilter@2d6aca33}, order = -1], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.ForwardPathFilter@35f8a9d3}, order = 0], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.GatewayMetricsFilter@6b1e7ad3}, order = 0], [[StripPrefix parts = 1], order = 1], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.RouteToRequestUrlFilter@29314cc9}, order = 10000], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.ReactiveLoadBalancerClientFilter@62ddd21b}, order = 10150], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.LoadBalancerServiceInstanceCookieFilter@16c3ca31}, order = 10151], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.WebsocketRoutingFilter@48ea2003}, order = **********], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.NettyRoutingFilter@2d195ee4}, order = **********], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.ForwardRoutingFilter@4e38d975}, order = **********]]
2025-06-24 14:57:58.792 DEBUG 21607 --- [ctor-http-nio-5] o.s.c.g.h.RoutePredicateHandlerMapping   : Route matched: game-user
2025-06-24 14:57:58.792 DEBUG 21607 --- [ctor-http-nio-5] o.s.c.g.h.RoutePredicateHandlerMapping   : Mapping [Exchange: GET http://localhost:8080/user/api/user/aggregation/profile/perf_test_user_6] to Route{id='game-user', uri=lb://game-user, order=0, predicate=Paths: [/user/**], match trailing slash: true, gatewayFilters=[[[StripPrefix parts = 1], order = 1]], metadata={}}
2025-06-24 14:57:58.792 DEBUG 21607 --- [ctor-http-nio-5] o.s.c.g.h.RoutePredicateHandlerMapping   : [6ea7f0fe-30] Mapped to org.springframework.cloud.gateway.handler.FilteringWebHandler@6ba838f1
2025-06-24 14:57:58.792 DEBUG 21607 --- [ctor-http-nio-5] o.s.c.g.handler.FilteringWebHandler      : Sorted gatewayFilterFactories: [[GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.RemoveCachedBodyFilter@21ab988f}, order = -**********], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.AdaptCachedBodyGlobalFilter@34237b90}, order = -**********], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.NettyWriteResponseFilter@2d6aca33}, order = -1], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.ForwardPathFilter@35f8a9d3}, order = 0], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.GatewayMetricsFilter@6b1e7ad3}, order = 0], [[StripPrefix parts = 1], order = 1], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.RouteToRequestUrlFilter@29314cc9}, order = 10000], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.ReactiveLoadBalancerClientFilter@62ddd21b}, order = 10150], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.LoadBalancerServiceInstanceCookieFilter@16c3ca31}, order = 10151], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.WebsocketRoutingFilter@48ea2003}, order = **********], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.NettyRoutingFilter@2d195ee4}, order = **********], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.ForwardRoutingFilter@4e38d975}, order = **********]]
2025-06-24 14:57:58.806 DEBUG 21607 --- [ctor-http-nio-6] o.s.c.g.h.RoutePredicateHandlerMapping   : Route matched: game-user
2025-06-24 14:57:58.806 DEBUG 21607 --- [ctor-http-nio-6] o.s.c.g.h.RoutePredicateHandlerMapping   : Mapping [Exchange: GET http://localhost:8080/user/api/user/aggregation/profile/perf_test_user_7] to Route{id='game-user', uri=lb://game-user, order=0, predicate=Paths: [/user/**], match trailing slash: true, gatewayFilters=[[[StripPrefix parts = 1], order = 1]], metadata={}}
2025-06-24 14:57:58.806 DEBUG 21607 --- [ctor-http-nio-6] o.s.c.g.h.RoutePredicateHandlerMapping   : [e302c0d1-31] Mapped to org.springframework.cloud.gateway.handler.FilteringWebHandler@6ba838f1
2025-06-24 14:57:58.806 DEBUG 21607 --- [ctor-http-nio-6] o.s.c.g.handler.FilteringWebHandler      : Sorted gatewayFilterFactories: [[GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.RemoveCachedBodyFilter@21ab988f}, order = -**********], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.AdaptCachedBodyGlobalFilter@34237b90}, order = -**********], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.NettyWriteResponseFilter@2d6aca33}, order = -1], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.ForwardPathFilter@35f8a9d3}, order = 0], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.GatewayMetricsFilter@6b1e7ad3}, order = 0], [[StripPrefix parts = 1], order = 1], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.RouteToRequestUrlFilter@29314cc9}, order = 10000], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.ReactiveLoadBalancerClientFilter@62ddd21b}, order = 10150], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.LoadBalancerServiceInstanceCookieFilter@16c3ca31}, order = 10151], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.WebsocketRoutingFilter@48ea2003}, order = **********], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.NettyRoutingFilter@2d195ee4}, order = **********], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.ForwardRoutingFilter@4e38d975}, order = **********]]
2025-06-24 14:57:58.822 DEBUG 21607 --- [ctor-http-nio-7] o.s.c.g.h.RoutePredicateHandlerMapping   : Route matched: game-user
2025-06-24 14:57:58.822 DEBUG 21607 --- [ctor-http-nio-7] o.s.c.g.h.RoutePredicateHandlerMapping   : Mapping [Exchange: GET http://localhost:8080/user/api/user/aggregation/profile/perf_test_user_8] to Route{id='game-user', uri=lb://game-user, order=0, predicate=Paths: [/user/**], match trailing slash: true, gatewayFilters=[[[StripPrefix parts = 1], order = 1]], metadata={}}
2025-06-24 14:57:58.822 DEBUG 21607 --- [ctor-http-nio-7] o.s.c.g.h.RoutePredicateHandlerMapping   : [479ef438-32] Mapped to org.springframework.cloud.gateway.handler.FilteringWebHandler@6ba838f1
2025-06-24 14:57:58.822 DEBUG 21607 --- [ctor-http-nio-7] o.s.c.g.handler.FilteringWebHandler      : Sorted gatewayFilterFactories: [[GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.RemoveCachedBodyFilter@21ab988f}, order = -**********], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.AdaptCachedBodyGlobalFilter@34237b90}, order = -**********], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.NettyWriteResponseFilter@2d6aca33}, order = -1], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.ForwardPathFilter@35f8a9d3}, order = 0], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.GatewayMetricsFilter@6b1e7ad3}, order = 0], [[StripPrefix parts = 1], order = 1], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.RouteToRequestUrlFilter@29314cc9}, order = 10000], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.ReactiveLoadBalancerClientFilter@62ddd21b}, order = 10150], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.LoadBalancerServiceInstanceCookieFilter@16c3ca31}, order = 10151], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.WebsocketRoutingFilter@48ea2003}, order = **********], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.NettyRoutingFilter@2d195ee4}, order = **********], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.ForwardRoutingFilter@4e38d975}, order = **********]]
2025-06-24 14:57:58.835 DEBUG 21607 --- [ctor-http-nio-8] o.s.c.g.h.RoutePredicateHandlerMapping   : Route matched: game-user
2025-06-24 14:57:58.836 DEBUG 21607 --- [ctor-http-nio-8] o.s.c.g.h.RoutePredicateHandlerMapping   : Mapping [Exchange: GET http://localhost:8080/user/api/user/aggregation/profile/perf_test_user_9] to Route{id='game-user', uri=lb://game-user, order=0, predicate=Paths: [/user/**], match trailing slash: true, gatewayFilters=[[[StripPrefix parts = 1], order = 1]], metadata={}}
2025-06-24 14:57:58.836 DEBUG 21607 --- [ctor-http-nio-8] o.s.c.g.h.RoutePredicateHandlerMapping   : [66d6b749-33] Mapped to org.springframework.cloud.gateway.handler.FilteringWebHandler@6ba838f1
2025-06-24 14:57:58.836 DEBUG 21607 --- [ctor-http-nio-8] o.s.c.g.handler.FilteringWebHandler      : Sorted gatewayFilterFactories: [[GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.RemoveCachedBodyFilter@21ab988f}, order = -**********], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.AdaptCachedBodyGlobalFilter@34237b90}, order = -**********], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.NettyWriteResponseFilter@2d6aca33}, order = -1], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.ForwardPathFilter@35f8a9d3}, order = 0], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.GatewayMetricsFilter@6b1e7ad3}, order = 0], [[StripPrefix parts = 1], order = 1], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.RouteToRequestUrlFilter@29314cc9}, order = 10000], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.ReactiveLoadBalancerClientFilter@62ddd21b}, order = 10150], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.LoadBalancerServiceInstanceCookieFilter@16c3ca31}, order = 10151], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.WebsocketRoutingFilter@48ea2003}, order = **********], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.NettyRoutingFilter@2d195ee4}, order = **********], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.ForwardRoutingFilter@4e38d975}, order = **********]]
2025-06-24 14:57:58.852 DEBUG 21607 --- [ctor-http-nio-9] o.s.c.g.h.RoutePredicateHandlerMapping   : Route matched: game-user
2025-06-24 14:57:58.852 DEBUG 21607 --- [ctor-http-nio-9] o.s.c.g.h.RoutePredicateHandlerMapping   : Mapping [Exchange: GET http://localhost:8080/user/api/user/aggregation/profile/perf_test_user_10] to Route{id='game-user', uri=lb://game-user, order=0, predicate=Paths: [/user/**], match trailing slash: true, gatewayFilters=[[[StripPrefix parts = 1], order = 1]], metadata={}}
2025-06-24 14:57:58.853 DEBUG 21607 --- [ctor-http-nio-9] o.s.c.g.h.RoutePredicateHandlerMapping   : [136a381f-34] Mapped to org.springframework.cloud.gateway.handler.FilteringWebHandler@6ba838f1
2025-06-24 14:57:58.853 DEBUG 21607 --- [ctor-http-nio-9] o.s.c.g.handler.FilteringWebHandler      : Sorted gatewayFilterFactories: [[GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.RemoveCachedBodyFilter@21ab988f}, order = -**********], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.AdaptCachedBodyGlobalFilter@34237b90}, order = -**********], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.NettyWriteResponseFilter@2d6aca33}, order = -1], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.ForwardPathFilter@35f8a9d3}, order = 0], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.GatewayMetricsFilter@6b1e7ad3}, order = 0], [[StripPrefix parts = 1], order = 1], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.RouteToRequestUrlFilter@29314cc9}, order = 10000], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.ReactiveLoadBalancerClientFilter@62ddd21b}, order = 10150], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.LoadBalancerServiceInstanceCookieFilter@16c3ca31}, order = 10151], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.WebsocketRoutingFilter@48ea2003}, order = **********], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.NettyRoutingFilter@2d195ee4}, order = **********], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.ForwardRoutingFilter@4e38d975}, order = **********]]
2025-06-24 14:59:23.356  WARN 21607 --- [       Thread-1] c.a.n.common.http.HttpClientBeanHolder   : [HttpClientBeanHolder] Start destroying common HttpClient
2025-06-24 14:59:23.357  WARN 21607 --- [       Thread-1] c.a.n.common.http.HttpClientBeanHolder   : [HttpClientBeanHolder] Destruction of the end
2025-06-24 14:59:23.358  WARN 21607 --- [       Thread-6] c.a.nacos.common.notify.NotifyCenter     : [NotifyCenter] Start destroying Publisher
2025-06-24 14:59:23.359  WARN 21607 --- [       Thread-6] c.a.nacos.common.notify.NotifyCenter     : [NotifyCenter] Destruction of the end
2025-06-24 14:59:23.361  INFO 21607 --- [ionShutdownHook] c.a.c.n.registry.NacosServiceRegistry    : De-registering from Nacos Server now...
2025-06-24 14:59:23.371  INFO 21607 --- [ionShutdownHook] c.a.c.n.registry.NacosServiceRegistry    : De-registration finished.
