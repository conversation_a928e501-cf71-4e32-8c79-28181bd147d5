
  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/
 :: Spring Boot ::               (v2.7.14)

2025-06-24 10:04:00.660  INFO 6026 --- [           main] com.wingame.gateway.GatewayApplication   : Starting GatewayApplication v1.0-SNAPSHOT using Java 21.0.7 on WangXiaoShundeMacBook-Pro.local with PID 6026 (/Users/<USER>/wingame/saas_spring/game-gateway/target/game-gateway-1.0-SNAPSHOT.jar started by wangxiaoshun in /Users/<USER>/wingame/saas_spring/game-gateway)
2025-06-24 10:04:00.661 DEBUG 6026 --- [           main] com.wingame.gateway.GatewayApplication   : Running with Spring Boot v2.7.14, Spring v5.3.29
2025-06-24 10:04:00.661  INFO 6026 --- [           main] com.wingame.gateway.GatewayApplication   : The following 1 profile is active: "dev"
2025-06-24 10:04:00.682  WARN 6026 --- [           main] c.a.c.n.c.NacosConfigDataLoader          : [Nacos Config] config[dataId=game-gateway.yml, group=DEFAULT_GROUP] is empty
2025-06-24 10:04:00.971  INFO 6026 --- [           main] o.s.cloud.context.scope.GenericScope     : BeanFactory id=bcdc4a0c-7507-3921-acb2-dd7cf0307de1
2025-06-24 10:04:01.006  INFO 6026 --- [           main] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-24 10:04:01.006  INFO 6026 --- [           main] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-24 10:04:01.007  INFO 6026 --- [           main] trationDelegate$BeanPostProcessorChecker : Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-24 10:04:01.234 DEBUG 6026 --- [           main] o.s.c.gateway.config.GatewayProperties   : Routes supplied from Gateway Properties: [RouteDefinition{id='game-user', predicates=[PredicateDefinition{name='Path', args={_genkey_0=/user/**}}], filters=[FilterDefinition{name='StripPrefix', args={_genkey_0=1}}], uri=lb://game-user, order=0, metadata={}}, RouteDefinition{id='game-hall', predicates=[PredicateDefinition{name='Path', args={_genkey_0=/hall/**}}], filters=[FilterDefinition{name='StripPrefix', args={_genkey_0=1}}], uri=lb://game-hall, order=0, metadata={}}, RouteDefinition{id='game-agentgame', predicates=[PredicateDefinition{name='Path', args={_genkey_0=/agentgame/**}}], filters=[FilterDefinition{name='StripPrefix', args={_genkey_0=1}}], uri=lb://game-agentgame, order=0, metadata={}}, RouteDefinition{id='game-activity', predicates=[PredicateDefinition{name='Path', args={_genkey_0=/activity/**}}], filters=[FilterDefinition{name='StripPrefix', args={_genkey_0=1}}], uri=lb://game-activity, order=0, metadata={}}, RouteDefinition{id='game-account', predicates=[PredicateDefinition{name='Path', args={_genkey_0=/account/**}}], filters=[FilterDefinition{name='StripPrefix', args={_genkey_0=1}}], uri=lb://game-account, order=0, metadata={}}]
2025-06-24 10:04:01.278  INFO 6026 --- [           main] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [After]
2025-06-24 10:04:01.278  INFO 6026 --- [           main] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Before]
2025-06-24 10:04:01.278  INFO 6026 --- [           main] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Between]
2025-06-24 10:04:01.278  INFO 6026 --- [           main] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Cookie]
2025-06-24 10:04:01.278  INFO 6026 --- [           main] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Header]
2025-06-24 10:04:01.278  INFO 6026 --- [           main] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Host]
2025-06-24 10:04:01.278  INFO 6026 --- [           main] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Method]
2025-06-24 10:04:01.278  INFO 6026 --- [           main] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Path]
2025-06-24 10:04:01.278  INFO 6026 --- [           main] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Query]
2025-06-24 10:04:01.278  INFO 6026 --- [           main] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [ReadBody]
2025-06-24 10:04:01.278  INFO 6026 --- [           main] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [RemoteAddr]
2025-06-24 10:04:01.278  INFO 6026 --- [           main] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [XForwardedRemoteAddr]
2025-06-24 10:04:01.278  INFO 6026 --- [           main] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Weight]
2025-06-24 10:04:01.278  INFO 6026 --- [           main] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [CloudFoundryRouteService]
2025-06-24 10:04:01.366  WARN 6026 --- [           main] iguration$LoadBalancerCaffeineWarnLogger : Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-06-24 10:04:01.405  INFO 6026 --- [           main] o.s.b.web.embedded.netty.NettyWebServer  : Netty started on port 8080
2025-06-24 10:04:01.414  INFO 6026 --- [           main] c.a.n.p.a.s.c.ClientAuthPluginManager    : [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-06-24 10:04:01.414  INFO 6026 --- [           main] c.a.n.p.a.s.c.ClientAuthPluginManager    : [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-06-24 10:04:01.551  INFO 6026 --- [           main] c.a.c.n.registry.NacosServiceRegistry    : nacos registry, DEFAULT_GROUP game-gateway 192.168.2.85:8080 register finished
2025-06-24 10:04:01.660 DEBUG 6026 --- [oundedElastic-5] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-user applying {_genkey_0=/user/**} to Path
2025-06-24 10:04:01.662 DEBUG 6026 --- [undedElastic-12] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-user applying {_genkey_0=/user/**} to Path
2025-06-24 10:04:01.672 DEBUG 6026 --- [undedElastic-12] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-user applying filter {_genkey_0=1} to StripPrefix
2025-06-24 10:04:01.672 DEBUG 6026 --- [oundedElastic-5] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-user applying filter {_genkey_0=1} to StripPrefix
2025-06-24 10:04:01.676 DEBUG 6026 --- [undedElastic-12] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-user
2025-06-24 10:04:01.676 DEBUG 6026 --- [oundedElastic-5] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-user
2025-06-24 10:04:01.676 DEBUG 6026 --- [undedElastic-12] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-hall applying {_genkey_0=/hall/**} to Path
2025-06-24 10:04:01.676 DEBUG 6026 --- [oundedElastic-5] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-hall applying {_genkey_0=/hall/**} to Path
2025-06-24 10:04:01.677 DEBUG 6026 --- [oundedElastic-5] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-hall applying filter {_genkey_0=1} to StripPrefix
2025-06-24 10:04:01.677 DEBUG 6026 --- [undedElastic-12] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-hall applying filter {_genkey_0=1} to StripPrefix
2025-06-24 10:04:01.677 DEBUG 6026 --- [oundedElastic-5] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-hall
2025-06-24 10:04:01.677 DEBUG 6026 --- [undedElastic-12] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-hall
2025-06-24 10:04:01.677 DEBUG 6026 --- [oundedElastic-5] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-agentgame applying {_genkey_0=/agentgame/**} to Path
2025-06-24 10:04:01.677 DEBUG 6026 --- [undedElastic-12] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-agentgame applying {_genkey_0=/agentgame/**} to Path
2025-06-24 10:04:01.677 DEBUG 6026 --- [undedElastic-12] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-agentgame applying filter {_genkey_0=1} to StripPrefix
2025-06-24 10:04:01.677 DEBUG 6026 --- [oundedElastic-5] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-agentgame applying filter {_genkey_0=1} to StripPrefix
2025-06-24 10:04:01.677 DEBUG 6026 --- [undedElastic-12] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-agentgame
2025-06-24 10:04:01.677 DEBUG 6026 --- [oundedElastic-5] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-agentgame
2025-06-24 10:04:01.678 DEBUG 6026 --- [oundedElastic-5] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-activity applying {_genkey_0=/activity/**} to Path
2025-06-24 10:04:01.678 DEBUG 6026 --- [undedElastic-12] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-activity applying {_genkey_0=/activity/**} to Path
2025-06-24 10:04:01.678 DEBUG 6026 --- [oundedElastic-5] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-activity applying filter {_genkey_0=1} to StripPrefix
2025-06-24 10:04:01.678 DEBUG 6026 --- [undedElastic-12] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-activity applying filter {_genkey_0=1} to StripPrefix
2025-06-24 10:04:01.678 DEBUG 6026 --- [oundedElastic-5] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-activity
2025-06-24 10:04:01.678 DEBUG 6026 --- [undedElastic-12] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-activity
2025-06-24 10:04:01.678 DEBUG 6026 --- [oundedElastic-5] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-account applying {_genkey_0=/account/**} to Path
2025-06-24 10:04:01.678 DEBUG 6026 --- [undedElastic-12] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-account applying {_genkey_0=/account/**} to Path
2025-06-24 10:04:01.678 DEBUG 6026 --- [oundedElastic-5] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-account applying filter {_genkey_0=1} to StripPrefix
2025-06-24 10:04:01.678 DEBUG 6026 --- [undedElastic-12] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-account applying filter {_genkey_0=1} to StripPrefix
2025-06-24 10:04:01.678 DEBUG 6026 --- [oundedElastic-5] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-account
2025-06-24 10:04:01.679 DEBUG 6026 --- [undedElastic-12] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-account
2025-06-24 10:04:01.679  INFO 6026 --- [           main] a.c.n.d.GatewayLocatorHeartBeatPublisher : Start nacos gateway locator heartBeat task scheduler.
2025-06-24 10:04:01.686 DEBUG 6026 --- [oundedElastic-7] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-user applying {_genkey_0=/user/**} to Path
2025-06-24 10:04:01.686 DEBUG 6026 --- [oundedElastic-7] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-user applying filter {_genkey_0=1} to StripPrefix
2025-06-24 10:04:01.687  INFO 6026 --- [           main] com.wingame.gateway.GatewayApplication   : Started GatewayApplication in 6.885 seconds (JVM running for 7.083)
2025-06-24 10:04:01.687 DEBUG 6026 --- [oundedElastic-7] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-user
2025-06-24 10:04:01.687 DEBUG 6026 --- [oundedElastic-7] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-hall applying {_genkey_0=/hall/**} to Path
2025-06-24 10:04:01.688 DEBUG 6026 --- [oundedElastic-7] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-hall applying filter {_genkey_0=1} to StripPrefix
2025-06-24 10:04:01.688 DEBUG 6026 --- [oundedElastic-7] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-hall
2025-06-24 10:04:01.688 DEBUG 6026 --- [oundedElastic-7] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-agentgame applying {_genkey_0=/agentgame/**} to Path
2025-06-24 10:04:01.688 DEBUG 6026 --- [oundedElastic-7] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-agentgame applying filter {_genkey_0=1} to StripPrefix
2025-06-24 10:04:01.689 DEBUG 6026 --- [oundedElastic-7] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-agentgame
2025-06-24 10:04:01.689 DEBUG 6026 --- [oundedElastic-7] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-activity applying {_genkey_0=/activity/**} to Path
2025-06-24 10:04:01.689 DEBUG 6026 --- [oundedElastic-7] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-activity applying filter {_genkey_0=1} to StripPrefix
2025-06-24 10:04:01.689 DEBUG 6026 --- [oundedElastic-5] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-user applying {_genkey_0=/user/**} to Path
2025-06-24 10:04:01.689 DEBUG 6026 --- [oundedElastic-7] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-activity
2025-06-24 10:04:01.689 DEBUG 6026 --- [oundedElastic-7] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-account applying {_genkey_0=/account/**} to Path
2025-06-24 10:04:01.689 DEBUG 6026 --- [oundedElastic-7] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-account applying filter {_genkey_0=1} to StripPrefix
2025-06-24 10:04:01.690 DEBUG 6026 --- [oundedElastic-7] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-account
2025-06-24 10:04:01.690 DEBUG 6026 --- [oundedElastic-5] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-user applying filter {_genkey_0=1} to StripPrefix
2025-06-24 10:04:01.690 DEBUG 6026 --- [oundedElastic-5] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-user
2025-06-24 10:04:01.690 DEBUG 6026 --- [oundedElastic-5] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-hall applying {_genkey_0=/hall/**} to Path
2025-06-24 10:04:01.691 DEBUG 6026 --- [oundedElastic-5] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-hall applying filter {_genkey_0=1} to StripPrefix
2025-06-24 10:04:01.691 DEBUG 6026 --- [oundedElastic-5] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-hall
2025-06-24 10:04:01.691 DEBUG 6026 --- [oundedElastic-5] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-agentgame applying {_genkey_0=/agentgame/**} to Path
2025-06-24 10:04:01.691 DEBUG 6026 --- [oundedElastic-5] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-agentgame applying filter {_genkey_0=1} to StripPrefix
2025-06-24 10:04:01.691  INFO 6026 --- [           main] c.a.c.n.refresh.NacosContextRefresher    : [Nacos Config] Listening config: dataId=game-gateway.yml, group=DEFAULT_GROUP
2025-06-24 10:04:01.692 DEBUG 6026 --- [oundedElastic-5] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-agentgame
2025-06-24 10:04:01.692 DEBUG 6026 --- [oundedElastic-5] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-activity applying {_genkey_0=/activity/**} to Path
2025-06-24 10:04:01.692 DEBUG 6026 --- [oundedElastic-5] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-activity applying filter {_genkey_0=1} to StripPrefix
2025-06-24 10:04:01.693 DEBUG 6026 --- [oundedElastic-5] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-activity
2025-06-24 10:04:01.693 DEBUG 6026 --- [oundedElastic-5] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-account applying {_genkey_0=/account/**} to Path
2025-06-24 10:04:01.693 DEBUG 6026 --- [oundedElastic-5] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-account applying filter {_genkey_0=1} to StripPrefix
2025-06-24 10:04:01.693 DEBUG 6026 --- [oundedElastic-5] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-account
2025-06-24 10:04:31.708 DEBUG 6026 --- [oundedElastic-9] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-gateway applying {pattern=/game-gateway/**} to Path
2025-06-24 10:04:31.709 DEBUG 6026 --- [oundedElastic-9] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-gateway applying filter {regexp=/game-gateway/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
2025-06-24 10:04:31.719 DEBUG 6026 --- [oundedElastic-9] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: ReactiveCompositeDiscoveryClient_game-gateway
2025-06-24 10:04:31.719 DEBUG 6026 --- [oundedElastic-9] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-user applying {_genkey_0=/user/**} to Path
2025-06-24 10:04:31.720 DEBUG 6026 --- [oundedElastic-9] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-user applying filter {_genkey_0=1} to StripPrefix
2025-06-24 10:04:31.720 DEBUG 6026 --- [oundedElastic-9] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-user
2025-06-24 10:04:31.720 DEBUG 6026 --- [oundedElastic-9] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-hall applying {_genkey_0=/hall/**} to Path
2025-06-24 10:04:31.721 DEBUG 6026 --- [oundedElastic-9] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-hall applying filter {_genkey_0=1} to StripPrefix
2025-06-24 10:04:31.721 DEBUG 6026 --- [oundedElastic-9] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-hall
2025-06-24 10:04:31.721 DEBUG 6026 --- [oundedElastic-9] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-agentgame applying {_genkey_0=/agentgame/**} to Path
2025-06-24 10:04:31.721 DEBUG 6026 --- [oundedElastic-9] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-agentgame applying filter {_genkey_0=1} to StripPrefix
2025-06-24 10:04:31.722 DEBUG 6026 --- [oundedElastic-9] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-agentgame
2025-06-24 10:04:31.722 DEBUG 6026 --- [oundedElastic-9] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-activity applying {_genkey_0=/activity/**} to Path
2025-06-24 10:04:31.723 DEBUG 6026 --- [oundedElastic-9] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-activity applying filter {_genkey_0=1} to StripPrefix
2025-06-24 10:04:31.723 DEBUG 6026 --- [oundedElastic-9] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-activity
2025-06-24 10:04:31.723 DEBUG 6026 --- [oundedElastic-9] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-account applying {_genkey_0=/account/**} to Path
2025-06-24 10:04:31.724 DEBUG 6026 --- [oundedElastic-9] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-account applying filter {_genkey_0=1} to StripPrefix
2025-06-24 10:04:31.724 DEBUG 6026 --- [oundedElastic-9] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-account
2025-06-24 10:05:01.729 DEBUG 6026 --- [oundedElastic-7] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-gateway applying {pattern=/game-gateway/**} to Path
2025-06-24 10:05:01.731 DEBUG 6026 --- [oundedElastic-7] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-gateway applying filter {regexp=/game-gateway/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
2025-06-24 10:05:01.732 DEBUG 6026 --- [oundedElastic-7] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: ReactiveCompositeDiscoveryClient_game-gateway
2025-06-24 10:05:01.732 DEBUG 6026 --- [oundedElastic-7] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-user applying {_genkey_0=/user/**} to Path
2025-06-24 10:05:01.733 DEBUG 6026 --- [oundedElastic-7] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-user applying filter {_genkey_0=1} to StripPrefix
2025-06-24 10:05:01.734 DEBUG 6026 --- [oundedElastic-7] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-user
2025-06-24 10:05:01.734 DEBUG 6026 --- [oundedElastic-7] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-hall applying {_genkey_0=/hall/**} to Path
2025-06-24 10:05:01.735 DEBUG 6026 --- [oundedElastic-7] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-hall applying filter {_genkey_0=1} to StripPrefix
2025-06-24 10:05:01.735 DEBUG 6026 --- [oundedElastic-7] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-hall
2025-06-24 10:05:01.735 DEBUG 6026 --- [oundedElastic-7] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-agentgame applying {_genkey_0=/agentgame/**} to Path
2025-06-24 10:05:01.736 DEBUG 6026 --- [oundedElastic-7] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-agentgame applying filter {_genkey_0=1} to StripPrefix
2025-06-24 10:05:01.737 DEBUG 6026 --- [oundedElastic-7] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-agentgame
2025-06-24 10:05:01.737 DEBUG 6026 --- [oundedElastic-7] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-activity applying {_genkey_0=/activity/**} to Path
2025-06-24 10:05:01.737 DEBUG 6026 --- [oundedElastic-7] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-activity applying filter {_genkey_0=1} to StripPrefix
2025-06-24 10:05:01.738 DEBUG 6026 --- [oundedElastic-7] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-activity
2025-06-24 10:05:01.738 DEBUG 6026 --- [oundedElastic-7] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-account applying {_genkey_0=/account/**} to Path
2025-06-24 10:05:01.738 DEBUG 6026 --- [oundedElastic-7] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-account applying filter {_genkey_0=1} to StripPrefix
2025-06-24 10:05:01.738 DEBUG 6026 --- [oundedElastic-7] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-account
2025-06-24 10:05:31.711 DEBUG 6026 --- [oundedElastic-9] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-user applying {pattern=/game-user/**} to Path
2025-06-24 10:05:31.713 DEBUG 6026 --- [oundedElastic-9] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-user applying filter {regexp=/game-user/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
2025-06-24 10:05:31.714 DEBUG 6026 --- [oundedElastic-9] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: ReactiveCompositeDiscoveryClient_game-user
2025-06-24 10:05:31.715 DEBUG 6026 --- [oundedElastic-9] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-gateway applying {pattern=/game-gateway/**} to Path
2025-06-24 10:05:31.716 DEBUG 6026 --- [oundedElastic-9] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-gateway applying filter {regexp=/game-gateway/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
2025-06-24 10:05:31.717 DEBUG 6026 --- [oundedElastic-9] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: ReactiveCompositeDiscoveryClient_game-gateway
2025-06-24 10:05:31.717 DEBUG 6026 --- [oundedElastic-9] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-user applying {_genkey_0=/user/**} to Path
2025-06-24 10:05:31.718 DEBUG 6026 --- [oundedElastic-9] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-user applying filter {_genkey_0=1} to StripPrefix
2025-06-24 10:05:31.719 DEBUG 6026 --- [oundedElastic-9] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-user
2025-06-24 10:05:31.719 DEBUG 6026 --- [oundedElastic-9] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-hall applying {_genkey_0=/hall/**} to Path
2025-06-24 10:05:31.720 DEBUG 6026 --- [oundedElastic-9] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-hall applying filter {_genkey_0=1} to StripPrefix
2025-06-24 10:05:31.720 DEBUG 6026 --- [oundedElastic-9] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-hall
2025-06-24 10:05:31.721 DEBUG 6026 --- [oundedElastic-9] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-agentgame applying {_genkey_0=/agentgame/**} to Path
2025-06-24 10:05:31.721 DEBUG 6026 --- [oundedElastic-9] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-agentgame applying filter {_genkey_0=1} to StripPrefix
2025-06-24 10:05:31.722 DEBUG 6026 --- [oundedElastic-9] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-agentgame
2025-06-24 10:05:31.722 DEBUG 6026 --- [oundedElastic-9] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-activity applying {_genkey_0=/activity/**} to Path
2025-06-24 10:05:31.722 DEBUG 6026 --- [oundedElastic-9] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-activity applying filter {_genkey_0=1} to StripPrefix
2025-06-24 10:05:31.723 DEBUG 6026 --- [oundedElastic-9] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-activity
2025-06-24 10:05:31.723 DEBUG 6026 --- [oundedElastic-9] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-account applying {_genkey_0=/account/**} to Path
2025-06-24 10:05:31.724 DEBUG 6026 --- [oundedElastic-9] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-account applying filter {_genkey_0=1} to StripPrefix
2025-06-24 10:05:31.724 DEBUG 6026 --- [oundedElastic-9] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-account
2025-06-24 10:06:01.717 DEBUG 6026 --- [oundedElastic-7] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-user applying {pattern=/game-user/**} to Path
2025-06-24 10:06:01.718 DEBUG 6026 --- [oundedElastic-7] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-user applying filter {regexp=/game-user/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
2025-06-24 10:06:01.719 DEBUG 6026 --- [oundedElastic-7] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: ReactiveCompositeDiscoveryClient_game-user
2025-06-24 10:06:01.720 DEBUG 6026 --- [oundedElastic-7] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-gateway applying {pattern=/game-gateway/**} to Path
2025-06-24 10:06:01.721 DEBUG 6026 --- [oundedElastic-7] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-gateway applying filter {regexp=/game-gateway/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
2025-06-24 10:06:01.722 DEBUG 6026 --- [oundedElastic-7] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: ReactiveCompositeDiscoveryClient_game-gateway
2025-06-24 10:06:01.722 DEBUG 6026 --- [oundedElastic-7] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-user applying {_genkey_0=/user/**} to Path
2025-06-24 10:06:01.723 DEBUG 6026 --- [oundedElastic-7] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-user applying filter {_genkey_0=1} to StripPrefix
2025-06-24 10:06:01.724 DEBUG 6026 --- [oundedElastic-7] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-user
2025-06-24 10:06:01.724 DEBUG 6026 --- [oundedElastic-7] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-hall applying {_genkey_0=/hall/**} to Path
2025-06-24 10:06:01.725 DEBUG 6026 --- [oundedElastic-7] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-hall applying filter {_genkey_0=1} to StripPrefix
2025-06-24 10:06:01.725 DEBUG 6026 --- [oundedElastic-7] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-hall
2025-06-24 10:06:01.725 DEBUG 6026 --- [oundedElastic-7] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-agentgame applying {_genkey_0=/agentgame/**} to Path
2025-06-24 10:06:01.726 DEBUG 6026 --- [oundedElastic-7] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-agentgame applying filter {_genkey_0=1} to StripPrefix
2025-06-24 10:06:01.727 DEBUG 6026 --- [oundedElastic-7] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-agentgame
2025-06-24 10:06:01.727 DEBUG 6026 --- [oundedElastic-7] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-activity applying {_genkey_0=/activity/**} to Path
2025-06-24 10:06:01.727 DEBUG 6026 --- [oundedElastic-7] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-activity applying filter {_genkey_0=1} to StripPrefix
2025-06-24 10:06:01.727 DEBUG 6026 --- [oundedElastic-7] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-activity
2025-06-24 10:06:01.727 DEBUG 6026 --- [oundedElastic-7] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-account applying {_genkey_0=/account/**} to Path
2025-06-24 10:06:01.728 DEBUG 6026 --- [oundedElastic-7] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-account applying filter {_genkey_0=1} to StripPrefix
2025-06-24 10:06:01.728 DEBUG 6026 --- [oundedElastic-7] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-account
2025-06-24 10:06:31.722 DEBUG 6026 --- [undedElastic-15] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-hall applying {pattern=/game-hall/**} to Path
2025-06-24 10:06:31.724 DEBUG 6026 --- [undedElastic-15] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-hall applying filter {regexp=/game-hall/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
2025-06-24 10:06:31.725 DEBUG 6026 --- [undedElastic-15] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: ReactiveCompositeDiscoveryClient_game-hall
2025-06-24 10:06:31.725 DEBUG 6026 --- [undedElastic-15] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-user applying {pattern=/game-user/**} to Path
2025-06-24 10:06:31.726 DEBUG 6026 --- [undedElastic-15] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-user applying filter {regexp=/game-user/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
2025-06-24 10:06:31.727 DEBUG 6026 --- [undedElastic-15] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: ReactiveCompositeDiscoveryClient_game-user
2025-06-24 10:06:31.727 DEBUG 6026 --- [undedElastic-15] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-gateway applying {pattern=/game-gateway/**} to Path
2025-06-24 10:06:31.728 DEBUG 6026 --- [undedElastic-15] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-gateway applying filter {regexp=/game-gateway/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
2025-06-24 10:06:31.729 DEBUG 6026 --- [undedElastic-15] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: ReactiveCompositeDiscoveryClient_game-gateway
2025-06-24 10:06:31.729 DEBUG 6026 --- [undedElastic-15] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-user applying {_genkey_0=/user/**} to Path
2025-06-24 10:06:31.729 DEBUG 6026 --- [undedElastic-15] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-user applying filter {_genkey_0=1} to StripPrefix
2025-06-24 10:06:31.730 DEBUG 6026 --- [undedElastic-15] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-user
2025-06-24 10:06:31.730 DEBUG 6026 --- [undedElastic-15] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-hall applying {_genkey_0=/hall/**} to Path
2025-06-24 10:06:31.730 DEBUG 6026 --- [undedElastic-15] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-hall applying filter {_genkey_0=1} to StripPrefix
2025-06-24 10:06:31.731 DEBUG 6026 --- [undedElastic-15] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-hall
2025-06-24 10:06:31.731 DEBUG 6026 --- [undedElastic-15] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-agentgame applying {_genkey_0=/agentgame/**} to Path
2025-06-24 10:06:31.731 DEBUG 6026 --- [undedElastic-15] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-agentgame applying filter {_genkey_0=1} to StripPrefix
2025-06-24 10:06:31.731 DEBUG 6026 --- [undedElastic-15] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-agentgame
2025-06-24 10:06:31.731 DEBUG 6026 --- [undedElastic-15] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-activity applying {_genkey_0=/activity/**} to Path
2025-06-24 10:06:31.732 DEBUG 6026 --- [undedElastic-15] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-activity applying filter {_genkey_0=1} to StripPrefix
2025-06-24 10:06:31.733 DEBUG 6026 --- [undedElastic-15] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-activity
2025-06-24 10:06:31.733 DEBUG 6026 --- [undedElastic-15] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-account applying {_genkey_0=/account/**} to Path
2025-06-24 10:06:31.733 DEBUG 6026 --- [undedElastic-15] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-account applying filter {_genkey_0=1} to StripPrefix
2025-06-24 10:06:31.734 DEBUG 6026 --- [undedElastic-15] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-account
2025-06-24 10:07:01.725 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-hall applying {pattern=/game-hall/**} to Path
2025-06-24 10:07:01.727 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-hall applying filter {regexp=/game-hall/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
2025-06-24 10:07:01.728 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: ReactiveCompositeDiscoveryClient_game-hall
2025-06-24 10:07:01.729 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-user applying {pattern=/game-user/**} to Path
2025-06-24 10:07:01.730 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-user applying filter {regexp=/game-user/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
2025-06-24 10:07:01.731 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: ReactiveCompositeDiscoveryClient_game-user
2025-06-24 10:07:01.732 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-gateway applying {pattern=/game-gateway/**} to Path
2025-06-24 10:07:01.733 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-gateway applying filter {regexp=/game-gateway/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
2025-06-24 10:07:01.733 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: ReactiveCompositeDiscoveryClient_game-gateway
2025-06-24 10:07:01.733 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-user applying {_genkey_0=/user/**} to Path
2025-06-24 10:07:01.734 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-user applying filter {_genkey_0=1} to StripPrefix
2025-06-24 10:07:01.735 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-user
2025-06-24 10:07:01.735 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-hall applying {_genkey_0=/hall/**} to Path
2025-06-24 10:07:01.736 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-hall applying filter {_genkey_0=1} to StripPrefix
2025-06-24 10:07:01.736 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-hall
2025-06-24 10:07:01.736 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-agentgame applying {_genkey_0=/agentgame/**} to Path
2025-06-24 10:07:01.737 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-agentgame applying filter {_genkey_0=1} to StripPrefix
2025-06-24 10:07:01.737 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-agentgame
2025-06-24 10:07:01.737 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-activity applying {_genkey_0=/activity/**} to Path
2025-06-24 10:07:01.738 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-activity applying filter {_genkey_0=1} to StripPrefix
2025-06-24 10:07:01.738 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-activity
2025-06-24 10:07:01.738 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-account applying {_genkey_0=/account/**} to Path
2025-06-24 10:07:01.739 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-account applying filter {_genkey_0=1} to StripPrefix
2025-06-24 10:07:01.739 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-account
2025-06-24 10:07:31.731 DEBUG 6026 --- [undedElastic-16] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-agentgame applying {pattern=/game-agentgame/**} to Path
2025-06-24 10:07:31.732 DEBUG 6026 --- [undedElastic-16] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-agentgame applying filter {regexp=/game-agentgame/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
2025-06-24 10:07:31.733 DEBUG 6026 --- [undedElastic-16] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: ReactiveCompositeDiscoveryClient_game-agentgame
2025-06-24 10:07:31.733 DEBUG 6026 --- [undedElastic-16] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-hall applying {pattern=/game-hall/**} to Path
2025-06-24 10:07:31.734 DEBUG 6026 --- [undedElastic-16] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-hall applying filter {regexp=/game-hall/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
2025-06-24 10:07:31.734 DEBUG 6026 --- [undedElastic-16] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: ReactiveCompositeDiscoveryClient_game-hall
2025-06-24 10:07:31.735 DEBUG 6026 --- [undedElastic-16] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-user applying {pattern=/game-user/**} to Path
2025-06-24 10:07:31.735 DEBUG 6026 --- [undedElastic-16] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-user applying filter {regexp=/game-user/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
2025-06-24 10:07:31.736 DEBUG 6026 --- [undedElastic-16] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: ReactiveCompositeDiscoveryClient_game-user
2025-06-24 10:07:31.736 DEBUG 6026 --- [undedElastic-16] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-gateway applying {pattern=/game-gateway/**} to Path
2025-06-24 10:07:31.738 DEBUG 6026 --- [undedElastic-16] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-gateway applying filter {regexp=/game-gateway/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
2025-06-24 10:07:31.738 DEBUG 6026 --- [undedElastic-16] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: ReactiveCompositeDiscoveryClient_game-gateway
2025-06-24 10:07:31.738 DEBUG 6026 --- [undedElastic-16] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-user applying {_genkey_0=/user/**} to Path
2025-06-24 10:07:31.738 DEBUG 6026 --- [undedElastic-16] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-user applying filter {_genkey_0=1} to StripPrefix
2025-06-24 10:07:31.739 DEBUG 6026 --- [undedElastic-16] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-user
2025-06-24 10:07:31.739 DEBUG 6026 --- [undedElastic-16] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-hall applying {_genkey_0=/hall/**} to Path
2025-06-24 10:07:31.739 DEBUG 6026 --- [undedElastic-16] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-hall applying filter {_genkey_0=1} to StripPrefix
2025-06-24 10:07:31.740 DEBUG 6026 --- [undedElastic-16] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-hall
2025-06-24 10:07:31.740 DEBUG 6026 --- [undedElastic-16] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-agentgame applying {_genkey_0=/agentgame/**} to Path
2025-06-24 10:07:31.740 DEBUG 6026 --- [undedElastic-16] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-agentgame applying filter {_genkey_0=1} to StripPrefix
2025-06-24 10:07:31.740 DEBUG 6026 --- [undedElastic-16] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-agentgame
2025-06-24 10:07:31.740 DEBUG 6026 --- [undedElastic-16] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-activity applying {_genkey_0=/activity/**} to Path
2025-06-24 10:07:31.741 DEBUG 6026 --- [undedElastic-16] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-activity applying filter {_genkey_0=1} to StripPrefix
2025-06-24 10:07:31.741 DEBUG 6026 --- [undedElastic-16] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-activity
2025-06-24 10:07:31.741 DEBUG 6026 --- [undedElastic-16] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-account applying {_genkey_0=/account/**} to Path
2025-06-24 10:07:31.742 DEBUG 6026 --- [undedElastic-16] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-account applying filter {_genkey_0=1} to StripPrefix
2025-06-24 10:07:31.742 DEBUG 6026 --- [undedElastic-16] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-account
2025-06-24 10:08:01.753 DEBUG 6026 --- [oundedElastic-9] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-agentgame applying {pattern=/game-agentgame/**} to Path
2025-06-24 10:08:01.755 DEBUG 6026 --- [oundedElastic-9] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-agentgame applying filter {regexp=/game-agentgame/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
2025-06-24 10:08:01.755 DEBUG 6026 --- [oundedElastic-9] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: ReactiveCompositeDiscoveryClient_game-agentgame
2025-06-24 10:08:01.756 DEBUG 6026 --- [oundedElastic-9] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-hall applying {pattern=/game-hall/**} to Path
2025-06-24 10:08:01.756 DEBUG 6026 --- [oundedElastic-9] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-hall applying filter {regexp=/game-hall/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
2025-06-24 10:08:01.757 DEBUG 6026 --- [oundedElastic-9] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: ReactiveCompositeDiscoveryClient_game-hall
2025-06-24 10:08:01.757 DEBUG 6026 --- [oundedElastic-9] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-user applying {pattern=/game-user/**} to Path
2025-06-24 10:08:01.757 DEBUG 6026 --- [oundedElastic-9] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-user applying filter {regexp=/game-user/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
2025-06-24 10:08:01.758 DEBUG 6026 --- [oundedElastic-9] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: ReactiveCompositeDiscoveryClient_game-user
2025-06-24 10:08:01.758 DEBUG 6026 --- [oundedElastic-9] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-gateway applying {pattern=/game-gateway/**} to Path
2025-06-24 10:08:01.759 DEBUG 6026 --- [oundedElastic-9] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-gateway applying filter {regexp=/game-gateway/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
2025-06-24 10:08:01.759 DEBUG 6026 --- [oundedElastic-9] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: ReactiveCompositeDiscoveryClient_game-gateway
2025-06-24 10:08:01.760 DEBUG 6026 --- [oundedElastic-9] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-user applying {_genkey_0=/user/**} to Path
2025-06-24 10:08:01.760 DEBUG 6026 --- [oundedElastic-9] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-user applying filter {_genkey_0=1} to StripPrefix
2025-06-24 10:08:01.760 DEBUG 6026 --- [oundedElastic-9] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-user
2025-06-24 10:08:01.760 DEBUG 6026 --- [oundedElastic-9] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-hall applying {_genkey_0=/hall/**} to Path
2025-06-24 10:08:01.761 DEBUG 6026 --- [oundedElastic-9] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-hall applying filter {_genkey_0=1} to StripPrefix
2025-06-24 10:08:01.761 DEBUG 6026 --- [oundedElastic-9] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-hall
2025-06-24 10:08:01.761 DEBUG 6026 --- [oundedElastic-9] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-agentgame applying {_genkey_0=/agentgame/**} to Path
2025-06-24 10:08:01.762 DEBUG 6026 --- [oundedElastic-9] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-agentgame applying filter {_genkey_0=1} to StripPrefix
2025-06-24 10:08:01.762 DEBUG 6026 --- [oundedElastic-9] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-agentgame
2025-06-24 10:08:01.762 DEBUG 6026 --- [oundedElastic-9] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-activity applying {_genkey_0=/activity/**} to Path
2025-06-24 10:08:01.770 DEBUG 6026 --- [oundedElastic-9] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-activity applying filter {_genkey_0=1} to StripPrefix
2025-06-24 10:08:01.770 DEBUG 6026 --- [oundedElastic-9] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-activity
2025-06-24 10:08:01.770 DEBUG 6026 --- [oundedElastic-9] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-account applying {_genkey_0=/account/**} to Path
2025-06-24 10:08:01.771 DEBUG 6026 --- [oundedElastic-9] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-account applying filter {_genkey_0=1} to StripPrefix
2025-06-24 10:08:01.772 DEBUG 6026 --- [oundedElastic-9] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-account
2025-06-24 10:08:31.735 DEBUG 6026 --- [undedElastic-15] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-agentgame applying {pattern=/game-agentgame/**} to Path
2025-06-24 10:08:31.735 DEBUG 6026 --- [undedElastic-15] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-agentgame applying filter {regexp=/game-agentgame/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
2025-06-24 10:08:31.735 DEBUG 6026 --- [undedElastic-15] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: ReactiveCompositeDiscoveryClient_game-agentgame
2025-06-24 10:08:31.736 DEBUG 6026 --- [undedElastic-15] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-hall applying {pattern=/game-hall/**} to Path
2025-06-24 10:08:31.736 DEBUG 6026 --- [undedElastic-15] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-hall applying filter {regexp=/game-hall/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
2025-06-24 10:08:31.736 DEBUG 6026 --- [undedElastic-15] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: ReactiveCompositeDiscoveryClient_game-hall
2025-06-24 10:08:31.736 DEBUG 6026 --- [undedElastic-15] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-user applying {pattern=/game-user/**} to Path
2025-06-24 10:08:31.736 DEBUG 6026 --- [undedElastic-15] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-user applying filter {regexp=/game-user/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
2025-06-24 10:08:31.736 DEBUG 6026 --- [undedElastic-15] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: ReactiveCompositeDiscoveryClient_game-user
2025-06-24 10:08:31.736 DEBUG 6026 --- [undedElastic-15] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-gateway applying {pattern=/game-gateway/**} to Path
2025-06-24 10:08:31.736 DEBUG 6026 --- [undedElastic-15] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-gateway applying filter {regexp=/game-gateway/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
2025-06-24 10:08:31.737 DEBUG 6026 --- [undedElastic-15] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: ReactiveCompositeDiscoveryClient_game-gateway
2025-06-24 10:08:31.737 DEBUG 6026 --- [undedElastic-15] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-user applying {_genkey_0=/user/**} to Path
2025-06-24 10:08:31.737 DEBUG 6026 --- [undedElastic-15] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-user applying filter {_genkey_0=1} to StripPrefix
2025-06-24 10:08:31.737 DEBUG 6026 --- [undedElastic-15] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-user
2025-06-24 10:08:31.737 DEBUG 6026 --- [undedElastic-15] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-hall applying {_genkey_0=/hall/**} to Path
2025-06-24 10:08:31.737 DEBUG 6026 --- [undedElastic-15] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-hall applying filter {_genkey_0=1} to StripPrefix
2025-06-24 10:08:31.737 DEBUG 6026 --- [undedElastic-15] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-hall
2025-06-24 10:08:31.737 DEBUG 6026 --- [undedElastic-15] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-agentgame applying {_genkey_0=/agentgame/**} to Path
2025-06-24 10:08:31.737 DEBUG 6026 --- [undedElastic-15] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-agentgame applying filter {_genkey_0=1} to StripPrefix
2025-06-24 10:08:31.737 DEBUG 6026 --- [undedElastic-15] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-agentgame
2025-06-24 10:08:31.737 DEBUG 6026 --- [undedElastic-15] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-activity applying {_genkey_0=/activity/**} to Path
2025-06-24 10:08:31.738 DEBUG 6026 --- [undedElastic-15] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-activity applying filter {_genkey_0=1} to StripPrefix
2025-06-24 10:08:31.738 DEBUG 6026 --- [undedElastic-15] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-activity
2025-06-24 10:08:31.738 DEBUG 6026 --- [undedElastic-15] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-account applying {_genkey_0=/account/**} to Path
2025-06-24 10:08:31.738 DEBUG 6026 --- [undedElastic-15] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-account applying filter {_genkey_0=1} to StripPrefix
2025-06-24 10:08:31.738 DEBUG 6026 --- [undedElastic-15] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-account
2025-06-24 10:09:01.745 DEBUG 6026 --- [undedElastic-17] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-agentgame applying {pattern=/game-agentgame/**} to Path
2025-06-24 10:09:01.747 DEBUG 6026 --- [undedElastic-17] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-agentgame applying filter {regexp=/game-agentgame/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
2025-06-24 10:09:01.748 DEBUG 6026 --- [undedElastic-17] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: ReactiveCompositeDiscoveryClient_game-agentgame
2025-06-24 10:09:01.748 DEBUG 6026 --- [undedElastic-17] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-hall applying {pattern=/game-hall/**} to Path
2025-06-24 10:09:01.749 DEBUG 6026 --- [undedElastic-17] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-hall applying filter {regexp=/game-hall/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
2025-06-24 10:09:01.749 DEBUG 6026 --- [undedElastic-17] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: ReactiveCompositeDiscoveryClient_game-hall
2025-06-24 10:09:01.749 DEBUG 6026 --- [undedElastic-17] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-user applying {pattern=/game-user/**} to Path
2025-06-24 10:09:01.750 DEBUG 6026 --- [undedElastic-17] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-user applying filter {regexp=/game-user/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
2025-06-24 10:09:01.750 DEBUG 6026 --- [undedElastic-17] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: ReactiveCompositeDiscoveryClient_game-user
2025-06-24 10:09:01.751 DEBUG 6026 --- [undedElastic-17] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-gateway applying {pattern=/game-gateway/**} to Path
2025-06-24 10:09:01.751 DEBUG 6026 --- [undedElastic-17] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-gateway applying filter {regexp=/game-gateway/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
2025-06-24 10:09:01.752 DEBUG 6026 --- [undedElastic-17] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: ReactiveCompositeDiscoveryClient_game-gateway
2025-06-24 10:09:01.752 DEBUG 6026 --- [undedElastic-17] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-activity applying {pattern=/game-activity/**} to Path
2025-06-24 10:09:01.752 DEBUG 6026 --- [undedElastic-17] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-activity applying filter {regexp=/game-activity/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
2025-06-24 10:09:01.753 DEBUG 6026 --- [undedElastic-17] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: ReactiveCompositeDiscoveryClient_game-activity
2025-06-24 10:09:01.753 DEBUG 6026 --- [undedElastic-17] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-user applying {_genkey_0=/user/**} to Path
2025-06-24 10:09:01.753 DEBUG 6026 --- [undedElastic-17] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-user applying filter {_genkey_0=1} to StripPrefix
2025-06-24 10:09:01.754 DEBUG 6026 --- [undedElastic-17] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-user
2025-06-24 10:09:01.754 DEBUG 6026 --- [undedElastic-17] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-hall applying {_genkey_0=/hall/**} to Path
2025-06-24 10:09:01.754 DEBUG 6026 --- [undedElastic-17] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-hall applying filter {_genkey_0=1} to StripPrefix
2025-06-24 10:09:01.755 DEBUG 6026 --- [undedElastic-17] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-hall
2025-06-24 10:09:01.755 DEBUG 6026 --- [undedElastic-17] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-agentgame applying {_genkey_0=/agentgame/**} to Path
2025-06-24 10:09:01.755 DEBUG 6026 --- [undedElastic-17] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-agentgame applying filter {_genkey_0=1} to StripPrefix
2025-06-24 10:09:01.755 DEBUG 6026 --- [undedElastic-17] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-agentgame
2025-06-24 10:09:01.756 DEBUG 6026 --- [undedElastic-17] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-activity applying {_genkey_0=/activity/**} to Path
2025-06-24 10:09:01.756 DEBUG 6026 --- [undedElastic-17] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-activity applying filter {_genkey_0=1} to StripPrefix
2025-06-24 10:09:01.756 DEBUG 6026 --- [undedElastic-17] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-activity
2025-06-24 10:09:01.756 DEBUG 6026 --- [undedElastic-17] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-account applying {_genkey_0=/account/**} to Path
2025-06-24 10:09:01.757 DEBUG 6026 --- [undedElastic-17] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-account applying filter {_genkey_0=1} to StripPrefix
2025-06-24 10:09:01.757 DEBUG 6026 --- [undedElastic-17] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-account
2025-06-24 10:09:31.750 DEBUG 6026 --- [undedElastic-16] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-agentgame applying {pattern=/game-agentgame/**} to Path
2025-06-24 10:09:31.752 DEBUG 6026 --- [undedElastic-16] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-agentgame applying filter {regexp=/game-agentgame/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
2025-06-24 10:09:31.753 DEBUG 6026 --- [undedElastic-16] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: ReactiveCompositeDiscoveryClient_game-agentgame
2025-06-24 10:09:31.753 DEBUG 6026 --- [undedElastic-16] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-hall applying {pattern=/game-hall/**} to Path
2025-06-24 10:09:31.754 DEBUG 6026 --- [undedElastic-16] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-hall applying filter {regexp=/game-hall/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
2025-06-24 10:09:31.754 DEBUG 6026 --- [undedElastic-16] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: ReactiveCompositeDiscoveryClient_game-hall
2025-06-24 10:09:31.755 DEBUG 6026 --- [undedElastic-16] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-user applying {pattern=/game-user/**} to Path
2025-06-24 10:09:31.755 DEBUG 6026 --- [undedElastic-16] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-user applying filter {regexp=/game-user/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
2025-06-24 10:09:31.756 DEBUG 6026 --- [undedElastic-16] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: ReactiveCompositeDiscoveryClient_game-user
2025-06-24 10:09:31.756 DEBUG 6026 --- [undedElastic-16] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-gateway applying {pattern=/game-gateway/**} to Path
2025-06-24 10:09:31.757 DEBUG 6026 --- [undedElastic-16] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-gateway applying filter {regexp=/game-gateway/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
2025-06-24 10:09:31.757 DEBUG 6026 --- [undedElastic-16] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: ReactiveCompositeDiscoveryClient_game-gateway
2025-06-24 10:09:31.757 DEBUG 6026 --- [undedElastic-16] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-activity applying {pattern=/game-activity/**} to Path
2025-06-24 10:09:31.758 DEBUG 6026 --- [undedElastic-16] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-activity applying filter {regexp=/game-activity/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
2025-06-24 10:09:31.759 DEBUG 6026 --- [undedElastic-16] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: ReactiveCompositeDiscoveryClient_game-activity
2025-06-24 10:09:31.759 DEBUG 6026 --- [undedElastic-16] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-user applying {_genkey_0=/user/**} to Path
2025-06-24 10:09:31.760 DEBUG 6026 --- [undedElastic-16] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-user applying filter {_genkey_0=1} to StripPrefix
2025-06-24 10:09:31.760 DEBUG 6026 --- [undedElastic-16] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-user
2025-06-24 10:09:31.760 DEBUG 6026 --- [undedElastic-16] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-hall applying {_genkey_0=/hall/**} to Path
2025-06-24 10:09:31.761 DEBUG 6026 --- [undedElastic-16] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-hall applying filter {_genkey_0=1} to StripPrefix
2025-06-24 10:09:31.762 DEBUG 6026 --- [undedElastic-16] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-hall
2025-06-24 10:09:31.762 DEBUG 6026 --- [undedElastic-16] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-agentgame applying {_genkey_0=/agentgame/**} to Path
2025-06-24 10:09:31.763 DEBUG 6026 --- [undedElastic-16] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-agentgame applying filter {_genkey_0=1} to StripPrefix
2025-06-24 10:09:31.763 DEBUG 6026 --- [undedElastic-16] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-agentgame
2025-06-24 10:09:31.763 DEBUG 6026 --- [undedElastic-16] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-activity applying {_genkey_0=/activity/**} to Path
2025-06-24 10:09:31.764 DEBUG 6026 --- [undedElastic-16] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-activity applying filter {_genkey_0=1} to StripPrefix
2025-06-24 10:09:31.764 DEBUG 6026 --- [undedElastic-16] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-activity
2025-06-24 10:09:31.764 DEBUG 6026 --- [undedElastic-16] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-account applying {_genkey_0=/account/**} to Path
2025-06-24 10:09:31.765 DEBUG 6026 --- [undedElastic-16] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-account applying filter {_genkey_0=1} to StripPrefix
2025-06-24 10:09:31.766 DEBUG 6026 --- [undedElastic-16] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-account
2025-06-24 10:10:01.760 DEBUG 6026 --- [undedElastic-17] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-agentgame applying {pattern=/game-agentgame/**} to Path
2025-06-24 10:10:01.762 DEBUG 6026 --- [undedElastic-17] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-agentgame applying filter {regexp=/game-agentgame/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
2025-06-24 10:10:01.762 DEBUG 6026 --- [undedElastic-17] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: ReactiveCompositeDiscoveryClient_game-agentgame
2025-06-24 10:10:01.763 DEBUG 6026 --- [undedElastic-17] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-hall applying {pattern=/game-hall/**} to Path
2025-06-24 10:10:01.763 DEBUG 6026 --- [undedElastic-17] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-hall applying filter {regexp=/game-hall/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
2025-06-24 10:10:01.764 DEBUG 6026 --- [undedElastic-17] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: ReactiveCompositeDiscoveryClient_game-hall
2025-06-24 10:10:01.764 DEBUG 6026 --- [undedElastic-17] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-user applying {pattern=/game-user/**} to Path
2025-06-24 10:10:01.765 DEBUG 6026 --- [undedElastic-17] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-user applying filter {regexp=/game-user/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
2025-06-24 10:10:01.765 DEBUG 6026 --- [undedElastic-17] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: ReactiveCompositeDiscoveryClient_game-user
2025-06-24 10:10:01.765 DEBUG 6026 --- [undedElastic-17] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-gateway applying {pattern=/game-gateway/**} to Path
2025-06-24 10:10:01.766 DEBUG 6026 --- [undedElastic-17] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-gateway applying filter {regexp=/game-gateway/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
2025-06-24 10:10:01.766 DEBUG 6026 --- [undedElastic-17] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: ReactiveCompositeDiscoveryClient_game-gateway
2025-06-24 10:10:01.766 DEBUG 6026 --- [undedElastic-17] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-activity applying {pattern=/game-activity/**} to Path
2025-06-24 10:10:01.767 DEBUG 6026 --- [undedElastic-17] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-activity applying filter {regexp=/game-activity/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
2025-06-24 10:10:01.767 DEBUG 6026 --- [undedElastic-17] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: ReactiveCompositeDiscoveryClient_game-activity
2025-06-24 10:10:01.767 DEBUG 6026 --- [undedElastic-17] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-user applying {_genkey_0=/user/**} to Path
2025-06-24 10:10:01.768 DEBUG 6026 --- [undedElastic-17] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-user applying filter {_genkey_0=1} to StripPrefix
2025-06-24 10:10:01.768 DEBUG 6026 --- [undedElastic-17] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-user
2025-06-24 10:10:01.768 DEBUG 6026 --- [undedElastic-17] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-hall applying {_genkey_0=/hall/**} to Path
2025-06-24 10:10:01.768 DEBUG 6026 --- [undedElastic-17] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-hall applying filter {_genkey_0=1} to StripPrefix
2025-06-24 10:10:01.769 DEBUG 6026 --- [undedElastic-17] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-hall
2025-06-24 10:10:01.769 DEBUG 6026 --- [undedElastic-17] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-agentgame applying {_genkey_0=/agentgame/**} to Path
2025-06-24 10:10:01.769 DEBUG 6026 --- [undedElastic-17] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-agentgame applying filter {_genkey_0=1} to StripPrefix
2025-06-24 10:10:01.769 DEBUG 6026 --- [undedElastic-17] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-agentgame
2025-06-24 10:10:01.770 DEBUG 6026 --- [undedElastic-17] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-activity applying {_genkey_0=/activity/**} to Path
2025-06-24 10:10:01.770 DEBUG 6026 --- [undedElastic-17] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-activity applying filter {_genkey_0=1} to StripPrefix
2025-06-24 10:10:01.770 DEBUG 6026 --- [undedElastic-17] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-activity
2025-06-24 10:10:01.770 DEBUG 6026 --- [undedElastic-17] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-account applying {_genkey_0=/account/**} to Path
2025-06-24 10:10:01.771 DEBUG 6026 --- [undedElastic-17] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-account applying filter {_genkey_0=1} to StripPrefix
2025-06-24 10:10:01.771 DEBUG 6026 --- [undedElastic-17] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-account
2025-06-24 10:10:31.762 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-agentgame applying {pattern=/game-agentgame/**} to Path
2025-06-24 10:10:31.763 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-agentgame applying filter {regexp=/game-agentgame/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
2025-06-24 10:10:31.763 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: ReactiveCompositeDiscoveryClient_game-agentgame
2025-06-24 10:10:31.763 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-hall applying {pattern=/game-hall/**} to Path
2025-06-24 10:10:31.764 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-hall applying filter {regexp=/game-hall/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
2025-06-24 10:10:31.764 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: ReactiveCompositeDiscoveryClient_game-hall
2025-06-24 10:10:31.764 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-user applying {pattern=/game-user/**} to Path
2025-06-24 10:10:31.764 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-user applying filter {regexp=/game-user/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
2025-06-24 10:10:31.764 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: ReactiveCompositeDiscoveryClient_game-user
2025-06-24 10:10:31.765 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-gateway applying {pattern=/game-gateway/**} to Path
2025-06-24 10:10:31.765 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-gateway applying filter {regexp=/game-gateway/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
2025-06-24 10:10:31.765 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: ReactiveCompositeDiscoveryClient_game-gateway
2025-06-24 10:10:31.765 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-activity applying {pattern=/game-activity/**} to Path
2025-06-24 10:10:31.766 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-activity applying filter {regexp=/game-activity/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
2025-06-24 10:10:31.766 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: ReactiveCompositeDiscoveryClient_game-activity
2025-06-24 10:10:31.766 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-user applying {_genkey_0=/user/**} to Path
2025-06-24 10:10:31.766 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-user applying filter {_genkey_0=1} to StripPrefix
2025-06-24 10:10:31.766 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-user
2025-06-24 10:10:31.766 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-hall applying {_genkey_0=/hall/**} to Path
2025-06-24 10:10:31.767 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-hall applying filter {_genkey_0=1} to StripPrefix
2025-06-24 10:10:31.767 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-hall
2025-06-24 10:10:31.767 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-agentgame applying {_genkey_0=/agentgame/**} to Path
2025-06-24 10:10:31.767 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-agentgame applying filter {_genkey_0=1} to StripPrefix
2025-06-24 10:10:31.767 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-agentgame
2025-06-24 10:10:31.767 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-activity applying {_genkey_0=/activity/**} to Path
2025-06-24 10:10:31.768 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-activity applying filter {_genkey_0=1} to StripPrefix
2025-06-24 10:10:31.768 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-activity
2025-06-24 10:10:31.768 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-account applying {_genkey_0=/account/**} to Path
2025-06-24 10:10:31.768 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-account applying filter {_genkey_0=1} to StripPrefix
2025-06-24 10:10:31.769 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-account
2025-06-24 10:11:01.768 DEBUG 6026 --- [oundedElastic-9] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-agentgame applying {pattern=/game-agentgame/**} to Path
2025-06-24 10:11:01.768 DEBUG 6026 --- [oundedElastic-9] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-agentgame applying filter {regexp=/game-agentgame/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
2025-06-24 10:11:01.769 DEBUG 6026 --- [oundedElastic-9] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: ReactiveCompositeDiscoveryClient_game-agentgame
2025-06-24 10:11:01.769 DEBUG 6026 --- [oundedElastic-9] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-hall applying {pattern=/game-hall/**} to Path
2025-06-24 10:11:01.769 DEBUG 6026 --- [oundedElastic-9] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-hall applying filter {regexp=/game-hall/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
2025-06-24 10:11:01.769 DEBUG 6026 --- [oundedElastic-9] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: ReactiveCompositeDiscoveryClient_game-hall
2025-06-24 10:11:01.769 DEBUG 6026 --- [oundedElastic-9] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-user applying {pattern=/game-user/**} to Path
2025-06-24 10:11:01.769 DEBUG 6026 --- [oundedElastic-9] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-user applying filter {regexp=/game-user/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
2025-06-24 10:11:01.769 DEBUG 6026 --- [oundedElastic-9] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: ReactiveCompositeDiscoveryClient_game-user
2025-06-24 10:11:01.770 DEBUG 6026 --- [oundedElastic-9] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-gateway applying {pattern=/game-gateway/**} to Path
2025-06-24 10:11:01.770 DEBUG 6026 --- [oundedElastic-9] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-gateway applying filter {regexp=/game-gateway/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
2025-06-24 10:11:01.770 DEBUG 6026 --- [oundedElastic-9] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: ReactiveCompositeDiscoveryClient_game-gateway
2025-06-24 10:11:01.770 DEBUG 6026 --- [oundedElastic-9] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-activity applying {pattern=/game-activity/**} to Path
2025-06-24 10:11:01.770 DEBUG 6026 --- [oundedElastic-9] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-activity applying filter {regexp=/game-activity/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
2025-06-24 10:11:01.770 DEBUG 6026 --- [oundedElastic-9] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: ReactiveCompositeDiscoveryClient_game-activity
2025-06-24 10:11:01.770 DEBUG 6026 --- [oundedElastic-9] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-user applying {_genkey_0=/user/**} to Path
2025-06-24 10:11:01.770 DEBUG 6026 --- [oundedElastic-9] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-user applying filter {_genkey_0=1} to StripPrefix
2025-06-24 10:11:01.770 DEBUG 6026 --- [oundedElastic-9] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-user
2025-06-24 10:11:01.770 DEBUG 6026 --- [oundedElastic-9] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-hall applying {_genkey_0=/hall/**} to Path
2025-06-24 10:11:01.771 DEBUG 6026 --- [oundedElastic-9] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-hall applying filter {_genkey_0=1} to StripPrefix
2025-06-24 10:11:01.771 DEBUG 6026 --- [oundedElastic-9] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-hall
2025-06-24 10:11:01.771 DEBUG 6026 --- [oundedElastic-9] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-agentgame applying {_genkey_0=/agentgame/**} to Path
2025-06-24 10:11:01.771 DEBUG 6026 --- [oundedElastic-9] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-agentgame applying filter {_genkey_0=1} to StripPrefix
2025-06-24 10:11:01.771 DEBUG 6026 --- [oundedElastic-9] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-agentgame
2025-06-24 10:11:01.771 DEBUG 6026 --- [oundedElastic-9] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-activity applying {_genkey_0=/activity/**} to Path
2025-06-24 10:11:01.771 DEBUG 6026 --- [oundedElastic-9] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-activity applying filter {_genkey_0=1} to StripPrefix
2025-06-24 10:11:01.771 DEBUG 6026 --- [oundedElastic-9] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-activity
2025-06-24 10:11:01.771 DEBUG 6026 --- [oundedElastic-9] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-account applying {_genkey_0=/account/**} to Path
2025-06-24 10:11:01.771 DEBUG 6026 --- [oundedElastic-9] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-account applying filter {_genkey_0=1} to StripPrefix
2025-06-24 10:11:01.771 DEBUG 6026 --- [oundedElastic-9] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-account
2025-06-24 10:11:31.779 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-agentgame applying {pattern=/game-agentgame/**} to Path
2025-06-24 10:11:31.781 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-agentgame applying filter {regexp=/game-agentgame/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
2025-06-24 10:11:31.782 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: ReactiveCompositeDiscoveryClient_game-agentgame
2025-06-24 10:11:31.783 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-hall applying {pattern=/game-hall/**} to Path
2025-06-24 10:11:31.783 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-hall applying filter {regexp=/game-hall/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
2025-06-24 10:11:31.784 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: ReactiveCompositeDiscoveryClient_game-hall
2025-06-24 10:11:31.784 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-user applying {pattern=/game-user/**} to Path
2025-06-24 10:11:31.784 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-user applying filter {regexp=/game-user/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
2025-06-24 10:11:31.785 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: ReactiveCompositeDiscoveryClient_game-user
2025-06-24 10:11:31.785 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-gateway applying {pattern=/game-gateway/**} to Path
2025-06-24 10:11:31.785 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-gateway applying filter {regexp=/game-gateway/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
2025-06-24 10:11:31.786 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: ReactiveCompositeDiscoveryClient_game-gateway
2025-06-24 10:11:31.786 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-activity applying {pattern=/game-activity/**} to Path
2025-06-24 10:11:31.786 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-activity applying filter {regexp=/game-activity/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
2025-06-24 10:11:31.787 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: ReactiveCompositeDiscoveryClient_game-activity
2025-06-24 10:11:31.787 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-user applying {_genkey_0=/user/**} to Path
2025-06-24 10:11:31.787 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-user applying filter {_genkey_0=1} to StripPrefix
2025-06-24 10:11:31.788 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-user
2025-06-24 10:11:31.788 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-hall applying {_genkey_0=/hall/**} to Path
2025-06-24 10:11:31.789 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-hall applying filter {_genkey_0=1} to StripPrefix
2025-06-24 10:11:31.789 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-hall
2025-06-24 10:11:31.789 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-agentgame applying {_genkey_0=/agentgame/**} to Path
2025-06-24 10:11:31.789 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-agentgame applying filter {_genkey_0=1} to StripPrefix
2025-06-24 10:11:31.790 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-agentgame
2025-06-24 10:11:31.790 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-activity applying {_genkey_0=/activity/**} to Path
2025-06-24 10:11:31.790 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-activity applying filter {_genkey_0=1} to StripPrefix
2025-06-24 10:11:31.791 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-activity
2025-06-24 10:11:31.791 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-account applying {_genkey_0=/account/**} to Path
2025-06-24 10:11:31.791 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-account applying filter {_genkey_0=1} to StripPrefix
2025-06-24 10:11:31.791 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-account
2025-06-24 10:12:01.781 DEBUG 6026 --- [undedElastic-17] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-agentgame applying {pattern=/game-agentgame/**} to Path
2025-06-24 10:12:01.782 DEBUG 6026 --- [undedElastic-17] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-agentgame applying filter {regexp=/game-agentgame/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
2025-06-24 10:12:01.783 DEBUG 6026 --- [undedElastic-17] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: ReactiveCompositeDiscoveryClient_game-agentgame
2025-06-24 10:12:01.783 DEBUG 6026 --- [undedElastic-17] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-hall applying {pattern=/game-hall/**} to Path
2025-06-24 10:12:01.783 DEBUG 6026 --- [undedElastic-17] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-hall applying filter {regexp=/game-hall/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
2025-06-24 10:12:01.784 DEBUG 6026 --- [undedElastic-17] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: ReactiveCompositeDiscoveryClient_game-hall
2025-06-24 10:12:01.784 DEBUG 6026 --- [undedElastic-17] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-gateway applying {pattern=/game-gateway/**} to Path
2025-06-24 10:12:01.784 DEBUG 6026 --- [undedElastic-17] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-gateway applying filter {regexp=/game-gateway/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
2025-06-24 10:12:01.785 DEBUG 6026 --- [undedElastic-17] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: ReactiveCompositeDiscoveryClient_game-gateway
2025-06-24 10:12:01.785 DEBUG 6026 --- [undedElastic-17] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-activity applying {pattern=/game-activity/**} to Path
2025-06-24 10:12:01.785 DEBUG 6026 --- [undedElastic-17] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-activity applying filter {regexp=/game-activity/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
2025-06-24 10:12:01.786 DEBUG 6026 --- [undedElastic-17] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: ReactiveCompositeDiscoveryClient_game-activity
2025-06-24 10:12:01.786 DEBUG 6026 --- [undedElastic-17] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-user applying {_genkey_0=/user/**} to Path
2025-06-24 10:12:01.786 DEBUG 6026 --- [undedElastic-17] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-user applying filter {_genkey_0=1} to StripPrefix
2025-06-24 10:12:01.786 DEBUG 6026 --- [undedElastic-17] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-user
2025-06-24 10:12:01.786 DEBUG 6026 --- [undedElastic-17] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-hall applying {_genkey_0=/hall/**} to Path
2025-06-24 10:12:01.787 DEBUG 6026 --- [undedElastic-17] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-hall applying filter {_genkey_0=1} to StripPrefix
2025-06-24 10:12:01.787 DEBUG 6026 --- [undedElastic-17] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-hall
2025-06-24 10:12:01.787 DEBUG 6026 --- [undedElastic-17] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-agentgame applying {_genkey_0=/agentgame/**} to Path
2025-06-24 10:12:01.787 DEBUG 6026 --- [undedElastic-17] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-agentgame applying filter {_genkey_0=1} to StripPrefix
2025-06-24 10:12:01.787 DEBUG 6026 --- [undedElastic-17] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-agentgame
2025-06-24 10:12:01.788 DEBUG 6026 --- [undedElastic-17] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-activity applying {_genkey_0=/activity/**} to Path
2025-06-24 10:12:01.788 DEBUG 6026 --- [undedElastic-17] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-activity applying filter {_genkey_0=1} to StripPrefix
2025-06-24 10:12:01.788 DEBUG 6026 --- [undedElastic-17] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-activity
2025-06-24 10:12:01.788 DEBUG 6026 --- [undedElastic-17] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-account applying {_genkey_0=/account/**} to Path
2025-06-24 10:12:01.789 DEBUG 6026 --- [undedElastic-17] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-account applying filter {_genkey_0=1} to StripPrefix
2025-06-24 10:12:01.789 DEBUG 6026 --- [undedElastic-17] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-account
2025-06-24 10:12:31.784 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-agentgame applying {pattern=/game-agentgame/**} to Path
2025-06-24 10:12:31.785 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-agentgame applying filter {regexp=/game-agentgame/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
2025-06-24 10:12:31.785 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: ReactiveCompositeDiscoveryClient_game-agentgame
2025-06-24 10:12:31.785 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-hall applying {pattern=/game-hall/**} to Path
2025-06-24 10:12:31.785 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-hall applying filter {regexp=/game-hall/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
2025-06-24 10:12:31.785 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: ReactiveCompositeDiscoveryClient_game-hall
2025-06-24 10:12:31.785 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-gateway applying {pattern=/game-gateway/**} to Path
2025-06-24 10:12:31.785 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-gateway applying filter {regexp=/game-gateway/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
2025-06-24 10:12:31.785 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: ReactiveCompositeDiscoveryClient_game-gateway
2025-06-24 10:12:31.786 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-activity applying {pattern=/game-activity/**} to Path
2025-06-24 10:12:31.786 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-activity applying filter {regexp=/game-activity/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
2025-06-24 10:12:31.786 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: ReactiveCompositeDiscoveryClient_game-activity
2025-06-24 10:12:31.786 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-user applying {_genkey_0=/user/**} to Path
2025-06-24 10:12:31.786 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-user applying filter {_genkey_0=1} to StripPrefix
2025-06-24 10:12:31.786 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-user
2025-06-24 10:12:31.786 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-hall applying {_genkey_0=/hall/**} to Path
2025-06-24 10:12:31.786 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-hall applying filter {_genkey_0=1} to StripPrefix
2025-06-24 10:12:31.786 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-hall
2025-06-24 10:12:31.786 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-agentgame applying {_genkey_0=/agentgame/**} to Path
2025-06-24 10:12:31.786 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-agentgame applying filter {_genkey_0=1} to StripPrefix
2025-06-24 10:12:31.786 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-agentgame
2025-06-24 10:12:31.786 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-activity applying {_genkey_0=/activity/**} to Path
2025-06-24 10:12:31.787 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-activity applying filter {_genkey_0=1} to StripPrefix
2025-06-24 10:12:31.787 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-activity
2025-06-24 10:12:31.787 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-account applying {_genkey_0=/account/**} to Path
2025-06-24 10:12:31.787 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-account applying filter {_genkey_0=1} to StripPrefix
2025-06-24 10:12:31.787 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-account
2025-06-24 10:13:01.797 DEBUG 6026 --- [undedElastic-17] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-agentgame applying {pattern=/game-agentgame/**} to Path
2025-06-24 10:13:01.799 DEBUG 6026 --- [undedElastic-17] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-agentgame applying filter {regexp=/game-agentgame/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
2025-06-24 10:13:01.800 DEBUG 6026 --- [undedElastic-17] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: ReactiveCompositeDiscoveryClient_game-agentgame
2025-06-24 10:13:01.801 DEBUG 6026 --- [undedElastic-17] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-hall applying {pattern=/game-hall/**} to Path
2025-06-24 10:13:01.802 DEBUG 6026 --- [undedElastic-17] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-hall applying filter {regexp=/game-hall/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
2025-06-24 10:13:01.803 DEBUG 6026 --- [undedElastic-17] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: ReactiveCompositeDiscoveryClient_game-hall
2025-06-24 10:13:01.803 DEBUG 6026 --- [undedElastic-17] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-user applying {pattern=/game-user/**} to Path
2025-06-24 10:13:01.803 DEBUG 6026 --- [undedElastic-17] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-user applying filter {regexp=/game-user/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
2025-06-24 10:13:01.804 DEBUG 6026 --- [undedElastic-17] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: ReactiveCompositeDiscoveryClient_game-user
2025-06-24 10:13:01.804 DEBUG 6026 --- [undedElastic-17] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-gateway applying {pattern=/game-gateway/**} to Path
2025-06-24 10:13:01.804 DEBUG 6026 --- [undedElastic-17] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-gateway applying filter {regexp=/game-gateway/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
2025-06-24 10:13:01.804 DEBUG 6026 --- [undedElastic-17] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: ReactiveCompositeDiscoveryClient_game-gateway
2025-06-24 10:13:01.804 DEBUG 6026 --- [undedElastic-17] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-activity applying {pattern=/game-activity/**} to Path
2025-06-24 10:13:01.805 DEBUG 6026 --- [undedElastic-17] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-activity applying filter {regexp=/game-activity/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
2025-06-24 10:13:01.806 DEBUG 6026 --- [undedElastic-17] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: ReactiveCompositeDiscoveryClient_game-activity
2025-06-24 10:13:01.806 DEBUG 6026 --- [undedElastic-17] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-user applying {_genkey_0=/user/**} to Path
2025-06-24 10:13:01.806 DEBUG 6026 --- [undedElastic-17] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-user applying filter {_genkey_0=1} to StripPrefix
2025-06-24 10:13:01.806 DEBUG 6026 --- [undedElastic-17] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-user
2025-06-24 10:13:01.806 DEBUG 6026 --- [undedElastic-17] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-hall applying {_genkey_0=/hall/**} to Path
2025-06-24 10:13:01.807 DEBUG 6026 --- [undedElastic-17] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-hall applying filter {_genkey_0=1} to StripPrefix
2025-06-24 10:13:01.807 DEBUG 6026 --- [undedElastic-17] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-hall
2025-06-24 10:13:01.807 DEBUG 6026 --- [undedElastic-17] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-agentgame applying {_genkey_0=/agentgame/**} to Path
2025-06-24 10:13:01.807 DEBUG 6026 --- [undedElastic-17] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-agentgame applying filter {_genkey_0=1} to StripPrefix
2025-06-24 10:13:01.808 DEBUG 6026 --- [undedElastic-17] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-agentgame
2025-06-24 10:13:01.808 DEBUG 6026 --- [undedElastic-17] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-activity applying {_genkey_0=/activity/**} to Path
2025-06-24 10:13:01.808 DEBUG 6026 --- [undedElastic-17] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-activity applying filter {_genkey_0=1} to StripPrefix
2025-06-24 10:13:01.808 DEBUG 6026 --- [undedElastic-17] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-activity
2025-06-24 10:13:01.808 DEBUG 6026 --- [undedElastic-17] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-account applying {_genkey_0=/account/**} to Path
2025-06-24 10:13:01.808 DEBUG 6026 --- [undedElastic-17] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-account applying filter {_genkey_0=1} to StripPrefix
2025-06-24 10:13:01.808 DEBUG 6026 --- [undedElastic-17] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-account
2025-06-24 10:13:31.800 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-agentgame applying {pattern=/game-agentgame/**} to Path
2025-06-24 10:13:31.801 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-agentgame applying filter {regexp=/game-agentgame/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
2025-06-24 10:13:31.801 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: ReactiveCompositeDiscoveryClient_game-agentgame
2025-06-24 10:13:31.801 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-hall applying {pattern=/game-hall/**} to Path
2025-06-24 10:13:31.801 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-hall applying filter {regexp=/game-hall/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
2025-06-24 10:13:31.801 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: ReactiveCompositeDiscoveryClient_game-hall
2025-06-24 10:13:31.802 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-gateway applying {pattern=/game-gateway/**} to Path
2025-06-24 10:13:31.802 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-gateway applying filter {regexp=/game-gateway/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
2025-06-24 10:13:31.802 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: ReactiveCompositeDiscoveryClient_game-gateway
2025-06-24 10:13:31.802 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-activity applying {pattern=/game-activity/**} to Path
2025-06-24 10:13:31.802 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-activity applying filter {regexp=/game-activity/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
2025-06-24 10:13:31.802 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: ReactiveCompositeDiscoveryClient_game-activity
2025-06-24 10:13:31.802 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-user applying {_genkey_0=/user/**} to Path
2025-06-24 10:13:31.802 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-user applying filter {_genkey_0=1} to StripPrefix
2025-06-24 10:13:31.803 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-user
2025-06-24 10:13:31.803 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-hall applying {_genkey_0=/hall/**} to Path
2025-06-24 10:13:31.803 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-hall applying filter {_genkey_0=1} to StripPrefix
2025-06-24 10:13:31.803 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-hall
2025-06-24 10:13:31.803 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-agentgame applying {_genkey_0=/agentgame/**} to Path
2025-06-24 10:13:31.803 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-agentgame applying filter {_genkey_0=1} to StripPrefix
2025-06-24 10:13:31.803 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-agentgame
2025-06-24 10:13:31.803 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-activity applying {_genkey_0=/activity/**} to Path
2025-06-24 10:13:31.803 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-activity applying filter {_genkey_0=1} to StripPrefix
2025-06-24 10:13:31.804 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-activity
2025-06-24 10:13:31.804 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-account applying {_genkey_0=/account/**} to Path
2025-06-24 10:13:31.804 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-account applying filter {_genkey_0=1} to StripPrefix
2025-06-24 10:13:31.804 DEBUG 6026 --- [oundedElastic-6] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-account
2025-06-24 10:14:01.806 DEBUG 6026 --- [oundedElastic-9] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-agentgame applying {pattern=/game-agentgame/**} to Path
2025-06-24 10:14:01.808 DEBUG 6026 --- [oundedElastic-9] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-agentgame applying filter {regexp=/game-agentgame/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
2025-06-24 10:14:01.809 DEBUG 6026 --- [oundedElastic-9] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: ReactiveCompositeDiscoveryClient_game-agentgame
2025-06-24 10:14:01.809 DEBUG 6026 --- [oundedElastic-9] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-hall applying {pattern=/game-hall/**} to Path
2025-06-24 10:14:01.810 DEBUG 6026 --- [oundedElastic-9] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-hall applying filter {regexp=/game-hall/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
2025-06-24 10:14:01.810 DEBUG 6026 --- [oundedElastic-9] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: ReactiveCompositeDiscoveryClient_game-hall
2025-06-24 10:14:01.810 DEBUG 6026 --- [oundedElastic-9] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-gateway applying {pattern=/game-gateway/**} to Path
2025-06-24 10:14:01.810 DEBUG 6026 --- [oundedElastic-9] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-gateway applying filter {regexp=/game-gateway/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
2025-06-24 10:14:01.811 DEBUG 6026 --- [oundedElastic-9] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: ReactiveCompositeDiscoveryClient_game-gateway
2025-06-24 10:14:01.811 DEBUG 6026 --- [oundedElastic-9] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-activity applying {pattern=/game-activity/**} to Path
2025-06-24 10:14:01.811 DEBUG 6026 --- [oundedElastic-9] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition ReactiveCompositeDiscoveryClient_game-activity applying filter {regexp=/game-activity/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
2025-06-24 10:14:01.811 DEBUG 6026 --- [oundedElastic-9] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: ReactiveCompositeDiscoveryClient_game-activity
2025-06-24 10:14:01.811 DEBUG 6026 --- [oundedElastic-9] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-user applying {_genkey_0=/user/**} to Path
2025-06-24 10:14:01.811 DEBUG 6026 --- [oundedElastic-9] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-user applying filter {_genkey_0=1} to StripPrefix
2025-06-24 10:14:01.812 DEBUG 6026 --- [oundedElastic-9] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-user
2025-06-24 10:14:01.812 DEBUG 6026 --- [oundedElastic-9] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-hall applying {_genkey_0=/hall/**} to Path
2025-06-24 10:14:01.812 DEBUG 6026 --- [oundedElastic-9] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-hall applying filter {_genkey_0=1} to StripPrefix
2025-06-24 10:14:01.812 DEBUG 6026 --- [oundedElastic-9] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-hall
2025-06-24 10:14:01.812 DEBUG 6026 --- [oundedElastic-9] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-agentgame applying {_genkey_0=/agentgame/**} to Path
2025-06-24 10:14:01.812 DEBUG 6026 --- [oundedElastic-9] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-agentgame applying filter {_genkey_0=1} to StripPrefix
2025-06-24 10:14:01.812 DEBUG 6026 --- [oundedElastic-9] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-agentgame
2025-06-24 10:14:01.813 DEBUG 6026 --- [oundedElastic-9] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-activity applying {_genkey_0=/activity/**} to Path
2025-06-24 10:14:01.813 DEBUG 6026 --- [oundedElastic-9] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-activity applying filter {_genkey_0=1} to StripPrefix
2025-06-24 10:14:01.813 DEBUG 6026 --- [oundedElastic-9] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-activity
2025-06-24 10:14:01.813 DEBUG 6026 --- [oundedElastic-9] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-account applying {_genkey_0=/account/**} to Path
2025-06-24 10:14:01.813 DEBUG 6026 --- [oundedElastic-9] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition game-account applying filter {_genkey_0=1} to StripPrefix
2025-06-24 10:14:01.813 DEBUG 6026 --- [oundedElastic-9] o.s.c.g.r.RouteDefinitionRouteLocator    : RouteDefinition matched: game-account
