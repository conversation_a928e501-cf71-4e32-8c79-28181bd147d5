
  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/
 :: Spring Boot ::               (v2.7.14)

2025-06-24 13:41:21.982  INFO 14574 --- [           main] c.w.agentgame.AgentGameApplication       : Starting AgentGameApplication v1.0-SNAPSHOT using Java 21.0.7 on WangXiaoShundeMacBook-Pro.local with PID 14574 (/Users/<USER>/wingame/saas_spring/game-agentGame/target/game-agentGame-1.0-SNAPSHOT.jar started by wangxia<PERSON>un in /Users/<USER>/wingame/saas_spring/game-agentGame)
2025-06-24 13:41:21.984 DEBUG 14574 --- [           main] c.w.agentgame.AgentGameApplication       : Running with Spring Boot v2.7.14, Spring v5.3.29
2025-06-24 13:41:21.985  INFO 14574 --- [           main] c.w.agentgame.AgentGameApplication       : The following 1 profile is active: "dev"
2025-06-24 13:41:22.006  WARN 14574 --- [           main] c.a.c.n.c.NacosConfigDataLoader          : [Nacos Config] config[dataId=game-agentgame.yml, group=DEFAULT_GROUP] is empty
2025-06-24 13:41:22.340  INFO 14574 --- [           main] .s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-24 13:41:22.340  INFO 14574 --- [           main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
2025-06-24 13:41:22.344  INFO 14574 --- [           main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 0 MongoDB repository interfaces.
2025-06-24 13:41:22.350  INFO 14574 --- [           main] .s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-24 13:41:22.350  INFO 14574 --- [           main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-24 13:41:22.354  INFO 14574 --- [           main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 Redis repository interfaces.
2025-06-24 13:41:22.422  INFO 14574 --- [           main] o.s.cloud.context.scope.GenericScope     : BeanFactory id=61649fed-e775-3ee1-8afc-06b65f372d2a
2025-06-24 13:41:22.569  INFO 14574 --- [           main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8083 (http)
2025-06-24 13:41:22.573  INFO 14574 --- [           main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-06-24 13:41:22.573  INFO 14574 --- [           main] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.78]
2025-06-24 13:41:22.609  INFO 14574 --- [           main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-06-24 13:41:22.609  INFO 14574 --- [           main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 602 ms
2025-06-24 13:41:23.038  INFO 14574 --- [           main] org.mongodb.driver.client                : MongoClient with metadata {"driver": {"name": "mongo-java-driver|sync|spring-boot", "version": "4.6.1"}, "os": {"type": "Darwin", "name": "Mac OS X", "architecture": "aarch64", "version": "15.5"}, "platform": "Java/Homebrew/21.0.7"} created with settings MongoClientSettings{readPreference=primary, writeConcern=WriteConcern{w=null, wTimeout=null ms, journal=null}, retryWrites=true, retryReads=true, readConcern=ReadConcern{level=null}, credential=MongoCredential{mechanism=null, userName='', source='wingame_agentgame', password=<hidden>, mechanismProperties=<hidden>}, streamFactoryFactory=null, commandListeners=[], codecRegistry=ProvidersCodecRegistry{codecProviders=[ValueCodecProvider{}, BsonValueCodecProvider{}, DBRefCodecProvider{}, DBObjectCodecProvider{}, DocumentCodecProvider{}, IterableCodecProvider{}, MapCodecProvider{}, GeoJsonCodecProvider{}, GridFSFileCodecProvider{}, Jsr310CodecProvider{}, JsonObjectCodecProvider{}, BsonCodecProvider{}, EnumCodecProvider{}, com.mongodb.Jep395RecordCodecProvider@6d2dc9d2]}, clusterSettings={hosts=[localhost:27017], srvServiceName=mongodb, mode=SINGLE, requiredClusterType=UNKNOWN, requiredReplicaSetName='null', serverSelector='null', clusterListeners='[]', serverSelectionTimeout='30000 ms', localThreshold='30000 ms'}, socketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=0, receiveBufferSize=0, sendBufferSize=0}, heartbeatSocketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=10000, receiveBufferSize=0, sendBufferSize=0}, connectionPoolSettings=ConnectionPoolSettings{maxSize=100, minSize=0, maxWaitTimeMS=120000, maxConnectionLifeTimeMS=0, maxConnectionIdleTimeMS=0, maintenanceInitialDelayMS=0, maintenanceFrequencyMS=60000, connectionPoolListeners=[], maxConnecting=2}, serverSettings=ServerSettings{heartbeatFrequencyMS=10000, minHeartbeatFrequencyMS=500, serverListeners='[]', serverMonitorListeners='[]'}, sslSettings=SslSettings{enabled=false, invalidHostNameAllowed=false, context=null}, applicationName='null', compressorList=[], uuidRepresentation=JAVA_LEGACY, serverApi=null, autoEncryptionSettings=null, contextProvider=null}
2025-06-24 13:41:23.043  INFO 14574 --- [localhost:27017] org.mongodb.driver.connection            : Opened connection [connectionId{localValue:1, serverValue:23}] to localhost:27017
2025-06-24 13:41:23.043  INFO 14574 --- [localhost:27017] org.mongodb.driver.connection            : Opened connection [connectionId{localValue:2, serverValue:24}] to localhost:27017
2025-06-24 13:41:23.043  INFO 14574 --- [localhost:27017] org.mongodb.driver.cluster               : Monitor thread successfully connected to server with description ServerDescription{address=localhost:27017, type=STANDALONE, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=17, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=8976750}
2025-06-24 13:41:23.154  WARN 14574 --- [           main] ConfigServletWebServerApplicationContext : Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'; nested exception is org.springframework.boot.web.server.PortInUseException: Port 8083 is already in use
2025-06-24 13:41:23.161  INFO 14574 --- [           main] o.apache.catalina.core.StandardService   : Stopping service [Tomcat]
2025-06-24 13:41:23.167  INFO 14574 --- [           main] ConditionEvaluationReportLoggingListener : 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-06-24 13:41:23.182 ERROR 14574 --- [           main] o.s.b.d.LoggingFailureAnalysisReporter   : 

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 8083 was already in use.

Action:

Identify and stop the process that's listening on port 8083 or configure this application to listen on another port.

2025-06-24 13:41:23.649  WARN 14574 --- [       Thread-1] c.a.n.common.http.HttpClientBeanHolder   : [HttpClientBeanHolder] Start destroying common HttpClient
2025-06-24 13:41:23.649  WARN 14574 --- [       Thread-6] c.a.nacos.common.notify.NotifyCenter     : [NotifyCenter] Start destroying Publisher
2025-06-24 13:41:23.650  WARN 14574 --- [       Thread-6] c.a.nacos.common.notify.NotifyCenter     : [NotifyCenter] Destruction of the end
