
  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/
 :: Spring Boot ::               (v2.7.14)

2025-06-24 09:59:15.444  INFO 5446 --- [           main] com.wingame.account.AccountApplication   : Starting AccountApplication v1.0-SNAPSHOT using Java 21.0.7 on WangXiaoShundeMacBook-Pro.local with PID 5446 (/Users/<USER>/wingame/saas_spring/game-account/target/game-account-1.0-SNAPSHOT.jar started by wang<PERSON><PERSON>un in /Users/<USER>/wingame/saas_spring/game-account)
2025-06-24 09:59:15.444 DEBUG 5446 --- [           main] com.wingame.account.AccountApplication   : Running with Spring Boot v2.7.14, Spring v5.3.29
2025-06-24 09:59:15.445  INFO 5446 --- [           main] com.wingame.account.AccountApplication   : The following 1 profile is active: "dev"
2025-06-24 09:59:15.461  WARN 5446 --- [           main] c.a.c.n.c.NacosConfigDataLoader          : [Nacos Config] config[dataId=game-account.yml, group=DEFAULT_GROUP] is empty
2025-06-24 09:59:15.700  INFO 5446 --- [           main] .s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-24 09:59:15.701  INFO 5446 --- [           main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
2025-06-24 09:59:15.704  INFO 5446 --- [           main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 0 MongoDB repository interfaces.
2025-06-24 09:59:15.709  INFO 5446 --- [           main] .s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-24 09:59:15.710  INFO 5446 --- [           main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-24 09:59:15.712  INFO 5446 --- [           main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 Redis repository interfaces.
2025-06-24 09:59:15.784  INFO 5446 --- [           main] o.s.cloud.context.scope.GenericScope     : BeanFactory id=b7ab0dba-dd2a-3e12-91a5-d310ceb80755
2025-06-24 09:59:15.913  INFO 5446 --- [           main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8085 (http)
2025-06-24 09:59:15.916  INFO 5446 --- [           main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-06-24 09:59:15.916  INFO 5446 --- [           main] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.78]
2025-06-24 09:59:15.944  INFO 5446 --- [           main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-06-24 09:59:15.944  INFO 5446 --- [           main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 482 ms
2025-06-24 09:59:16.258  INFO 5446 --- [           main] org.mongodb.driver.client                : MongoClient with metadata {"driver": {"name": "mongo-java-driver|sync|spring-boot", "version": "4.6.1"}, "os": {"type": "Darwin", "name": "Mac OS X", "architecture": "aarch64", "version": "15.5"}, "platform": "Java/Homebrew/21.0.7"} created with settings MongoClientSettings{readPreference=primary, writeConcern=WriteConcern{w=null, wTimeout=null ms, journal=null}, retryWrites=true, retryReads=true, readConcern=ReadConcern{level=null}, credential=MongoCredential{mechanism=null, userName='', source='wingame_account', password=<hidden>, mechanismProperties=<hidden>}, streamFactoryFactory=null, commandListeners=[], codecRegistry=ProvidersCodecRegistry{codecProviders=[ValueCodecProvider{}, BsonValueCodecProvider{}, DBRefCodecProvider{}, DBObjectCodecProvider{}, DocumentCodecProvider{}, IterableCodecProvider{}, MapCodecProvider{}, GeoJsonCodecProvider{}, GridFSFileCodecProvider{}, Jsr310CodecProvider{}, JsonObjectCodecProvider{}, BsonCodecProvider{}, EnumCodecProvider{}, com.mongodb.Jep395RecordCodecProvider@********]}, clusterSettings={hosts=[localhost:27017], srvServiceName=mongodb, mode=SINGLE, requiredClusterType=UNKNOWN, requiredReplicaSetName='null', serverSelector='null', clusterListeners='[]', serverSelectionTimeout='30000 ms', localThreshold='30000 ms'}, socketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=0, receiveBufferSize=0, sendBufferSize=0}, heartbeatSocketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=10000, receiveBufferSize=0, sendBufferSize=0}, connectionPoolSettings=ConnectionPoolSettings{maxSize=100, minSize=0, maxWaitTimeMS=120000, maxConnectionLifeTimeMS=0, maxConnectionIdleTimeMS=0, maintenanceInitialDelayMS=0, maintenanceFrequencyMS=60000, connectionPoolListeners=[], maxConnecting=2}, serverSettings=ServerSettings{heartbeatFrequencyMS=10000, minHeartbeatFrequencyMS=500, serverListeners='[]', serverMonitorListeners='[]'}, sslSettings=SslSettings{enabled=false, invalidHostNameAllowed=false, context=null}, applicationName='null', compressorList=[], uuidRepresentation=JAVA_LEGACY, serverApi=null, autoEncryptionSettings=null, contextProvider=null}
2025-06-24 09:59:16.266  INFO 5446 --- [localhost:27017] org.mongodb.driver.connection            : Opened connection [connectionId{localValue:1, serverValue:64}] to localhost:27017
2025-06-24 09:59:16.266  INFO 5446 --- [localhost:27017] org.mongodb.driver.connection            : Opened connection [connectionId{localValue:2, serverValue:63}] to localhost:27017
2025-06-24 09:59:16.266  INFO 5446 --- [localhost:27017] org.mongodb.driver.cluster               : Monitor thread successfully connected to server with description ServerDescription{address=localhost:27017, type=STANDALONE, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=17, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=9986250}
2025-06-24 09:59:16.361  WARN 5446 --- [           main] ConfigServletWebServerApplicationContext : Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'; nested exception is org.springframework.boot.web.server.PortInUseException: Port 8085 is already in use
2025-06-24 09:59:16.368  INFO 5446 --- [           main] o.apache.catalina.core.StandardService   : Stopping service [Tomcat]
2025-06-24 09:59:16.374  INFO 5446 --- [           main] ConditionEvaluationReportLoggingListener : 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-06-24 09:59:16.384 ERROR 5446 --- [           main] o.s.b.d.LoggingFailureAnalysisReporter   : 

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 8085 was already in use.

Action:

Identify and stop the process that's listening on port 8085 or configure this application to listen on another port.

2025-06-24 09:59:16.984  WARN 5446 --- [       Thread-6] c.a.nacos.common.notify.NotifyCenter     : [NotifyCenter] Start destroying Publisher
2025-06-24 09:59:16.984  WARN 5446 --- [       Thread-1] c.a.n.common.http.HttpClientBeanHolder   : [HttpClientBeanHolder] Start destroying common HttpClient
2025-06-24 09:59:16.985  WARN 5446 --- [       Thread-6] c.a.nacos.common.notify.NotifyCenter     : [NotifyCenter] Destruction of the end
2025-06-24 09:59:16.985  WARN 5446 --- [       Thread-1] c.a.n.common.http.HttpClientBeanHolder   : [HttpClientBeanHolder] Destruction of the end
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          2025-06-24 09:59:43.710  INFO 4536 --- [nio-8085-exec-3] c.w.a.controller.AccountController       : 获取用户 demo_user_123 的账户余额
2025-06-24 09:59:43.747  INFO 4536 --- [nio-8085-exec-4] c.w.a.controller.AccountController       : 用户 demo_user_123 充值 1000 元，支付方式: alipay
2025-06-24 10:03:51.567  WARN 4536 --- [       Thread-6] c.a.nacos.common.notify.NotifyCenter     : [NotifyCenter] Start destroying Publisher
2025-06-24 10:03:51.567  WARN 4536 --- [       Thread-1] c.a.n.common.http.HttpClientBeanHolder   : [HttpClientBeanHolder] Start destroying common HttpClient
2025-06-24 10:03:51.567  WARN 4536 --- [       Thread-6] c.a.nacos.common.notify.NotifyCenter     : [NotifyCenter] Destruction of the end
2025-06-24 10:03:51.568  WARN 4536 --- [       Thread-1] c.a.n.common.http.HttpClientBeanHolder   : [HttpClientBeanHolder] Destruction of the end
2025-06-24 10:03:52.595  INFO 4536 --- [ionShutdownHook] c.a.c.n.registry.NacosServiceRegistry    : De-registering from Nacos Server now...
2025-06-24 10:03:52.605  INFO 4536 --- [ionShutdownHook] c.a.c.n.registry.NacosServiceRegistry    : De-registration finished.
