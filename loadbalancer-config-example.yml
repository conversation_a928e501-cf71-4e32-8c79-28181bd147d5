# LoadBalancer配置示例
spring:
  cloud:
    loadbalancer:
      # 负载均衡策略
      ribbon:
        enabled: false  # 禁用Ribbon
      # 健康检查
      health-check:
        initial-delay: 0
        interval: 10s
      # 缓存配置
      cache:
        enabled: true
        ttl: 35s
        capacity: 256
      # 重试配置
      retry:
        enabled: true
        max-retries-on-same-service-instance: 1
        max-retries-on-next-service-instance: 1
        retry-on-all-operations: false

# Feign配置
feign:
  client:
    config:
      default:
        connectTimeout: 5000
        readTimeout: 10000
        loggerLevel: basic
  # 启用LoadBalancer
  loadbalancer:
    enabled: true
  # 压缩配置
  compression:
    request:
      enabled: true
      mime-types: text/xml,application/xml,application/json
      min-request-size: 2048
    response:
      enabled: true

# 日志配置
logging:
  level:
    org.springframework.cloud.loadbalancer: DEBUG
    org.springframework.cloud.openfeign: DEBUG
