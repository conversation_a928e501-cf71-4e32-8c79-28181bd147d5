package com.wingame.agentgame;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;

/**
 * 代理游戏服务启动类
 */
@SpringBootApplication(scanBasePackages = {"com.wingame.agentgame", "com.wingame.common"})
@EnableDiscoveryClient
@EnableFeignClients
public class AgentGameApplication {
    
    public static void main(String[] args) {
        SpringApplication.run(AgentGameApplication.class, args);
    }
}
