package com.wingame.agentgame.controller;

import com.wingame.common.result.Result;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * 代理游戏控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/agentgame")
@RequiredArgsConstructor
public class AgentGameController {
    
    /**
     * 获取可用游戏列表
     */
    @GetMapping("/games")
    public Result<List<Object>> getAvailableGames() {
        log.info("获取可用游戏列表");
        
        List<Object> games = new ArrayList<>();
        
        games.add(new Object() {
            public final String id = "texas_holdem";
            public final String name = "德州扑克";
            public final String provider = "Evolution Gaming";
            public final String category = "POKER";
            public final boolean isLive = true;
            public final int minBet = 10;
            public final int maxBet = 10000;
            public final String status = "ACTIVE";
        });
        
        games.add(new Object() {
            public final String id = "baccarat_live";
            public final String name = "真人百家乐";
            public final String provider = "Pragmatic Play";
            public final String category = "CARD";
            public final boolean isLive = true;
            public final int minBet = 50;
            public final int maxBet = 50000;
            public final String status = "ACTIVE";
        });
        
        games.add(new Object() {
            public final String id = "roulette_european";
            public final String name = "欧洲轮盘";
            public final String provider = "NetEnt";
            public final String category = "ROULETTE";
            public final boolean isLive = false;
            public final int minBet = 1;
            public final int maxBet = 5000;
            public final String status = "ACTIVE";
        });
        
        return Result.success("获取游戏列表成功", games);
    }
    

    /**
     * 健康检查
     */
    @GetMapping("/health")
    public Result<String> health() {
        return Result.success("代理游戏服务运行正常");
    }
}
