server:
  port: 8083

spring:
  application:
    name: game-agentgame
  profiles:
    active: dev
  config:
    import:
      - optional:nacos:game-agentgame.yml
  cloud:
    nacos:
      discovery:
        server-addr: localhost:8848
        namespace: dev
      config:
        server-addr: localhost:8848
        namespace: dev
        file-extension: yml
        group: DEFAULT_GROUP
  data:
    mongodb:
      host: localhost
      port: 27017
      database: wingame_agentgame
      username: 
      password: 
    redis:
      host: localhost
      port: 6379
      password: 
      database: 2
      timeout: 3000ms
      lettuce:
        pool:
          max-active: 8
          max-wait: -1ms
          max-idle: 8
          min-idle: 0

logging:
  level:
    com.wingame.agentgame: debug
    org.springframework.data.mongodb: debug

# Swagger/OpenAPI配置
springdoc:
  api-docs:
    path: /v3/api-docs
  swagger-ui:
    path: /swagger-ui.html
    enabled: true
  show-actuator: true
