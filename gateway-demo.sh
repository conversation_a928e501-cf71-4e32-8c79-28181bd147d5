#!/bin/bash

echo "=== Spring Cloud Gateway 路由测试 Demo ==="
echo ""

GATEWAY_URL="http://localhost:8080"
USER_ID="demo_user_123"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 测试函数
test_api() {
    local method=$1
    local url=$2
    local data=$3
    local description=$4
    
    echo -e "${BLUE}测试: $description${NC}"
    echo -e "${YELLOW}请求: $method $url${NC}"
    
    if [ -n "$data" ]; then
        echo -e "${YELLOW}数据: $data${NC}"
        response=$(curl -s -X $method "$url" \
            -H "Content-Type: application/json" \
            -d "$data" \
            -w "\nHTTP_CODE:%{http_code}")
    else
        response=$(curl -s -X $method "$url" \
            -w "\nHTTP_CODE:%{http_code}")
    fi
    
    http_code=$(echo "$response" | grep "HTTP_CODE:" | cut -d: -f2)
    body=$(echo "$response" | sed '/HTTP_CODE:/d')
    
    if [ "$http_code" = "200" ]; then
        echo -e "${GREEN}✅ 成功 (HTTP $http_code)${NC}"
        echo "$body" | python3 -m json.tool 2>/dev/null || echo "$body"
    else
        echo -e "${RED}❌ 失败 (HTTP $http_code)${NC}"
        echo "$body"
    fi
    echo ""
    echo "----------------------------------------"
    echo ""
}

echo "开始测试网关路由到各个微服务..."
echo ""

# 1. 测试用户服务
echo -e "${BLUE}=== 1. 用户服务测试 ===${NC}"

# 用户注册
test_api "POST" "$GATEWAY_URL/user/api/user/register" '{
    "username": "testuser001",
    "password": "123456",
    "confirmPassword": "123456",
    "nickname": "测试用户001",
    "email": "<EMAIL>",
    "phone": "13800138001"
}' "用户注册"

# 用户登录
test_api "POST" "$GATEWAY_URL/user/api/user/login" '{
    "username": "testuser001",
    "password": "123456"
}' "用户登录"

# 检查用户名
test_api "GET" "$GATEWAY_URL/user/api/user/check/username/testuser001" "" "检查用户名是否存在"

# 2. 测试大厅服务
echo -e "${BLUE}=== 2. 大厅服务测试 ===${NC}"

# 获取所有房间
test_api "GET" "$GATEWAY_URL/hall/api/hall/rooms" "" "获取所有游戏房间"

# 获取大厅统计
test_api "GET" "$GATEWAY_URL/hall/api/hall/stats" "" "获取大厅统计信息"

# 创建房间
test_api "POST" "$GATEWAY_URL/hall/api/hall/rooms" '{
    "roomName": "测试房间001",
    "gameType": "POKER",
    "roomType": 1,
    "maxPlayers": 6,
    "minBet": 100,
    "maxBet": 5000
}' "创建游戏房间"

# 加入房间
test_api "POST" "$GATEWAY_URL/hall/api/hall/rooms/test-room-123/join?userId=$USER_ID" "" "加入游戏房间"

# 3. 测试代理游戏服务
echo -e "${BLUE}=== 3. 代理游戏服务测试 ===${NC}"

# 获取可用游戏
test_api "GET" "$GATEWAY_URL/agentgame/api/agentgame/games" "" "获取可用游戏列表"

# 获取游戏详情
test_api "GET" "$GATEWAY_URL/agentgame/api/agentgame/games/texas_holdem" "" "获取游戏详情"

# 启动游戏
test_api "POST" "$GATEWAY_URL/agentgame/api/agentgame/games/texas_holdem/launch?userId=$USER_ID" "" "启动游戏"

# 获取游戏历史
test_api "GET" "$GATEWAY_URL/agentgame/api/agentgame/history?userId=$USER_ID&page=0&size=5" "" "获取游戏历史记录"

# 获取游戏统计
test_api "GET" "$GATEWAY_URL/agentgame/api/agentgame/stats?userId=$USER_ID" "" "获取游戏统计"

# 4. 测试活动服务
echo -e "${BLUE}=== 4. 活动服务测试 ===${NC}"

# 获取活动列表
test_api "GET" "$GATEWAY_URL/activity/api/activity/list" "" "获取所有活动"

# 获取活动详情
test_api "GET" "$GATEWAY_URL/activity/api/activity/test-activity-123" "" "获取活动详情"

# 参与活动
test_api "POST" "$GATEWAY_URL/activity/api/activity/test-activity-123/participate?userId=$USER_ID" "" "参与活动"

# 每日签到
test_api "POST" "$GATEWAY_URL/activity/api/activity/checkin?userId=$USER_ID" "" "每日签到"

# 获取用户活动
test_api "GET" "$GATEWAY_URL/activity/api/activity/user/$USER_ID" "" "获取用户参与的活动"

# 5. 测试账户服务
echo -e "${BLUE}=== 5. 账户服务测试 ===${NC}"

# 获取账户余额
test_api "GET" "$GATEWAY_URL/account/api/account/$USER_ID/balance" "" "获取账户余额"

# 充值
test_api "POST" "$GATEWAY_URL/account/api/account/$USER_ID/recharge?amount=1000&paymentMethod=alipay" "" "账户充值"

# 获取交易记录
test_api "GET" "$GATEWAY_URL/account/api/account/$USER_ID/transactions?page=0&size=5" "" "获取交易记录"

# 获取账户统计
test_api "GET" "$GATEWAY_URL/account/api/account/$USER_ID/statistics" "" "获取账户统计"

# 获取支付方式
test_api "GET" "$GATEWAY_URL/account/api/account/payment-methods" "" "获取支付方式"

# 转账
test_api "POST" "$GATEWAY_URL/account/api/account/$USER_ID/transfer?toUserId=target_user_456&amount=500&remark=测试转账" "" "用户转账"

# 6. 健康检查
echo -e "${BLUE}=== 6. 服务健康检查 ===${NC}"

test_api "GET" "$GATEWAY_URL/user/api/user/health" "" "用户服务健康检查"
test_api "GET" "$GATEWAY_URL/hall/api/hall/health" "" "大厅服务健康检查"
test_api "GET" "$GATEWAY_URL/agentgame/api/agentgame/health" "" "代理游戏服务健康检查"
test_api "GET" "$GATEWAY_URL/activity/api/activity/health" "" "活动服务健康检查"
test_api "GET" "$GATEWAY_URL/account/api/account/health" "" "账户服务健康检查"

echo -e "${GREEN}=== 网关路由测试完成 ===${NC}"
echo ""
echo -e "${YELLOW}📋 测试总结:${NC}"
echo "1. 用户服务: 注册、登录、用户信息查询"
echo "2. 大厅服务: 房间管理、统计信息"
echo "3. 代理游戏服务: 游戏列表、启动游戏、历史记录"
echo "4. 活动服务: 活动管理、签到、参与活动"
echo "5. 账户服务: 余额查询、充值提现、交易记录"
echo ""
echo -e "${YELLOW}🔗 网关路由配置:${NC}"
echo "- /user/** -> game-user 服务"
echo "- /hall/** -> game-hall 服务"
echo "- /agentgame/** -> game-agentgame 服务"
echo "- /activity/** -> game-activity 服务"
echo "- /account/** -> game-account 服务"
echo ""
echo -e "${YELLOW}📱 前端可以通过网关统一访问所有服务:${NC}"
echo "- 网关地址: http://localhost:8080"
echo "- 所有API请求都通过网关转发到对应的微服务"
echo "- 支持负载均衡和服务发现"
