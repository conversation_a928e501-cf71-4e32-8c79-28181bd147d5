#!/bin/bash

echo "=== 重启用户服务 ==="
echo ""

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 停止用户服务
echo -e "${BLUE}停止用户服务...${NC}"
pkill -f "game-user.*jar" 2>/dev/null || true
sleep 2

# 重新编译用户服务
echo -e "${BLUE}重新编译用户服务...${NC}"
if mvn clean package -pl game-user -am -DskipTests -q; then
    echo -e "${GREEN}✅ 编译成功${NC}"
else
    echo -e "${RED}❌ 编译失败${NC}"
    exit 1
fi

# 启动用户服务
echo -e "${BLUE}启动用户服务...${NC}"
cd game-user
nohup java -jar target/game-user-1.0-SNAPSHOT.jar > ../logs/user.log 2>&1 &
USER_PID=$!
cd ..

echo "用户服务启动中，PID: $USER_PID"

# 等待服务启动
echo -e "${BLUE}等待服务启动...${NC}"
for i in {1..30}; do
    if curl -s http://localhost:8081/actuator/health > /dev/null 2>&1; then
        echo -e "${GREEN}✅ 用户服务启动成功！${NC}"
        break
    elif curl -s http://localhost:8081 > /dev/null 2>&1; then
        echo -e "${GREEN}✅ 用户服务启动成功！${NC}"
        break
    else
        echo -n "."
        sleep 2
    fi
    
    if [ $i -eq 30 ]; then
        echo ""
        echo -e "${RED}❌ 用户服务启动超时${NC}"
        echo "查看日志: tail -f logs/user.log"
        exit 1
    fi
done

echo ""

# 测试用户服务
echo -e "${BLUE}测试用户服务...${NC}"

# 测试健康检查
if curl -s http://localhost:8081/actuator/health > /dev/null 2>&1; then
    echo -e "${GREEN}✅ 健康检查通过${NC}"
elif curl -s http://localhost:8081 > /dev/null 2>&1; then
    echo -e "${GREEN}✅ 服务响应正常${NC}"
else
    echo -e "${YELLOW}⚠️ 健康检查失败，但服务可能正在启动${NC}"
fi

# 测试通过网关访问
echo -e "${BLUE}测试网关路由...${NC}"
response=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8080/user/api/user/health)

if [ "$response" = "200" ]; then
    echo -e "${GREEN}✅ 网关路由正常${NC}"
elif [ "$response" = "503" ]; then
    echo -e "${YELLOW}⚠️ 网关返回503，服务可能还在启动中${NC}"
    echo "等待10秒后重试..."
    sleep 10
    
    response=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8080/user/api/user/health)
    if [ "$response" = "200" ]; then
        echo -e "${GREEN}✅ 网关路由现在正常${NC}"
    else
        echo -e "${RED}❌ 网关路由仍有问题 (HTTP $response)${NC}"
    fi
else
    echo -e "${RED}❌ 网关路由失败 (HTTP $response)${NC}"
fi

echo ""
echo -e "${GREEN}🎉 用户服务重启完成！${NC}"
echo ""
echo -e "${BLUE}测试建议:${NC}"
echo "1. 直接访问: curl http://localhost:8081/api/user/health"
echo "2. 通过网关: curl http://localhost:8080/user/api/user/health"
echo "3. 用户注册: curl -X POST http://localhost:8080/user/api/user/register ..."
echo "4. 查看日志: tail -f logs/user.log"
