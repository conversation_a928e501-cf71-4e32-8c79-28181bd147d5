#!/bin/bash

echo "编译并启动微服务..."

# 检查Java版本
echo "检查Java版本..."
java -version

# 检查Maven是否安装
if ! command -v mvn &> /dev/null; then
    echo "错误: Maven未安装，请先安装Maven"
    exit 1
fi

# 编译项目
echo "编译项目..."
mvn clean package -DskipTests

# 启动网关服务
echo "启动网关服务..."
cd game-gateway
nohup java -jar target/game-gateway-1.0-SNAPSHOT.jar > ../logs/gateway.log 2>&1 &
cd ..

sleep 10

# 启动用户服务
echo "启动用户服务..."
cd game-user
nohup java -jar target/game-user-1.0-SNAPSHOT.jar > ../logs/user.log 2>&1 &
cd ..

sleep 5

# 启动大厅服务
echo "启动大厅服务..."
cd game-hall
nohup java -jar target/game-hall-1.0-SNAPSHOT.jar > ../logs/hall.log 2>&1 &
cd ..

sleep 5

# 启动代理游戏服务
echo "启动代理游戏服务..."
cd game-agentGame
nohup java -jar target/game-agentGame-1.0-SNAPSHOT.jar > ../logs/agentgame.log 2>&1 &
cd ..

sleep 5

# 启动活动服务
echo "启动活动服务..."
cd game-activity
nohup java -jar target/game-activity-1.0-SNAPSHOT.jar > ../logs/activity.log 2>&1 &
cd ..

sleep 5

# 启动账户服务
echo "启动账户服务..."
cd game-account
nohup java -jar target/game-account-1.0-SNAPSHOT.jar > ../logs/account.log 2>&1 &
cd ..

echo "所有服务启动完成！"
echo "网关服务: http://localhost:8080"
echo "用户服务: http://localhost:8081"
echo "大厅服务: http://localhost:8082"
echo "代理游戏服务: http://localhost:8083"
echo "活动服务: http://localhost:8084"
echo "账户服务: http://localhost:8085"
