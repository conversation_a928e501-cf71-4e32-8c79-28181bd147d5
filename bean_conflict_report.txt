# Bean冲突检查报告
生成时间: Tue Jun 24 10:53:24 CST 2025

## 扫描的服务
- game-user
- game-hall
- game-agentGame
- game-activity
- game-account
- game-gateway

## Bean定义统计
- @Service:        2
- @Component:        3
- @FeignClient:        3
- @Configuration:       10

## 详细Bean列表
- ./game-user/src/main/java/com/wingame/user/feign/AccountFeignClient.java
  类名: AccountFeignClient
  注解: @FeignClient @GetMapping @PathVariable @GetMapping @PathVariable 

- ./game-user/src/main/java/com/wingame/user/feign/AccountFeignClientFallback.java
  类名: AccountFeignClientFallback
  注解: @Slf @Component @Override @Override 

- ./game-user/src/main/java/com/wingame/user/service/impl/UserAggregationServiceImpl.java
  类名: UserAggregationServiceImpl
  注解: @Slf @Service @RequiredArgsConstructor @Override @Override 

- ./game-user/src/main/java/com/wingame/user/service/impl/UserServiceImpl.java
  类名: UserServiceImpl
  注解: @Slf @Service @RequiredArgsConstructor @Override @Override 

- ./game-gateway/src/main/java/com/wingame/gateway/feign/UserFeignClientFallback.java
  类名: UserFeignClientFallback
  注解: @Slf @Component @Override @Override @Override 

- ./game-gateway/src/main/java/com/wingame/gateway/feign/AccountFeignClient.java
  类名: AccountFeignClient
  注解: @FeignClient @GetMapping @PathVariable @GetMapping @PathVariable 

- ./game-gateway/src/main/java/com/wingame/gateway/feign/AccountFeignClientFallback.java
  类名: AccountFeignClientFallback
  注解: @Slf @Component @Override @Override @Override 

- ./game-gateway/src/main/java/com/wingame/gateway/feign/UserFeignClient.java
  类名: UserFeignClient
  注解: @FeignClient @GetMapping @PathVariable @GetMapping @PathVariable 

