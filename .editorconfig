# EditorConfig is awesome: https://EditorConfig.org

# top-most EditorConfig file
root = true

# All files
[*]
charset = utf-8
end_of_line = lf
insert_final_newline = true
trim_trailing_whitespace = true

# Java files
[*.java]
indent_style = space
indent_size = 4
max_line_length = 120
continuation_indent_size = 8

# XML files (Maven <PERSON>, Spring configs)
[*.xml]
indent_style = space
indent_size = 4
max_line_length = 120

# YAML files (Spring Boot configs)
[*.{yml,yaml}]
indent_style = space
indent_size = 2
max_line_length = 120

# Properties files
[*.properties]
indent_style = space
indent_size = 4
max_line_length = 120

# JSON files
[*.json]
indent_style = space
indent_size = 2
max_line_length = 120

# JavaScript/TypeScript files
[*.{js,ts,jsx,tsx}]
indent_style = space
indent_size = 2
max_line_length = 100

# CSS/SCSS files
[*.{css,scss,less}]
indent_style = space
indent_size = 2
max_line_length = 100

# HTML files
[*.html]
indent_style = space
indent_size = 2
max_line_length = 120

# Markdown files
[*.md]
indent_style = space
indent_size = 2
max_line_length = 80
trim_trailing_whitespace = false

# Shell scripts
[*.sh]
indent_style = space
indent_size = 4
max_line_length = 120

# Docker files
[{Dockerfile,*.dockerfile}]
indent_style = space
indent_size = 4
max_line_length = 120

# SQL files
[*.sql]
indent_style = space
indent_size = 2
max_line_length = 120

# Configuration files
[*.{ini,cfg,conf}]
indent_style = space
indent_size = 4
