<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WinGame 微服务网关 Demo</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .content {
            padding: 30px;
        }
        
        .service-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .service-card {
            border: 2px solid #e1e8ed;
            border-radius: 10px;
            padding: 20px;
            transition: all 0.3s ease;
        }
        
        .service-card:hover {
            border-color: #667eea;
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.1);
        }
        
        .service-card h3 {
            color: #667eea;
            margin-bottom: 15px;
            font-size: 1.3em;
        }
        
        .api-list {
            list-style: none;
        }
        
        .api-item {
            margin-bottom: 10px;
            padding: 8px 12px;
            background: #f8f9fa;
            border-radius: 5px;
            cursor: pointer;
            transition: background 0.3s ease;
        }
        
        .api-item:hover {
            background: #e9ecef;
        }
        
        .method {
            font-weight: bold;
            margin-right: 10px;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 0.8em;
        }
        
        .method.get { background: #28a745; color: white; }
        .method.post { background: #007bff; color: white; }
        .method.put { background: #ffc107; color: black; }
        .method.delete { background: #dc3545; color: white; }
        
        .test-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-top: 30px;
        }
        
        .test-controls {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: #667eea;
            color: white;
        }
        
        .btn-primary:hover {
            background: #5a6fd8;
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
        
        .btn-success:hover {
            background: #218838;
        }
        
        .btn-info {
            background: #17a2b8;
            color: white;
        }
        
        .btn-info:hover {
            background: #138496;
        }
        
        .result-area {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        
        .status-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-online { background: #28a745; }
        .status-offline { background: #dc3545; }
        .status-unknown { background: #6c757d; }
        
        .route-info {
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 15px;
            margin: 20px 0;
            border-radius: 0 5px 5px 0;
        }
        
        .route-info h4 {
            color: #1976d2;
            margin-bottom: 10px;
        }
        
        .route-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }
        
        .route-table th,
        .route-table td {
            padding: 8px 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        
        .route-table th {
            background: #f5f5f5;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎮 WinGame 微服务网关 Demo</h1>
            <p>Spring Cloud Gateway 路由测试平台</p>
        </div>
        
        <div class="content">
            <div class="route-info">
                <h4>🔗 网关路由配置</h4>
                <table class="route-table">
                    <thead>
                        <tr>
                            <th>路由路径</th>
                            <th>目标服务</th>
                            <th>服务端口</th>
                            <th>状态</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>/user/**</td>
                            <td>game-user</td>
                            <td>8081</td>
                            <td><span class="status-indicator status-unknown"></span>检测中</td>
                        </tr>
                        <tr>
                            <td>/hall/**</td>
                            <td>game-hall</td>
                            <td>8082</td>
                            <td><span class="status-indicator status-unknown"></span>检测中</td>
                        </tr>
                        <tr>
                            <td>/agentgame/**</td>
                            <td>game-agentgame</td>
                            <td>8083</td>
                            <td><span class="status-indicator status-unknown"></span>检测中</td>
                        </tr>
                        <tr>
                            <td>/activity/**</td>
                            <td>game-activity</td>
                            <td>8084</td>
                            <td><span class="status-indicator status-unknown"></span>检测中</td>
                        </tr>
                        <tr>
                            <td>/account/**</td>
                            <td>game-account</td>
                            <td>8085</td>
                            <td><span class="status-indicator status-unknown"></span>检测中</td>
                        </tr>
                    </tbody>
                </table>
            </div>
            
            <div class="service-grid">
                <div class="service-card">
                    <h3>👤 用户服务 (User Service)</h3>
                    <ul class="api-list">
                        <li class="api-item" onclick="testApi('POST', '/user/api/user/register', 'register')">
                            <span class="method post">POST</span>用户注册
                        </li>
                        <li class="api-item" onclick="testApi('POST', '/user/api/user/login', 'login')">
                            <span class="method post">POST</span>用户登录
                        </li>
                        <li class="api-item" onclick="testApi('GET', '/user/api/user/check/username/testuser001')">
                            <span class="method get">GET</span>检查用户名
                        </li>
                        <li class="api-item" onclick="testApi('GET', '/user/api/user/health')">
                            <span class="method get">GET</span>健康检查
                        </li>
                    </ul>
                </div>
                
                <div class="service-card">
                    <h3>🏛️ 大厅服务 (Hall Service)</h3>
                    <ul class="api-list">
                        <li class="api-item" onclick="testApi('GET', '/hall/api/hall/rooms')">
                            <span class="method get">GET</span>获取房间列表
                        </li>
                        <li class="api-item" onclick="testApi('GET', '/hall/api/hall/stats')">
                            <span class="method get">GET</span>大厅统计
                        </li>
                        <li class="api-item" onclick="testApi('POST', '/hall/api/hall/rooms', 'createRoom')">
                            <span class="method post">POST</span>创建房间
                        </li>
                        <li class="api-item" onclick="testApi('GET', '/hall/api/hall/health')">
                            <span class="method get">GET</span>健康检查
                        </li>
                    </ul>
                </div>
                
                <div class="service-card">
                    <h3>🎯 代理游戏服务 (Agent Game)</h3>
                    <ul class="api-list">
                        <li class="api-item" onclick="testApi('GET', '/agentgame/api/agentgame/games')">
                            <span class="method get">GET</span>获取游戏列表
                        </li>
                        <li class="api-item" onclick="testApi('GET', '/agentgame/api/agentgame/games/texas_holdem')">
                            <span class="method get">GET</span>游戏详情
                        </li>
                        <li class="api-item" onclick="testApi('POST', '/agentgame/api/agentgame/games/texas_holdem/launch?userId=demo_user_123')">
                            <span class="method post">POST</span>启动游戏
                        </li>
                        <li class="api-item" onclick="testApi('GET', '/agentgame/api/agentgame/health')">
                            <span class="method get">GET</span>健康检查
                        </li>
                    </ul>
                </div>
                
                <div class="service-card">
                    <h3>🎉 活动服务 (Activity Service)</h3>
                    <ul class="api-list">
                        <li class="api-item" onclick="testApi('GET', '/activity/api/activity/list')">
                            <span class="method get">GET</span>活动列表
                        </li>
                        <li class="api-item" onclick="testApi('POST', '/activity/api/activity/checkin?userId=demo_user_123')">
                            <span class="method post">POST</span>每日签到
                        </li>
                        <li class="api-item" onclick="testApi('POST', '/activity/api/activity/test-activity-123/participate?userId=demo_user_123')">
                            <span class="method post">POST</span>参与活动
                        </li>
                        <li class="api-item" onclick="testApi('GET', '/activity/api/activity/health')">
                            <span class="method get">GET</span>健康检查
                        </li>
                    </ul>
                </div>
                
                <div class="service-card">
                    <h3>💰 账户服务 (Account Service)</h3>
                    <ul class="api-list">
                        <li class="api-item" onclick="testApi('GET', '/account/api/account/demo_user_123/balance')">
                            <span class="method get">GET</span>账户余额
                        </li>
                        <li class="api-item" onclick="testApi('POST', '/account/api/account/demo_user_123/recharge?amount=1000&paymentMethod=alipay')">
                            <span class="method post">POST</span>账户充值
                        </li>
                        <li class="api-item" onclick="testApi('GET', '/account/api/account/demo_user_123/transactions?page=0&size=5')">
                            <span class="method get">GET</span>交易记录
                        </li>
                        <li class="api-item" onclick="testApi('GET', '/account/api/account/health')">
                            <span class="method get">GET</span>健康检查
                        </li>
                    </ul>
                </div>
            </div>
            
            <div class="test-section">
                <h3>🧪 API 测试控制台</h3>
                <div class="test-controls">
                    <button class="btn btn-primary" onclick="runAllHealthChecks()">检查所有服务状态</button>
                    <button class="btn btn-success" onclick="runUserFlow()">运行用户流程测试</button>
                    <button class="btn btn-info" onclick="clearResults()">清空结果</button>
                </div>
                <div id="results" class="result-area">点击上方的API项目开始测试...\n\n网关地址: http://localhost:8080\n所有请求将通过网关路由到对应的微服务\n</div>
            </div>
        </div>
    </div>

    <script>
        const GATEWAY_URL = 'http://localhost:8080';
        
        // 预定义的请求数据
        const requestData = {
            register: {
                username: 'testuser' + Date.now(),
                password: '123456',
                confirmPassword: '123456',
                nickname: '测试用户',
                email: '<EMAIL>',
                phone: '13800138000'
            },
            login: {
                username: 'testuser001',
                password: '123456'
            },
            createRoom: {
                roomName: '测试房间' + Date.now(),
                gameType: 'POKER',
                roomType: 1,
                maxPlayers: 6,
                minBet: 100,
                maxBet: 5000
            }
        };
        
        function log(message) {
            const results = document.getElementById('results');
            results.textContent += new Date().toLocaleTimeString() + ' - ' + message + '\n';
            results.scrollTop = results.scrollHeight;
        }
        
        async function testApi(method, path, dataKey = null) {
            const url = GATEWAY_URL + path;
            log(`🚀 ${method} ${path}`);
            
            try {
                const options = {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json',
                    }
                };
                
                if (dataKey && requestData[dataKey]) {
                    options.body = JSON.stringify(requestData[dataKey]);
                    log(`📤 请求数据: ${JSON.stringify(requestData[dataKey], null, 2)}`);
                }
                
                const response = await fetch(url, options);
                const data = await response.json();
                
                if (response.ok) {
                    log(`✅ 成功 (${response.status}): ${JSON.stringify(data, null, 2)}`);
                } else {
                    log(`❌ 失败 (${response.status}): ${JSON.stringify(data, null, 2)}`);
                }
            } catch (error) {
                log(`💥 错误: ${error.message}`);
            }
            
            log('─'.repeat(50));
        }
        
        async function runAllHealthChecks() {
            log('🔍 开始检查所有服务状态...');
            
            const services = [
                '/user/api/user/health',
                '/hall/api/hall/health',
                '/agentgame/api/agentgame/health',
                '/activity/api/activity/health',
                '/account/api/account/health'
            ];
            
            for (const service of services) {
                await testApi('GET', service);
                await new Promise(resolve => setTimeout(resolve, 500));
            }
            
            log('✨ 服务状态检查完成');
        }
        
        async function runUserFlow() {
            log('🎯 开始运行用户流程测试...');
            
            // 1. 用户注册
            await testApi('POST', '/user/api/user/register', 'register');
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // 2. 用户登录
            await testApi('POST', '/user/api/user/login', 'login');
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // 3. 获取账户余额
            await testApi('GET', '/account/api/account/demo_user_123/balance');
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // 4. 获取游戏列表
            await testApi('GET', '/agentgame/api/agentgame/games');
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // 5. 获取活动列表
            await testApi('GET', '/activity/api/activity/list');
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // 6. 每日签到
            await testApi('POST', '/activity/api/activity/checkin?userId=demo_user_123');
            
            log('🎉 用户流程测试完成');
        }
        
        function clearResults() {
            document.getElementById('results').textContent = '控制台已清空，准备开始新的测试...\n\n';
        }
        
        // 页面加载时自动检查服务状态
        window.onload = function() {
            log('🌟 WinGame 微服务网关 Demo 已启动');
            log('📡 网关地址: ' + GATEWAY_URL);
            log('🔧 点击上方的API项目开始测试');
            log('═'.repeat(50));
        };
    </script>
</body>
</html>
