#!/bin/bash

echo "=== Nacos服务注册修复脚本 ==="
echo ""

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 检查Nacos状态
check_nacos() {
    echo -e "${BLUE}检查Nacos状态...${NC}"
    
    if curl -s http://localhost:8848/nacos > /dev/null 2>&1; then
        echo -e "${GREEN}✅ Nacos运行正常${NC}"
        return 0
    else
        echo -e "${RED}❌ Nacos未运行${NC}"
        echo "请先启动Nacos: ./start-infrastructure.sh"
        return 1
    fi
}

# 检查服务注册状态
check_service_registration() {
    echo -e "${BLUE}检查服务注册状态...${NC}"
    
    services=("game-user" "game-hall" "game-agentgame" "game-activity" "game-account" "game-gateway")
    
    registered_services=0
    
    for service in "${services[@]}"; do
        response=$(curl -s "http://localhost:8848/nacos/v1/ns/instance/list?serviceName=$service")
        
        if echo "$response" | grep -q '"count":[1-9]'; then
            echo -e "${GREEN}✅ $service 已注册${NC}"
            ((registered_services++))
        else
            echo -e "${RED}❌ $service 未注册${NC}"
        fi
    done
    
    echo ""
    echo "已注册服务: $registered_services/${#services[@]}"
    
    if [ $registered_services -eq 0 ]; then
        return 1
    fi
    
    return 0
}

# 修复Redis连接配置
fix_redis_config() {
    echo -e "${BLUE}修复Redis连接配置...${NC}"
    
    # 检查Redis是否需要密码
    if docker exec redis redis-cli ping 2>/dev/null | grep -q PONG; then
        echo -e "${YELLOW}Redis无需密码，更新配置...${NC}"
        
        # 更新所有服务的Redis配置，移除密码
        services=("game-user" "game-hall" "game-agentGame" "game-activity" "game-account")
        
        for service in "${services[@]}"; do
            config_file="$service/src/main/resources/application.yml"
            if [ -f "$config_file" ]; then
                # 注释掉Redis密码配置
                sed -i.bak 's/password: redis123/#password: redis123/' "$config_file"
                echo -e "${GREEN}✅ 更新 $service Redis配置${NC}"
            fi
        done
    else
        echo -e "${GREEN}✅ Redis密码配置正确${NC}"
    fi
}

# 重新编译所有服务
rebuild_services() {
    echo -e "${BLUE}重新编译所有服务...${NC}"
    
    if mvn clean package -DskipTests -q; then
        echo -e "${GREEN}✅ 编译成功${NC}"
        return 0
    else
        echo -e "${RED}❌ 编译失败${NC}"
        return 1
    fi
}

# 停止所有微服务
stop_services() {
    echo -e "${BLUE}停止所有微服务...${NC}"
    
    pkill -f "game-.*jar" 2>/dev/null || true
    sleep 3
    
    echo -e "${GREEN}✅ 服务已停止${NC}"
}

# 启动单个服务并等待注册
start_service_and_wait() {
    local service_name=$1
    local service_port=$2
    local jar_path=$3
    
    echo -e "${CYAN}启动 $service_name...${NC}"
    
    cd "$service_name"
    nohup java -jar "$jar_path" > "../logs/${service_name#game-}.log" 2>&1 &
    local pid=$!
    cd ..
    
    echo "  PID: $pid"
    
    # 等待服务启动
    for i in {1..30}; do
        if curl -s "http://localhost:$service_port" > /dev/null 2>&1; then
            echo -e "${GREEN}  ✅ $service_name 启动成功${NC}"
            break
        else
            echo -n "."
            sleep 2
        fi
        
        if [ $i -eq 30 ]; then
            echo ""
            echo -e "${RED}  ❌ $service_name 启动超时${NC}"
            return 1
        fi
    done
    
    # 等待Nacos注册
    echo -n "  等待Nacos注册"
    for i in {1..20}; do
        response=$(curl -s "http://localhost:8848/nacos/v1/ns/instance/list?serviceName=$service_name")
        
        if echo "$response" | grep -q '"count":[1-9]'; then
            echo ""
            echo -e "${GREEN}  ✅ $service_name 已注册到Nacos${NC}"
            return 0
        else
            echo -n "."
            sleep 3
        fi
    done
    
    echo ""
    echo -e "${YELLOW}  ⚠️ $service_name 启动成功但未注册到Nacos${NC}"
    return 0
}

# 启动所有服务
start_all_services() {
    echo -e "${BLUE}启动所有微服务...${NC}"
    
    # 创建logs目录
    mkdir -p logs
    
    # 按顺序启动服务
    services=(
        "game-gateway:8080:target/game-gateway-1.0-SNAPSHOT.jar"
        "game-user:8081:target/game-user-1.0-SNAPSHOT.jar"
        "game-hall:8082:target/game-hall-1.0-SNAPSHOT.jar"
        "game-agentGame:8083:target/game-agentGame-1.0-SNAPSHOT.jar"
        "game-activity:8084:target/game-activity-1.0-SNAPSHOT.jar"
        "game-account:8085:target/game-account-1.0-SNAPSHOT.jar"
    )
    
    for service_info in "${services[@]}"; do
        IFS=':' read -r service_name service_port jar_path <<< "$service_info"
        
        if [ -f "$service_name/$jar_path" ]; then
            start_service_and_wait "$service_name" "$service_port" "$jar_path"
            echo ""
        else
            echo -e "${RED}❌ $service_name JAR文件不存在${NC}"
        fi
    done
}

# 验证最终状态
verify_final_state() {
    echo -e "${BLUE}验证最终状态...${NC}"
    
    echo ""
    echo "=== 服务状态 ==="
    check_service_registration
    
    echo ""
    echo "=== 网关路由测试 ==="
    routes=(
        "用户服务:/user/api/user/health"
        "大厅服务:/hall/api/hall/health"
        "代理游戏服务:/agentgame/api/agentgame/health"
        "活动服务:/activity/api/activity/health"
        "账户服务:/account/api/account/health"
    )
    
    for route in "${routes[@]}"; do
        IFS=':' read -r name path <<< "$route"
        
        response=$(curl -s -o /dev/null -w "%{http_code}" "http://localhost:8080$path")
        
        if [ "$response" = "200" ]; then
            echo -e "${GREEN}✅ $name 路由正常${NC}"
        else
            echo -e "${RED}❌ $name 路由失败 (HTTP $response)${NC}"
        fi
    done
}

# 主函数
main() {
    echo -e "${CYAN}开始修复Nacos服务注册问题...${NC}"
    echo ""
    
    # 1. 检查Nacos
    if ! check_nacos; then
        exit 1
    fi
    
    echo ""
    
    # 2. 检查当前注册状态
    if check_service_registration; then
        echo -e "${GREEN}部分服务已注册，继续检查...${NC}"
    else
        echo -e "${YELLOW}没有服务注册，开始修复...${NC}"
    fi
    
    echo ""
    
    # 3. 修复Redis配置
    fix_redis_config
    
    echo ""
    
    # 4. 重新编译
    if ! rebuild_services; then
        exit 1
    fi
    
    echo ""
    
    # 5. 停止现有服务
    stop_services
    
    echo ""
    
    # 6. 启动所有服务
    start_all_services
    
    echo ""
    
    # 7. 验证最终状态
    verify_final_state
    
    echo ""
    echo -e "${GREEN}🎉 Nacos服务注册修复完成！${NC}"
    echo ""
    echo -e "${BLUE}下一步建议:${NC}"
    echo "1. 访问Nacos控制台: http://localhost:8848/nacos"
    echo "2. 运行完整测试: ./complete-demo.sh"
    echo "3. 查看服务日志: tail -f logs/*.log"
}

# 运行主函数
main
