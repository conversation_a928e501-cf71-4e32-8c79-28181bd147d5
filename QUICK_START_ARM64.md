# ARM64 (Apple Silicon) 快速启动指南

## 🚀 一键解决方案

如果您在Apple Silicon Mac上遇到 `no matching manifest for linux/arm64/v8` 错误，请按以下步骤操作：

### 1. 使用优化的启动脚本

```bash
# 自动检测架构并启动（推荐）
./start-infrastructure.sh
```

这个脚本会：
- ✅ 自动检测您的系统架构
- ✅ 选择合适的Docker配置文件
- ✅ 处理ARM64兼容性问题
- ✅ 提供详细的启动状态信息

### 2. 手动选择配置（备选方案）

如果自动启动失败，可以手动选择配置：

```bash
# Apple Silicon Mac 使用ARM64优化配置
docker-compose -f docker-compose-arm64.yml up -d

# Intel Mac 使用标准配置
docker-compose -f docker-compose.yml up -d
```

## 📋 配置差异说明

| 配置文件 | 适用架构 | Nacos配置 | MySQL | 启动速度 | 资源占用 |
|----------|----------|-----------|-------|----------|----------|
| `docker-compose.yml` | Intel/AMD | 完整版 | ✅ | 慢 | 高 |
| `docker-compose-arm64.yml` | Apple Silicon | 简化版 | ❌ | 快 | 低 |

## 🔧 故障排除

### 问题1: Docker未运行
```bash
# 启动Docker Desktop
open -a Docker
```

### 问题2: 端口被占用
```bash
# 检查端口占用
lsof -i :8848  # Nacos
lsof -i :27017 # MongoDB
lsof -i :6379  # Redis

# 停止占用端口的进程
sudo kill -9 <PID>
```

### 问题3: 镜像拉取失败
```bash
# 手动拉取镜像
docker pull mongo:6.0
docker pull redis:7.0-alpine
docker pull nacos/nacos-server:v2.2.1
```

### 问题4: Nacos启动慢
```bash
# 等待更长时间（正常现象）
sleep 120

# 查看启动日志
docker logs nacos
```

## ✅ 验证启动成功

### 1. 检查容器状态
```bash
docker ps
```
应该看到3个运行中的容器：nacos、mongodb、redis

### 2. 访问服务
- **Nacos控制台**: http://localhost:8848/nacos
  - 用户名/密码: nacos/nacos
- **MongoDB**: localhost:27017
  - 用户名/密码: admin/admin123
- **Redis**: localhost:6379
  - 密码: redis123

### 3. 启动微服务
```bash
./start-services.sh
```

### 4. 测试网关路由
```bash
./complete-demo.sh
```

## 🎯 Apple Silicon 优化建议

### 1. 性能优化
- ✅ 使用ARM64原生镜像
- ✅ 避免x86模拟（除非必要）
- ✅ 简化服务依赖

### 2. 开发体验
- ✅ 使用轻量级配置
- ✅ 快速启动和停止
- ✅ 低资源占用

### 3. 生产部署
- 根据实际部署环境选择配置
- Intel服务器使用完整配置
- ARM服务器使用优化配置

## 📞 获取帮助

如果仍然遇到问题：

1. **查看详细指南**: `ARM64_COMPATIBILITY_GUIDE.md`
2. **运行兼容性测试**: `./test-docker-compatibility.sh`
3. **检查环境**: `./check-environment.sh`
4. **查看Docker日志**: `docker-compose logs`

## 🎉 成功标志

当您看到以下输出时，说明启动成功：

```
🎉 基础设施服务启动完成！

📋 服务访问信息:
• Nacos控制台: http://localhost:8848/nacos
• MongoDB: localhost:27017
• Redis: localhost:6379
```

现在您可以继续开发您的Spring Cloud微服务应用了！
