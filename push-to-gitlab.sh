#!/bin/bash

echo "=== 推送WinGame项目到GitLab ==="
echo ""

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# GitLab仓库信息
GITLAB_URL="https://gitlab.ttgamelab.com/daniel/wingame-saas-server-new.git"
GITLAB_SSH="************************:daniel/wingame-saas-server-new.git"

echo -e "${BLUE}GitLab仓库信息:${NC}"
echo "• HTTPS: $GITLAB_URL"
echo "• SSH: $GITLAB_SSH"
echo ""

# 检查Git状态
echo -e "${BLUE}检查Git状态...${NC}"
git status --short

echo ""

# 选择推送方式
echo -e "${CYAN}选择推送方式:${NC}"
echo "1. HTTPS (需要用户名和密码/Token)"
echo "2. SSH (需要SSH密钥)"
echo "3. 显示手动推送指令"
echo "4. 退出"
echo ""

echo -n "请选择 (1-4): "
read -r choice

case $choice in
    1)
        echo ""
        echo -e "${CYAN}使用HTTPS方式推送...${NC}"
        echo -e "${YELLOW}提示: 如果启用了2FA，请使用Personal Access Token作为密码${NC}"
        echo ""
        
        # 确保远程仓库是HTTPS
        git remote set-url origin "$GITLAB_URL"
        
        # 推送
        if git push -u origin main; then
            echo -e "${GREEN}✅ 推送成功！${NC}"
        else
            echo -e "${RED}❌ 推送失败${NC}"
            echo -e "${YELLOW}请检查用户名、密码或Token是否正确${NC}"
        fi
        ;;
        
    2)
        echo ""
        echo -e "${CYAN}使用SSH方式推送...${NC}"
        
        # 检查SSH密钥
        if [ ! -f ~/.ssh/id_rsa ]; then
            echo -e "${YELLOW}⚠️ 未找到SSH密钥，正在生成...${NC}"
            echo -n "请输入邮箱地址: "
            read -r email
            ssh-keygen -t rsa -b 4096 -C "$email"
            echo ""
            echo -e "${YELLOW}请将以下公钥添加到GitLab SSH Keys:${NC}"
            echo "----------------------------------------"
            cat ~/.ssh/id_rsa.pub
            echo "----------------------------------------"
            echo ""
            echo -n "添加完成后按回车继续..."
            read -r
        fi
        
        # 设置SSH远程仓库
        git remote set-url origin "$GITLAB_SSH"
        
        # 测试SSH连接
        echo -e "${CYAN}测试SSH连接...${NC}"
        if ssh -T ************************; then
            echo -e "${GREEN}✅ SSH连接成功${NC}"
        else
            echo -e "${YELLOW}⚠️ SSH连接测试失败，但可能仍能推送${NC}"
        fi
        
        # 推送
        if git push -u origin main; then
            echo -e "${GREEN}✅ 推送成功！${NC}"
        else
            echo -e "${RED}❌ 推送失败${NC}"
            echo -e "${YELLOW}请检查SSH密钥是否正确添加到GitLab${NC}"
        fi
        ;;
        
    3)
        echo ""
        echo -e "${CYAN}手动推送指令:${NC}"
        echo ""
        echo -e "${YELLOW}方法1 - HTTPS推送:${NC}"
        echo "git remote set-url origin $GITLAB_URL"
        echo "git push -u origin main"
        echo ""
        echo -e "${YELLOW}方法2 - SSH推送:${NC}"
        echo "git remote set-url origin $GITLAB_SSH"
        echo "git push -u origin main"
        echo ""
        echo -e "${YELLOW}如果需要生成SSH密钥:${NC}"
        echo "ssh-keygen -t rsa -b 4096 -C \"<EMAIL>\""
        echo "cat ~/.ssh/id_rsa.pub  # 复制公钥到GitLab"
        echo ""
        ;;
        
    4)
        echo "退出"
        exit 0
        ;;
        
    *)
        echo -e "${RED}❌ 无效选择${NC}"
        exit 1
        ;;
esac

echo ""

# 推送成功后的操作
if [ $choice -eq 1 ] || [ $choice -eq 2 ]; then
    echo -e "${BLUE}推送成功后的建议操作:${NC}"
    echo ""
    
    echo -e "${CYAN}1. 创建开发分支:${NC}"
    echo "git checkout -b develop"
    echo "git push -u origin develop"
    echo ""
    
    echo -e "${CYAN}2. 访问GitLab项目:${NC}"
    echo "https://gitlab.ttgamelab.com/daniel/wingame-saas-server-new"
    echo ""
    
    echo -e "${CYAN}3. 设置分支保护:${NC}"
    echo "• 进入项目 Settings > Repository > Protected Branches"
    echo "• 保护main分支"
    echo ""
    
    echo -e "${CYAN}4. 邀请团队成员:${NC}"
    echo "• 进入项目 Settings > Members"
    echo "• 添加团队成员"
    echo ""
    
    echo -n "是否现在创建develop分支? (y/N): "
    read -r create_develop
    
    if [[ $create_develop =~ ^[Yy]$ ]]; then
        echo ""
        echo -e "${CYAN}创建develop分支...${NC}"
        
        git checkout -b develop
        git push -u origin develop
        git checkout main
        
        echo -e "${GREEN}✅ develop分支已创建并推送${NC}"
        echo ""
        echo -e "${BLUE}分支说明:${NC}"
        echo "• main: 主分支，用于生产环境"
        echo "• develop: 开发分支，用于日常开发"
    fi
fi

echo ""
echo -e "${GREEN}🎉 GitLab配置完成！${NC}"
echo ""
echo -e "${BLUE}项目信息:${NC}"
echo "• 项目名称: WinGame微服务平台"
echo "• 微服务数量: 6个"
echo "• 技术栈: Spring Cloud + Nacos + MongoDB + Redis"
echo "• 功能特性: 网关聚合、服务发现、负载均衡、熔断降级"
