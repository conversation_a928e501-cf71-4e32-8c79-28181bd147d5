#!/bin/bash

echo "=== 停止所有WinGame服务 ==="
echo ""

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 停止微服务
stop_microservices() {
    echo -e "${BLUE}停止微服务...${NC}"
    
    # 定义服务列表
    services=(
        "game-gateway:网关服务"
        "game-user:用户服务"
        "game-hall:大厅服务"
        "game-agentGame:代理游戏服务"
        "game-activity:活动服务"
        "game-account:账户服务"
    )
    
    stopped_count=0
    
    for service in "${services[@]}"; do
        IFS=':' read -r service_name service_desc <<< "$service"
        
        echo -e "${CYAN}停止 $service_desc ($service_name)...${NC}"
        
        # 查找并停止Java进程
        pids=$(pgrep -f "$service_name.*jar" 2>/dev/null)
        
        if [ -n "$pids" ]; then
            echo "  发现进程: $pids"
            
            # 优雅停止
            echo "  发送TERM信号..."
            echo "$pids" | xargs kill -TERM 2>/dev/null
            
            # 等待进程停止
            sleep 3
            
            # 检查是否还在运行
            remaining_pids=$(pgrep -f "$service_name.*jar" 2>/dev/null)
            
            if [ -n "$remaining_pids" ]; then
                echo "  强制停止..."
                echo "$remaining_pids" | xargs kill -KILL 2>/dev/null
                sleep 1
            fi
            
            # 最终检查
            final_pids=$(pgrep -f "$service_name.*jar" 2>/dev/null)
            
            if [ -z "$final_pids" ]; then
                echo -e "${GREEN}  ✅ $service_desc 已停止${NC}"
                ((stopped_count++))
            else
                echo -e "${RED}  ❌ $service_desc 停止失败${NC}"
            fi
        else
            echo -e "${YELLOW}  ⚠️ $service_desc 未运行${NC}"
        fi
        
        echo ""
    done
    
    echo -e "${BLUE}微服务停止完成: $stopped_count/${#services[@]}${NC}"
    echo ""
}

# 停止基础设施服务
stop_infrastructure() {
    echo -e "${BLUE}停止基础设施服务...${NC}"
    
    # 检测系统架构选择合适的docker-compose文件
    ARCH=$(uname -m)
    if [[ "$ARCH" == "arm64" ]] || [[ "$ARCH" == "aarch64" ]]; then
        COMPOSE_FILE="docker-compose-arm64.yml"
        echo -e "${CYAN}使用ARM64配置: $COMPOSE_FILE${NC}"
    else
        COMPOSE_FILE="docker-compose.yml"
        echo -e "${CYAN}使用标准配置: $COMPOSE_FILE${NC}"
    fi
    
    # 停止Docker容器
    if [ -f "$COMPOSE_FILE" ]; then
        echo -e "${CYAN}停止Docker容器...${NC}"
        
        if docker-compose -f "$COMPOSE_FILE" down; then
            echo -e "${GREEN}✅ Docker容器已停止${NC}"
        else
            echo -e "${YELLOW}⚠️ Docker容器停止可能有问题${NC}"
        fi
    else
        echo -e "${YELLOW}⚠️ Docker Compose文件不存在: $COMPOSE_FILE${NC}"
    fi
    
    # 手动停止可能遗留的容器
    echo -e "${CYAN}检查遗留容器...${NC}"
    
    containers=("nacos" "mysql" "mongodb" "redis")
    
    for container in "${containers[@]}"; do
        if docker ps -q -f name="$container" | grep -q .; then
            echo "  停止容器: $container"
            docker stop "$container" 2>/dev/null || true
        fi
    done
    
    echo ""
}

# 清理进程
cleanup_processes() {
    echo -e "${BLUE}清理相关进程...${NC}"
    
    # 查找所有可能的Java进程
    echo -e "${CYAN}查找Java进程...${NC}"
    
    java_pids=$(pgrep -f "java.*jar" 2>/dev/null)
    
    if [ -n "$java_pids" ]; then
        echo "发现Java进程:"
        echo "$java_pids" | while read pid; do
            cmd=$(ps -p "$pid" -o command= 2>/dev/null || echo "进程已退出")
            echo "  PID $pid: $cmd"
        done
        
        echo ""
        echo -n "是否停止所有Java进程? (y/N): "
        read -r confirm
        
        if [[ $confirm =~ ^[Yy]$ ]]; then
            echo "停止所有Java进程..."
            echo "$java_pids" | xargs kill -TERM 2>/dev/null
            sleep 3
            
            # 强制停止仍在运行的进程
            remaining_pids=$(pgrep -f "java.*jar" 2>/dev/null)
            if [ -n "$remaining_pids" ]; then
                echo "强制停止剩余进程..."
                echo "$remaining_pids" | xargs kill -KILL 2>/dev/null
            fi
            
            echo -e "${GREEN}✅ Java进程清理完成${NC}"
        else
            echo "跳过Java进程清理"
        fi
    else
        echo -e "${GREEN}✅ 没有发现Java进程${NC}"
    fi
    
    echo ""
}

# 清理端口占用
cleanup_ports() {
    echo -e "${BLUE}检查端口占用...${NC}"
    
    ports=(8080 8081 8082 8083 8084 8085 8848 9848 27017 6379 3306)
    
    occupied_ports=()
    
    for port in "${ports[@]}"; do
        if lsof -i :$port > /dev/null 2>&1; then
            occupied_ports+=($port)
        fi
    done
    
    if [ ${#occupied_ports[@]} -gt 0 ]; then
        echo -e "${YELLOW}发现占用的端口: ${occupied_ports[*]}${NC}"
        
        for port in "${occupied_ports[@]}"; do
            echo "端口 $port 占用情况:"
            lsof -i :$port | head -5
            echo ""
        done
        
        echo -n "是否强制释放这些端口? (y/N): "
        read -r confirm
        
        if [[ $confirm =~ ^[Yy]$ ]]; then
            for port in "${occupied_ports[@]}"; do
                echo "释放端口 $port..."
                pids=$(lsof -ti :$port 2>/dev/null)
                if [ -n "$pids" ]; then
                    echo "$pids" | xargs kill -TERM 2>/dev/null
                fi
            done
            
            sleep 2
            echo -e "${GREEN}✅ 端口清理完成${NC}"
        else
            echo "跳过端口清理"
        fi
    else
        echo -e "${GREEN}✅ 没有发现端口占用${NC}"
    fi
    
    echo ""
}

# 清理日志文件
cleanup_logs() {
    echo -e "${BLUE}清理日志文件...${NC}"
    
    if [ -d "logs" ]; then
        log_files=$(find logs -name "*.log" -type f 2>/dev/null)
        
        if [ -n "$log_files" ]; then
            echo "发现日志文件:"
            echo "$log_files" | sed 's/^/  /'
            echo ""
            
            echo -n "是否清理日志文件? (y/N): "
            read -r confirm
            
            if [[ $confirm =~ ^[Yy]$ ]]; then
                echo "$log_files" | xargs rm -f
                echo -e "${GREEN}✅ 日志文件已清理${NC}"
            else
                echo "保留日志文件"
            fi
        else
            echo -e "${GREEN}✅ 没有发现日志文件${NC}"
        fi
    else
        echo -e "${GREEN}✅ 日志目录不存在${NC}"
    fi
    
    echo ""
}

# 显示停止状态
show_stop_status() {
    echo -e "${BLUE}检查停止状态...${NC}"
    
    # 检查Java进程
    java_count=$(pgrep -f "java.*jar" 2>/dev/null | wc -l)
    echo "Java进程数量: $java_count"
    
    # 检查Docker容器
    container_count=$(docker ps -q | wc -l)
    echo "运行中的Docker容器: $container_count"
    
    # 检查端口占用
    occupied_count=0
    ports=(8080 8081 8082 8083 8084 8085 8848 9848 27017 6379 3306)
    
    for port in "${ports[@]}"; do
        if lsof -i :$port > /dev/null 2>&1; then
            ((occupied_count++))
        fi
    done
    
    echo "占用的端口数量: $occupied_count"
    
    echo ""
    
    if [ $java_count -eq 0 ] && [ $container_count -eq 0 ] && [ $occupied_count -eq 0 ]; then
        echo -e "${GREEN}🎉 所有服务已完全停止！${NC}"
    else
        echo -e "${YELLOW}⚠️ 部分服务可能仍在运行${NC}"
        
        if [ $java_count -gt 0 ]; then
            echo "  - 仍有 $java_count 个Java进程运行"
        fi
        
        if [ $container_count -gt 0 ]; then
            echo "  - 仍有 $container_count 个Docker容器运行"
        fi
        
        if [ $occupied_count -gt 0 ]; then
            echo "  - 仍有 $occupied_count 个端口被占用"
        fi
    fi
}

# 主函数
main() {
    echo -e "${CYAN}开始停止所有WinGame服务...${NC}"
    echo ""
    
    # 1. 停止微服务
    stop_microservices
    
    # 2. 停止基础设施
    stop_infrastructure
    
    # 3. 清理进程
    cleanup_processes
    
    # 4. 清理端口
    cleanup_ports
    
    # 5. 清理日志（可选）
    cleanup_logs
    
    # 6. 显示最终状态
    show_stop_status
    
    echo ""
    echo -e "${GREEN}🎉 服务停止操作完成！${NC}"
    echo ""
    echo -e "${BLUE}如需重新启动服务:${NC}"
    echo "• 启动基础设施: ./start-infrastructure.sh"
    echo "• 启动微服务: ./start-services.sh"
    echo "• 完整演示: ./complete-demo.sh"
}

# 运行主函数
main
