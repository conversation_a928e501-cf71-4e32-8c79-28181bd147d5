version: '3.8'

services:
  # Nacos
  nacos:
    image: nacos/nacos-server:v2.2.1
    container_name: nacos
    environment:
      - MODE=standalone
      - SPRING_DATASOURCE_PLATFORM=mysql
      - MYSQL_SERVICE_HOST=mysql
      - MYSQL_SERVICE_DB_NAME=nacos_devtest
      - MYSQL_SERVICE_PORT=3306
      - MYSQL_SERVICE_USER=nacos
      - MYSQL_SERVICE_PASSWORD=nacos
    ports:
      - "8848:8848"
      - "9848:9848"
    depends_on:
      - mysql
    restart: always
    networks:
      - wingame-network

  # MySQL for Nacos
  mysql:
    image: mysql:8.0
    container_name: mysql
    environment:
      - MYSQL_ROOT_PASSWORD=root
      - MYSQL_DATABASE=nacos_devtest
      - MYSQL_USER=nacos
      - MYSQL_PASSWORD=nacos
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
    restart: always
    networks:
      - wingame-network

  # MongoDB
  mongodb:
    image: mongo:6.0
    container_name: mongodb
    environment:
      - MONGO_INITDB_ROOT_USERNAME=admin
      - MONGO_INITDB_ROOT_PASSWORD=admin123
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
    restart: always
    networks:
      - wingame-network

  # Redis
  redis:
    image: redis:7.0-alpine
    container_name: redis
    command: redis-server --requirepass redis123
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: always
    networks:
      - wingame-network

volumes:
  mysql_data:
  mongodb_data:
  redis_data:

networks:
  wingame-network:
    driver: bridge
