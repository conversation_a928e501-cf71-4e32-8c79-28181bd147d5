# WinGame Spring Cloud 微服务架构

## 项目概述

这是一个基于Spring Cloud的游戏微服务架构项目，使用JDK 18、Nacos作为注册和配置中心、MongoDB作为数据库、Redis作为缓存。

## 技术栈

- **JDK**: 21
- **Spring Boot**: 2.7.14
- **Spring Cloud**: 2021.0.8
- **Spring Cloud Alibaba**: 2021.0.5.0
- **注册中心**: Nacos 2.2.1
- **配置中心**: Nacos 2.2.1
- **数据库**: MongoDB 6.0
- **缓存**: Redis 7.0
- **网关**: Spring Cloud Gateway
- **服务调用**: OpenFeign
- **负载均衡**: Spring Cloud LoadBalancer

## 项目结构

```
wingame/
├── game-gateway/         # 网关服务 (端口: 8080)
├── game-user/           # 用户服务 (端口: 8081)
├── game-hall/           # 大厅服务 (端口: 8082)
├── game-agentGame/      # 代理游戏服务 (端口: 8083)
├── game-activity/       # 活动服务 (端口: 8084)
├── game-account/        # 账户服务 (端口: 8085)
├── docker-compose.yml   # Docker编排文件
├── start-infrastructure.sh  # 基础设施启动脚本
├── start-services.sh    # 微服务启动脚本
└── README.md
```

## 快速开始

### 1. 环境要求

- JDK 21+
- Maven 3.6+
- Docker & Docker Compose

### 2. 环境检查

在开始之前，建议先运行环境检查脚本：


**重要提示**：
- 如果JDK版本不是21，请参考 `JDK_UPGRADE_GUIDE.md` 进行升级
- 如果遇到ARM64架构问题，请参考 `ARM64_COMPATIBILITY_GUIDE.md`
- 如果遇到Nacos启动问题，请参考 `NACOS_TROUBLESHOOTING.md` 或运行 `./fix-nacos-issues.sh`


### 3. 启动基础设施

```bash
# 给脚本执行权限
chmod +x start-infrastructure.sh
chmod +x start-services.sh

# 启动基础设施 (Nacos, MongoDB, Redis)
./start-infrastructure.sh
```

### 3. 启动微服务

```bash
# 编译并启动所有微服务
./start-services.sh
```

### 5. 网关路由演示

```bash
# 运行完整的网关路由演示
./complete-demo.sh

# 或者运行基础API测试
./gateway-demo.sh
```

### 6. 网关层聚合演示（Feign调用）

```bash
# 演示用户服务通过Feign调用账户服务，实现数据聚合
./gateway-aggregation-demo.sh
```

**聚合功能特点**：
- ✅ 用户服务通过Feign调用账户服务
- ✅ 网关层统一入口，服务间透明调用
- ✅ 数据聚合，减少客户端请求次数
- ✅ 降级处理，保证服务可用性
- ✅ 并发调用，提高响应性能

### 7. 访问地址

- **Nacos控制台**: http://localhost:8848/nacos (用户名/密码: nacos/nacos)
- **API网关**: http://localhost:8080
- **用户服务**: http://localhost:8081
- **大厅服务**: http://localhost:8082
- **代理游戏服务**: http://localhost:8083
- **活动服务**: http://localhost:8084
- **账户服务**: http://localhost:8085

### 8. 可视化测试

- **前端演示页面**: 在浏览器中打开 `demo-frontend.html`
- **Postman集合**: 导入 `WinGame-Gateway-API.postman_collection.json`

## 服务说明

### game-gateway (网关服务)
- 统一入口
- 路由转发
- 负载均衡
- 跨域处理

### game-user (用户服务)
- 用户注册/登录
- 用户信息管理
- 用户状态管理

### game-hall (大厅服务)
- 游戏房间管理
- 房间状态控制
- 玩家匹配

### game-agentGame (代理游戏服务)
- 游戏代理逻辑
- 第三方游戏接入
- 游戏数据同步

### game-activity (活动服务)
- 活动管理
- 奖励发放
- 活动参与记录

### game-account (账户服务)
- 账户余额管理
- 交易记录
- 资金流水

## 数据库设计

每个服务使用独立的MongoDB数据库：
- `wingame_user`: 用户服务数据库
- `wingame_hall`: 大厅服务数据库
- `wingame_agentgame`: 代理游戏服务数据库
- `wingame_activity`: 活动服务数据库
- `wingame_account`: 账户服务数据库

## Redis使用

每个服务使用不同的Redis数据库：
- Database 0: 用户服务
- Database 1: 大厅服务
- Database 2: 代理游戏服务
- Database 3: 活动服务
- Database 4: 账户服务

## 开发指南

### 添加新的微服务

1. 在根pom.xml中添加新模块
2. 创建模块目录和pom.xml
3. 添加启动类和配置文件
4. 在网关中配置路由

### 配置管理

所有配置都通过Nacos配置中心管理，支持动态刷新。

### 服务间调用

使用OpenFeign进行服务间调用，支持负载均衡和熔断。

## 部署说明

### 开发环境
使用提供的脚本快速启动本地开发环境。

### 生产环境
1. 修改各服务的application.yml配置
2. 使用Docker容器化部署
3. 配置外部MongoDB和Redis集群
4. 使用Nginx进行负载均衡

## 监控和日志

- 日志文件位于 `logs/` 目录
- 可集成Spring Boot Actuator进行健康检查
- 建议集成ELK或其他日志收集系统

## 注意事项

1. 确保JDK版本为21
2. 首次启动需要等待Nacos完全启动
3. 各服务启动有依赖顺序，建议按脚本顺序启动
4. 生产环境需要配置安全认证和SSL
