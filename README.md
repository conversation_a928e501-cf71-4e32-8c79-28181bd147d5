# WinGame Spring Cloud 微服务架构

## 项目概述

这是一个基于Spring Cloud的游戏微服务架构项目，使用JDK 18、Nacos作为注册和配置中心、MongoDB作为数据库、Redis作为缓存。

## 技术栈

- **JDK**: 21
- **Spring Boot**: 2.7.14
- **Spring Cloud**: 2021.0.8
- **Spring Cloud Alibaba**: 2021.0.5.0
- **注册中心**: Nacos 2.2.1
- **配置中心**: Nacos 2.2.1
- **数据库**: MongoDB 6.0
- **缓存**: Redis 7.0
- **网关**: Spring Cloud Gateway
- **服务调用**: OpenFeign
- **负载均衡**: Spring Cloud LoadBalancer

## 项目结构

```
wingame/
├── game-gateway/         # 网关服务 (端口: 8080)
├── game-user/           # 用户服务 (端口: 8081)
├── game-hall/           # 大厅服务 (端口: 8082)
├── game-agentGame/      # 代理游戏服务 (端口: 8083)
├── game-activity/       # 活动服务 (端口: 8084)
├── game-account/        # 账户服务 (端口: 8085)
├── docker-compose.yml   # Docker编排文件
├── start-infrastructure.sh  # 基础设施启动脚本
├── start-services.sh    # 微服务启动脚本
└── README.md
```

## 快速开始

### 1. 环境要求

- JDK 21+
- Maven 3.6+
- Docker & Docker Compose

### 2. 环境检查

在开始之前，建议先运行环境检查脚本：


### 3. 启动基础设施

```bash
# 给脚本执行权限
chmod +x start-infrastructure.sh
chmod +x start-services.sh

# 启动基础设施 (Nacos, MongoDB, Redis)
./start-infrastructure.sh
```

### 3. 启动微服务

```bash
# 编译并启动所有微服务
./start-services.sh
```

### 5. 网关路由演示

```bash
#或者运行基础API测试
./gateway-demo.sh
```

### 6. 网关层聚合演示（Feign调用）

```bash
# 演示用户服务通过Feign调用账户服务，实现数据聚合
./gateway-aggregation-demo.sh
```

**聚合功能特点**：
- ✅ 用户服务通过Feign调用账户服务
- ✅ 网关层统一入口，服务间透明调用
- ✅ 数据聚合，减少客户端请求次数
- ✅ 降级处理，保证服务可用性
- ✅ 并发调用，提高响应性能

### 7. 访问地址

- **Nacos控制台**: http://localhost:8848/nacos (用户名/密码: nacos/nacos)
- **API网关**: http://localhost:8080
- **用户服务**: http://localhost:8081
- **大厅服务**: http://localhost:8082
- **代理游戏服务**: http://localhost:8083
- **活动服务**: http://localhost:8084
- **账户服务**: http://localhost:8085

### 8. 可视化测试

- **前端演示页面**: 在浏览器中打开 `demo-frontend.html`
- **Postman集合**: 导入 `WinGame-Gateway-API.postman_collection.json`

## 服务说明

### game-gateway (网关服务)
- 统一入口
- 路由转发
- 负载均衡
- 跨域处理

### game-user (用户服务)
- 用户注册/登录
- 用户信息管理
- 用户状态管理

### game-hall (大厅服务)
- 游戏房间管理
- 房间状态控制
- 玩家匹配

### game-agentGame (代理游戏服务)
- 游戏代理逻辑
- 第三方游戏接入
- 游戏数据同步

### game-activity (活动服务)
- 活动管理
- 奖励发放
- 活动参与记录

### game-account (账户服务)
- 账户余额管理
- 交易记录
- 资金流水

## 数据库设计

每个服务使用独立的MongoDB数据库：
- `wingame_user`: 用户服务数据库
- `wingame_hall`: 大厅服务数据库
- `wingame_agentgame`: 代理游戏服务数据库
- `wingame_activity`: 活动服务数据库
- `wingame_account`: 账户服务数据库

## Redis使用

每个服务使用不同的Redis数据库：
- Database 0: 用户服务
- Database 1: 大厅服务
- Database 2: 代理游戏服务
- Database 3: 活动服务
- Database 4: 账户服务

## 开发指南

### 添加新的微服务

1. 在根pom.xml中添加新模块
2. 创建模块目录和pom.xml
3. 添加启动类和配置文件
4. 在网关中配置路由

### 配置管理

所有配置都通过Nacos配置中心管理，支持动态刷新。

### 服务间调用

使用OpenFeign进行服务间调用，支持负载均衡和熔断。

## 部署说明

### 开发环境
使用提供的脚本快速启动本地开发环境。

### 生产环境
1. 修改各服务的application.yml配置
2. 使用Docker容器化部署
3. 配置外部MongoDB和Redis集群
4. 使用Nginx进行负载均衡

## 监控和日志

- 日志文件位于 `logs/` 目录
- 可集成Spring Boot Actuator进行健康检查
- 建议集成ELK或其他日志收集系统

## 注意事项

1. 确保JDK版本为21
2. 首次启动需要等待Nacos完全启动
3. 各服务启动有依赖顺序，建议按脚本顺序启动
4. 生产环境需要配置安全认证和SSL

# 网关层聚合功能指南

## 🎯 功能概述

本项目演示了在微服务架构中，如何在网关层实现数据聚合，通过Feign客户端进行服务间调用，将用户信息和账户余额等数据聚合返回，减少客户端的请求次数，提升用户体验。

## 🏗️ 架构设计

```
客户端 -> 网关 -> 用户服务 -> (Feign) -> 账户服务
                    ↓
                聚合数据返回
```

### 调用链路
1. **客户端** 发起请求到网关
2. **网关** 路由请求到用户服务
3. **用户服务** 通过Feign调用账户服务
4. **账户服务** 返回账户数据
5. **用户服务** 聚合用户信息和账户信息
6. **网关** 返回聚合后的完整数据给客户端

## 📋 核心组件

### 1. Feign客户端
```java
@FeignClient(name = "game-account", fallback = AccountServiceClientFallback.class)
public interface AccountServiceClient {
    @GetMapping("/api/account/{userId}/balance")
    Result<Object> getBalance(@PathVariable("userId") String userId);
}
```

### 2. 降级处理
```java
@Component
public class AccountServiceClientFallback implements AccountServiceClient {
    @Override
    public Result<Object> getBalance(String userId) {
        // 返回默认数据，保证服务可用性
        return Result.success("账户服务暂时不可用，返回默认数据", defaultBalance);
    }
}
```

### 3. 聚合服务
```java
@Service
public class UserAggregationServiceImpl implements UserAggregationService {
    // 并行调用多个服务，聚合数据
    CompletableFuture<User> userFuture = CompletableFuture.supplyAsync(...);
    CompletableFuture<Result<Object>> balanceFuture = CompletableFuture.supplyAsync(...);
}
```

## 🚀 API接口

### 1. 获取用户完整档案
```bash
GET /user/api/user/aggregation/profile/{userId}
```
**功能**: 聚合用户基本信息、账户余额、统计信息

**响应示例**:
```json
{
  "code": 200,
  "message": "获取用户档案成功",
  "data": {
    "userId": "demo_user_123",
    "username": "testuser",
    "nickname": "测试用户",
    "email": "<EMAIL>",
    "balanceInfo": {
      "totalBalance": 1000.00,
      "availableBalance": 800.00,
      "frozenBalance": 200.00,
      "status": "ACTIVE"
    },
    "accountStatistics": {
      "totalRecharge": 5000.00,
      "totalWithdraw": 2000.00,
      "netProfit": 500.00,
      "gameCount": 25
    },
    "dataSource": {
      "userServiceAvailable": true,
      "accountServiceAvailable": true,
      "aggregationTime": "2024-01-15T10:30:00"
    }
  }
}
```

### 2. 获取用户信息和余额
```bash
GET /user/api/user/aggregation/profile/{userId}/balance
```
**功能**: 只聚合用户信息和账户余额

### 3. 获取用户信息和统计
```bash
GET /user/api/user/aggregation/profile/{userId}/statistics
```
**功能**: 只聚合用户信息和账户统计

### 4. 批量获取用户档案
```bash
GET /user/api/user/aggregation/profiles?userIds=user1,user2,user3
```
**功能**: 批量获取多个用户的档案信息

### 5. 聚合服务健康检查
```bash
GET /user/api/user/aggregation/health
```
**功能**: 检查聚合服务和依赖服务的健康状态

## 🔧 技术特性

### 1. 异步并发调用
```java
// 并行调用多个服务，提高性能
CompletableFuture<User> userFuture = CompletableFuture.supplyAsync(() -> {
    return userService.findById(userId);
});

CompletableFuture<Result<Object>> balanceFuture = CompletableFuture.supplyAsync(() -> {
    return accountServiceClient.getBalance(userId);
});

// 等待所有调用完成
CompletableFuture.allOf(userFuture, balanceFuture).get(5, TimeUnit.SECONDS);
```

### 2. 熔断降级
- **Feign降级**: 当账户服务不可用时，返回默认数据
- **超时控制**: 设置合理的超时时间，避免长时间等待
- **异常处理**: 优雅处理各种异常情况

### 3. 数据聚合策略
- **完整聚合**: 获取所有相关数据
- **部分聚合**: 根据需要只获取特定数据
- **批量聚合**: 支持批量操作，提高效率

### 4. 性能优化
- **并行调用**: 同时调用多个服务
- **缓存策略**: 可以添加Redis缓存
- **连接池**: Feign客户端连接池优化

## 📊 性能对比

### 传统方式 vs 聚合方式

| 方式 | 请求次数 | 网络开销 | 响应时间 | 客户端复杂度 |
|------|----------|----------|----------|--------------|
| **传统方式** | 3次 | 高 | 300ms+ | 高 |
| **聚合方式** | 1次 | 低 | 150ms | 低 |

### 聚合方式优势
- ✅ **减少网络请求**: 3次请求合并为1次
- ✅ **降低延迟**: 并行调用，总时间取决于最慢的服务
- ✅ **简化客户端**: 客户端只需调用一个接口
- ✅ **提高可用性**: 降级处理保证服务可用

## 🧪 测试演示

### 1. 运行完整演示
```bash
./gateway-aggregation-demo.sh
```

### 2. 手动测试
```bash
# 1. 获取用户完整档案
curl "http://localhost:8080/user/api/user/aggregation/profile/demo_user_123"

# 2. 获取用户和余额
curl "http://localhost:8080/user/api/user/aggregation/profile/demo_user_123/balance"

# 3. 批量获取
curl "http://localhost:8080/user/api/user/aggregation/profiles?userIds=user1,user2,user3"

# 4. 健康检查
curl "http://localhost:8080/user/api/user/aggregation/health"
```

### 3. 性能测试
```bash
# 并发测试
for i in {1..10}; do
  curl "http://localhost:8080/user/api/user/aggregation/profile/user_$i" &
done
wait
```

## 🔍 监控和调试

### 1. 日志监控
```bash
# 查看用户服务日志
tail -f logs/user.log | grep -i "aggregation\|feign"

# 查看账户服务日志
tail -f logs/account.log | grep -i "balance\|statistics"
```

### 2. 性能监控
- **响应时间**: 监控聚合接口的响应时间
- **成功率**: 监控Feign调用的成功率
- **降级率**: 监控降级处理的触发频率

### 3. 错误处理
- **超时处理**: 设置合理的超时时间
- **重试机制**: 配置Feign的重试策略
- **熔断器**: 使用Hystrix或Resilience4j

## 🛠️ 配置优化

### 1. Feign配置
```yaml
feign:
  client:
    config:
      default:
        connectTimeout: 5000
        readTimeout: 10000
        loggerLevel: basic
  hystrix:
    enabled: true
```

### 2. 线程池配置
```yaml
spring:
  task:
    execution:
      pool:
        core-size: 10
        max-size: 20
        queue-capacity: 100
```

### 3. 缓存配置
```yaml
spring:
  cache:
    type: redis
    redis:
      time-to-live: 300000  # 5分钟
```

## 🎯 最佳实践

### 1. 设计原则
- **单一职责**: 每个聚合接口有明确的职责
- **幂等性**: 确保接口的幂等性
- **向后兼容**: 保持API的向后兼容性

### 2. 错误处理
- **优雅降级**: 部分服务失败时，返回可用数据
- **明确错误信息**: 提供清晰的错误描述
- **状态标识**: 标识数据来源和状态

### 3. 性能优化
- **合理超时**: 设置合适的超时时间
- **并行调用**: 尽可能并行调用服务
- **缓存策略**: 对稳定数据进行缓存

## 🔗 相关文档

- [Spring Cloud OpenFeign官方文档](https://docs.spring.io/spring-cloud-openfeign/docs/current/reference/html/)
- [Spring Cloud Gateway官方文档](https://docs.spring.io/spring-cloud-gateway/docs/current/reference/html/)
- [微服务聚合模式](https://microservices.io/patterns/data/api-composition.html)

---

**总结**: 网关层聚合是微服务架构中的重要模式，通过合理的设计和实现，可以显著提升系统性能和用户体验。




