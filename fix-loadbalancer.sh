#!/bin/bash

echo "=== LoadBalancer配置修复脚本 ==="
echo ""

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 检查LoadBalancer依赖
check_loadbalancer_dependencies() {
    echo -e "${BLUE}检查LoadBalancer依赖...${NC}"
    echo ""
    
    services=("game-gateway" "game-user" "game-hall" "game-agentGame" "game-activity" "game-account")
    
    missing_services=()
    
    for service in "${services[@]}"; do
        pom_file="$service/pom.xml"
        
        if [ -f "$pom_file" ]; then
            echo -e "${CYAN}检查 $service...${NC}"
            
            # 检查是否有OpenFeign依赖
            if grep -q "spring-cloud-starter-openfeign" "$pom_file"; then
                echo "  ✅ 包含OpenFeign依赖"
                
                # 检查是否有LoadBalancer依赖
                if grep -q "spring-cloud-starter-loadbalancer" "$pom_file"; then
                    echo -e "${GREEN}  ✅ 包含LoadBalancer依赖${NC}"
                else
                    echo -e "${RED}  ❌ 缺少LoadBalancer依赖${NC}"
                    missing_services+=("$service")
                fi
            else
                echo -e "${YELLOW}  ⚠️ 不包含OpenFeign依赖${NC}"
            fi
        else
            echo -e "${RED}❌ $service/pom.xml 不存在${NC}"
        fi
        
        echo ""
    done
    
    if [ ${#missing_services[@]} -gt 0 ]; then
        echo -e "${RED}缺少LoadBalancer依赖的服务: ${missing_services[*]}${NC}"
        return 1
    else
        echo -e "${GREEN}✅ 所有服务都包含LoadBalancer依赖${NC}"
        return 0
    fi
}

# 自动添加LoadBalancer依赖
add_loadbalancer_dependencies() {
    echo -e "${BLUE}自动添加LoadBalancer依赖...${NC}"
    echo ""
    
    services=("game-gateway" "game-user" "game-hall" "game-agentGame" "game-activity" "game-account")
    
    added_count=0
    
    for service in "${services[@]}"; do
        pom_file="$service/pom.xml"
        
        if [ -f "$pom_file" ]; then
            # 检查是否有OpenFeign但没有LoadBalancer
            if grep -q "spring-cloud-starter-openfeign" "$pom_file" && ! grep -q "spring-cloud-starter-loadbalancer" "$pom_file"; then
                echo -e "${CYAN}为 $service 添加LoadBalancer依赖...${NC}"
                
                # 在OpenFeign依赖后添加LoadBalancer依赖
                sed -i.bak '/spring-cloud-starter-openfeign/,/<\/dependency>/a\
        \
        <!-- LoadBalancer -->\
        <dependency>\
            <groupId>org.springframework.cloud</groupId>\
            <artifactId>spring-cloud-starter-loadbalancer</artifactId>\
        </dependency>' "$pom_file"
                
                echo -e "${GREEN}  ✅ $service LoadBalancer依赖添加成功${NC}"
                ((added_count++))
            fi
        fi
    done
    
    echo ""
    echo "添加LoadBalancer依赖的服务数量: $added_count"
}

# 检查LoadBalancer配置
check_loadbalancer_config() {
    echo -e "${BLUE}检查LoadBalancer配置...${NC}"
    echo ""
    
    services=("game-gateway" "game-user" "game-hall" "game-agentGame" "game-activity" "game-account")
    
    for service in "${services[@]}"; do
        config_file="$service/src/main/resources/application.yml"
        
        if [ -f "$config_file" ]; then
            echo -e "${CYAN}检查 $service 配置...${NC}"
            
            # 检查是否有LoadBalancer配置
            if grep -q "loadbalancer:" "$config_file"; then
                echo "  ✅ 包含LoadBalancer配置"
            else
                echo -e "${YELLOW}  ⚠️ 没有LoadBalancer配置（使用默认配置）${NC}"
            fi
            
            # 检查Ribbon配置（已废弃）
            if grep -q "ribbon:" "$config_file"; then
                echo -e "${RED}  ❌ 发现废弃的Ribbon配置${NC}"
            fi
        fi
        
        echo ""
    done
}

# 创建LoadBalancer配置示例
create_loadbalancer_config_example() {
    echo -e "${BLUE}创建LoadBalancer配置示例...${NC}"
    
    config_file="loadbalancer-config-example.yml"
    
    cat > "$config_file" << 'EOF'
# LoadBalancer配置示例
spring:
  cloud:
    loadbalancer:
      # 负载均衡策略
      ribbon:
        enabled: false  # 禁用Ribbon
      # 健康检查
      health-check:
        initial-delay: 0
        interval: 10s
      # 缓存配置
      cache:
        enabled: true
        ttl: 35s
        capacity: 256
      # 重试配置
      retry:
        enabled: true
        max-retries-on-same-service-instance: 1
        max-retries-on-next-service-instance: 1
        retry-on-all-operations: false

# Feign配置
feign:
  client:
    config:
      default:
        connectTimeout: 5000
        readTimeout: 10000
        loggerLevel: basic
  # 启用LoadBalancer
  loadbalancer:
    enabled: true
  # 压缩配置
  compression:
    request:
      enabled: true
      mime-types: text/xml,application/xml,application/json
      min-request-size: 2048
    response:
      enabled: true

# 日志配置
logging:
  level:
    org.springframework.cloud.loadbalancer: DEBUG
    org.springframework.cloud.openfeign: DEBUG
EOF
    
    echo -e "${GREEN}✅ LoadBalancer配置示例已创建: $config_file${NC}"
}

# 测试LoadBalancer功能
test_loadbalancer() {
    echo -e "${BLUE}测试LoadBalancer功能...${NC}"
    echo ""
    
    echo -e "${CYAN}编译项目...${NC}"
    if mvn clean compile -q; then
        echo -e "${GREEN}✅ 编译成功${NC}"
    else
        echo -e "${RED}❌ 编译失败${NC}"
        return 1
    fi
    
    echo ""
    echo -e "${CYAN}检查LoadBalancer类是否可用...${NC}"
    
    # 检查LoadBalancer相关类
    loadbalancer_classes=(
        "org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier"
        "org.springframework.cloud.loadbalancer.core.ReactorLoadBalancer"
        "org.springframework.cloud.loadbalancer.support.LoadBalancerClientFactory"
    )
    
    for class_name in "${loadbalancer_classes[@]}"; do
        if find . -name "*.jar" -exec jar -tf {} \; 2>/dev/null | grep -q "${class_name//./\/}.class"; then
            echo -e "${GREEN}  ✅ $class_name 可用${NC}"
        else
            echo -e "${YELLOW}  ⚠️ $class_name 可能不可用${NC}"
        fi
    done
    
    return 0
}

# 生成LoadBalancer检查报告
generate_loadbalancer_report() {
    echo -e "${BLUE}生成LoadBalancer检查报告...${NC}"
    
    report_file="loadbalancer_check_report.txt"
    
    cat > "$report_file" << EOF
# LoadBalancer检查报告
生成时间: $(date)

## 依赖检查
EOF
    
    services=("game-gateway" "game-user" "game-hall" "game-agentGame" "game-activity" "game-account")
    
    for service in "${services[@]}"; do
        pom_file="$service/pom.xml"
        
        echo "" >> "$report_file"
        echo "### $service 服务" >> "$report_file"
        
        if [ -f "$pom_file" ]; then
            if grep -q "spring-cloud-starter-openfeign" "$pom_file"; then
                echo "- OpenFeign: ✅" >> "$report_file"
            else
                echo "- OpenFeign: ❌" >> "$report_file"
            fi
            
            if grep -q "spring-cloud-starter-loadbalancer" "$pom_file"; then
                echo "- LoadBalancer: ✅" >> "$report_file"
            else
                echo "- LoadBalancer: ❌" >> "$report_file"
            fi
        else
            echo "- pom.xml: 不存在" >> "$report_file"
        fi
    done
    
    echo "" >> "$report_file"
    echo "## 建议" >> "$report_file"
    echo "1. 所有使用Feign的服务都应该包含LoadBalancer依赖" >> "$report_file"
    echo "2. 移除废弃的Ribbon配置" >> "$report_file"
    echo "3. 配置适当的LoadBalancer策略" >> "$report_file"
    echo "4. 启用健康检查和重试机制" >> "$report_file"
    
    echo -e "${GREEN}✅ 报告已生成: $report_file${NC}"
}

# 主函数
main() {
    echo -e "${CYAN}开始LoadBalancer配置检查和修复...${NC}"
    echo ""
    
    # 1. 检查LoadBalancer依赖
    if check_loadbalancer_dependencies; then
        echo -e "${GREEN}✅ LoadBalancer依赖检查通过${NC}"
    else
        echo -e "${YELLOW}⚠️ 发现缺少LoadBalancer依赖的服务${NC}"
        echo ""
        echo -n "是否自动添加LoadBalancer依赖? (y/N): "
        read -r confirm
        
        if [[ $confirm =~ ^[Yy]$ ]]; then
            add_loadbalancer_dependencies
        fi
    fi
    
    echo ""
    
    # 2. 检查LoadBalancer配置
    check_loadbalancer_config
    
    # 3. 创建配置示例
    create_loadbalancer_config_example
    
    # 4. 测试LoadBalancer功能
    echo ""
    if test_loadbalancer; then
        echo -e "${GREEN}✅ LoadBalancer功能测试通过${NC}"
    else
        echo -e "${RED}❌ LoadBalancer功能测试失败${NC}"
    fi
    
    # 5. 生成报告
    echo ""
    generate_loadbalancer_report
    
    echo ""
    echo -e "${GREEN}🎉 LoadBalancer配置检查完成！${NC}"
    echo ""
    echo -e "${BLUE}关键要点:${NC}"
    echo "• Spring Cloud 2020+ 使用LoadBalancer替代Ribbon"
    echo "• 所有使用Feign的服务都需要LoadBalancer依赖"
    echo "• LoadBalancer提供更好的性能和可扩展性"
    echo "• 支持响应式编程和非阻塞I/O"
    echo ""
    echo -e "${BLUE}下一步:${NC}"
    echo "• 重新编译项目: mvn clean package -DskipTests"
    echo "• 重启服务: ./start-services.sh"
    echo "• 测试Feign调用: ./gateway-aggregation-demo.sh"
}

# 运行主函数
main
