server:
  port: 8084

spring:
  application:
    name: game-activity
  profiles:
    active: dev
  config:
    import:
      - optional:nacos:game-activity.yml
  cloud:
    nacos:
      discovery:
        server-addr: localhost:8848
        namespace: public
      config:
        server-addr: localhost:8848
        namespace: public
        file-extension: yml
        group: DEFAULT_GROUP
  data:
    mongodb:
      host: localhost
      port: 27017
      database: wingame_activity
      username: 
      password: 
    redis:
      host: localhost
      port: 6379
      password: 
      database: 3
      timeout: 3000ms
      lettuce:
        pool:
          max-active: 8
          max-wait: -1ms
          max-idle: 8
          min-idle: 0

logging:
  level:
    com.wingame.activity: debug
    org.springframework.data.mongodb: debug

# Swagger/OpenAPI配置
springdoc:
  api-docs:
    path: /v3/api-docs
  swagger-ui:
    path: /swagger-ui.html
    enabled: true
  show-actuator: true
