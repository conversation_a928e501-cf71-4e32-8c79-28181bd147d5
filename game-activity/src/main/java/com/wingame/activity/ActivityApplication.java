package com.wingame.activity;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;

/**
 * 活动服务启动类
 */
@SpringBootApplication(scanBasePackages = {"com.wingame.activity", "com.wingame.common"})
@EnableDiscoveryClient
@EnableFeignClients
public class ActivityApplication {
    
    public static void main(String[] args) {
        SpringApplication.run(ActivityApplication.class, args);
    }
}
