package com.wingame.activity.controller;

import com.wingame.common.result.Result;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * 活动控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/activity")
@RequiredArgsConstructor
public class ActivityController {
    
    /**
     * 获取所有活动
     */
    @GetMapping("/list")
    public Result<List<Object>> getAllActivities(@RequestParam(defaultValue = "ALL") String status) {
        log.info("获取活动列表, 状态: {}", status);
        
        List<Object> activities = new ArrayList<>();
        
        activities.add(new Object() {
            public final String id = UUID.randomUUID().toString();
            public final String title = "新用户注册送豪礼";
            public final String description = "新用户注册即可获得1000金币和3张体验券";
            public final String type = "REGISTER_BONUS";
            public final String status = "ACTIVE";
            public final LocalDateTime startTime = LocalDateTime.now().minusDays(7);
            public final LocalDateTime endTime = LocalDateTime.now().plusDays(23);
            public final int maxParticipants = 10000;
            public final int currentParticipants = 3456;
            public final List<Object> rewards = List.of(
                new Object() {
                    public final String type = "COINS";
                    public final int amount = 1000;
                },
                new Object() {
                    public final String type = "TICKETS";
                    public final int amount = 3;
                }
            );
        });
        
        activities.add(new Object() {
            public final String id = UUID.randomUUID().toString();
            public final String title = "每日签到领奖励";
            public final String description = "连续签到7天可获得超级大奖";
            public final String type = "DAILY_CHECKIN";
            public final String status = "ACTIVE";
            public final LocalDateTime startTime = LocalDateTime.now().minusDays(30);
            public final LocalDateTime endTime = LocalDateTime.now().plusDays(60);
            public final int maxParticipants = -1; // 无限制
            public final int currentParticipants = 8923;
            public final List<Object> rewards = List.of(
                new Object() {
                    public final String type = "COINS";
                    public final int amount = 100;
                },
                new Object() {
                    public final String type = "EXPERIENCE";
                    public final int amount = 50;
                }
            );
        });
        
        activities.add(new Object() {
            public final String id = UUID.randomUUID().toString();
            public final String title = "周末充值双倍返利";
            public final String description = "周末充值享受双倍返利，最高返利10000金币";
            public final String type = "RECHARGE_BONUS";
            public final String status = "UPCOMING";
            public final LocalDateTime startTime = LocalDateTime.now().plusDays(2);
            public final LocalDateTime endTime = LocalDateTime.now().plusDays(4);
            public final int maxParticipants = 5000;
            public final int currentParticipants = 0;
            public final List<Object> rewards = List.of(
                new Object() {
                    public final String type = "COINS";
                    public final String description = "充值金额的100%返利";
                }
            );
        });
        
        return Result.success("获取活动列表成功", activities);
    }

    /**
     * 健康检查
     */
    @GetMapping("/health")
    public Result<String> health() {
        return Result.success("活动服务运行正常");
    }
}
