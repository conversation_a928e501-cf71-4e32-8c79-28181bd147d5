#!/bin/bash

echo "=== WinGame 微服务网关完整演示 ==="
echo ""

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

GATEWAY_URL="http://localhost:8080"

# 检查服务是否启动
check_service() {
    local service_name=$1
    local url=$2
    
    echo -e "${BLUE}检查 $service_name 服务状态...${NC}"
    
    if curl -s "$url" > /dev/null 2>&1; then
        echo -e "${GREEN}✅ $service_name 服务运行正常${NC}"
        return 0
    else
        echo -e "${RED}❌ $service_name 服务未启动或无法访问${NC}"
        return 1
    fi
}

# 执行API测试
test_api() {
    local method=$1
    local url=$2
    local data=$3
    local description=$4
    
    echo -e "${CYAN}🚀 测试: $description${NC}"
    echo -e "${YELLOW}   请求: $method $url${NC}"
    
    if [ -n "$data" ]; then
        response=$(curl -s -X $method "$url" \
            -H "Content-Type: application/json" \
            -d "$data" \
            -w "\nHTTP_CODE:%{http_code}")
    else
        response=$(curl -s -X $method "$url" \
            -w "\nHTTP_CODE:%{http_code}")
    fi
    
    http_code=$(echo "$response" | grep "HTTP_CODE:" | cut -d: -f2)
    body=$(echo "$response" | sed '/HTTP_CODE:/d')
    
    if [ "$http_code" = "200" ]; then
        echo -e "${GREEN}   ✅ 成功 (HTTP $http_code)${NC}"
        echo "$body" | python3 -m json.tool 2>/dev/null | head -10 || echo "$body" | head -5
    else
        echo -e "${RED}   ❌ 失败 (HTTP $http_code)${NC}"
        echo "$body" | head -3
    fi
    echo ""
}

echo -e "${PURPLE}第一步: 检查所有服务状态${NC}"
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"

services=(
    "网关服务:$GATEWAY_URL"
    "用户服务:http://localhost:8081"
    "大厅服务:http://localhost:8082"
    "代理游戏服务:http://localhost:8083"
    "活动服务:http://localhost:8084"
    "账户服务:http://localhost:8085"
)

all_services_up=true
for service in "${services[@]}"; do
    IFS=':' read -r name url <<< "$service"
    if ! check_service "$name" "$url"; then
        all_services_up=false
    fi
done

echo ""

if [ "$all_services_up" = false ]; then
    echo -e "${RED}⚠️  部分服务未启动，请先运行以下命令启动服务:${NC}"
    echo -e "${YELLOW}   ./start-infrastructure.sh${NC}"
    echo -e "${YELLOW}   ./start-services.sh${NC}"
    echo ""
    echo -e "${BLUE}继续演示网关路由功能...${NC}"
    echo ""
fi

echo -e "${PURPLE}第二步: 网关路由演示${NC}"
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"

echo -e "${BLUE}=== 1. 用户服务路由测试 ===${NC}"

# 用户注册
test_api "POST" "$GATEWAY_URL/user/api/user/register" '{
    "username": "demo_user_'$(date +%s)'",
    "password": "123456",
    "confirmPassword": "123456",
    "nickname": "演示用户",
    "email": "<EMAIL>",
    "phone": "13800138000"
}' "通过网关注册用户"

# 检查用户名
test_api "GET" "$GATEWAY_URL/user/api/user/check/username/demo_user_123" "" "通过网关检查用户名"

echo -e "${BLUE}=== 2. 大厅服务路由测试 ===${NC}"

# 获取房间列表
test_api "GET" "$GATEWAY_URL/hall/api/hall/rooms" "" "通过网关获取游戏房间"

# 获取大厅统计
test_api "GET" "$GATEWAY_URL/hall/api/hall/stats" "" "通过网关获取大厅统计"

echo -e "${BLUE}=== 3. 代理游戏服务路由测试 ===${NC}"

# 获取游戏列表
test_api "GET" "$GATEWAY_URL/agentgame/api/agentgame/games" "" "通过网关获取游戏列表"

# 启动游戏
test_api "POST" "$GATEWAY_URL/agentgame/api/agentgame/games/texas_holdem/launch?userId=demo_user_123" "" "通过网关启动游戏"

echo -e "${BLUE}=== 4. 活动服务路由测试 ===${NC}"

# 获取活动列表
test_api "GET" "$GATEWAY_URL/activity/api/activity/list" "" "通过网关获取活动列表"

# 每日签到
test_api "POST" "$GATEWAY_URL/activity/api/activity/checkin?userId=demo_user_123" "" "通过网关每日签到"

echo -e "${BLUE}=== 5. 账户服务路由测试 ===${NC}"

# 获取账户余额
test_api "GET" "$GATEWAY_URL/account/api/account/demo_user_123/balance" "" "通过网关获取账户余额"

# 账户充值
test_api "POST" "$GATEWAY_URL/account/api/account/demo_user_123/recharge?amount=1000&paymentMethod=alipay" "" "通过网关账户充值"

echo -e "${PURPLE}第三步: 网关功能特性演示${NC}"
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"

echo -e "${BLUE}=== 负载均衡演示 ===${NC}"
echo "连续请求同一个服务，观察负载均衡效果:"
for i in {1..3}; do
    echo -e "${CYAN}第 $i 次请求:${NC}"
    test_api "GET" "$GATEWAY_URL/user/api/user/health" "" "健康检查 #$i"
    sleep 1
done

echo -e "${BLUE}=== 路由规则演示 ===${NC}"
echo -e "${YELLOW}当前网关路由配置:${NC}"
echo "  /user/**     -> game-user 服务 (端口 8081)"
echo "  /hall/**     -> game-hall 服务 (端口 8082)"
echo "  /agentgame/** -> game-agentgame 服务 (端口 8083)"
echo "  /activity/** -> game-activity 服务 (端口 8084)"
echo "  /account/**  -> game-account 服务 (端口 8085)"
echo ""

echo -e "${BLUE}=== 错误处理演示 ===${NC}"
test_api "GET" "$GATEWAY_URL/nonexistent/api/test" "" "访问不存在的路由"

echo -e "${PURPLE}第四步: 性能测试${NC}"
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"

echo -e "${BLUE}=== 并发请求测试 ===${NC}"
echo "发送10个并发请求测试网关性能..."

# 创建临时文件存储结果
temp_file="/tmp/gateway_test_$$"

# 并发请求
for i in {1..10}; do
    (
        start_time=$(date +%s%N)
        curl -s "$GATEWAY_URL/user/api/user/health" > /dev/null
        end_time=$(date +%s%N)
        duration=$(( (end_time - start_time) / 1000000 ))
        echo "请求 $i: ${duration}ms" >> "$temp_file"
    ) &
done

# 等待所有请求完成
wait

# 显示结果
if [ -f "$temp_file" ]; then
    echo -e "${GREEN}并发请求结果:${NC}"
    cat "$temp_file"
    rm -f "$temp_file"
fi

echo ""
echo -e "${PURPLE}演示总结${NC}"
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"

echo -e "${GREEN}✅ 网关路由功能演示完成${NC}"
echo ""
echo -e "${YELLOW}📋 演示内容总结:${NC}"
echo "1. ✅ 服务状态检查 - 验证所有微服务是否正常运行"
echo "2. ✅ 路由转发测试 - 验证网关正确路由到各个微服务"
echo "3. ✅ 负载均衡演示 - 展示网关的负载均衡能力"
echo "4. ✅ 错误处理测试 - 验证网关的错误处理机制"
echo "5. ✅ 性能测试 - 测试网关的并发处理能力"
echo ""
echo -e "${YELLOW}🔗 网关优势:${NC}"
echo "• 统一入口 - 所有请求通过网关统一处理"
echo "• 路由转发 - 自动将请求转发到对应的微服务"
echo "• 负载均衡 - 支持多实例负载均衡"
echo "• 服务发现 - 与Nacos集成，自动发现服务"
echo "• 跨域处理 - 统一处理跨域请求"
echo "• 监控统计 - 可以统一监控所有API调用"
echo ""
echo -e "${YELLOW}🚀 下一步建议:${NC}"
echo "1. 在浏览器中打开 demo-frontend.html 进行可视化测试"
echo "2. 导入 WinGame-Gateway-API.postman_collection.json 到Postman"
echo "3. 查看 Nacos 控制台: http://localhost:8848/nacos"
echo "4. 添加认证、限流等网关高级功能"
echo ""
echo -e "${CYAN}🎉 WinGame 微服务网关演示结束！${NC}"
