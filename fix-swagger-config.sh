#!/bin/bash

echo "=== Swagger配置修复脚本 ==="
echo ""

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 为所有服务添加Swagger依赖
add_swagger_dependencies() {
    echo -e "${BLUE}为所有服务添加Swagger依赖...${NC}"
    
    services=("game-hall" "game-agentGame" "game-activity" "game-account")
    
    for service in "${services[@]}"; do
        pom_file="$service/pom.xml"
        
        if [ -f "$pom_file" ]; then
            # 检查是否已经有Swagger依赖
            if grep -q "springdoc-openapi" "$pom_file"; then
                echo -e "${GREEN}✅ $service 已有Swagger依赖${NC}"
            else
                echo -e "${YELLOW}添加Swagger依赖到 $service...${NC}"
                
                # 在</dependencies>前添加Swagger依赖
                sed -i.bak '/<\/dependencies>/i\
        \
        <!-- Swagger 3 (OpenAPI 3) -->\
        <dependency>\
            <groupId>org.springdoc</groupId>\
            <artifactId>springdoc-openapi-ui</artifactId>\
            <version>1.7.0</version>\
        </dependency>' "$pom_file"
                
                echo -e "${GREEN}✅ $service Swagger依赖添加完成${NC}"
            fi
        else
            echo -e "${RED}❌ $service/pom.xml 不存在${NC}"
        fi
    done
}

# 创建Swagger配置类
create_swagger_configs() {
    echo -e "${BLUE}创建Swagger配置类...${NC}"
    
    services=("game-hall" "game-agentGame" "game-activity" "game-account")
    
    for service in "${services[@]}"; do
        config_dir="$service/src/main/java/com/wingame/${service#game-}/config"
        config_file="$config_dir/SwaggerConfig.java"
        
        # 创建配置目录
        mkdir -p "$config_dir"
        
        if [ -f "$config_file" ]; then
            echo -e "${GREEN}✅ $service Swagger配置已存在${NC}"
        else
            echo -e "${YELLOW}创建 $service Swagger配置...${NC}"
            
            # 获取服务名称和端口
            case $service in
                "game-hall")
                    service_name="大厅服务"
                    port="8082"
                    package_name="hall"
                    ;;
                "game-agentGame")
                    service_name="代理游戏服务"
                    port="8083"
                    package_name="agentgame"
                    ;;
                "game-activity")
                    service_name="活动服务"
                    port="8084"
                    package_name="activity"
                    ;;
                "game-account")
                    service_name="账户服务"
                    port="8085"
                    package_name="account"
                    ;;
            esac
            
            # 创建配置文件
            cat > "$config_file" << EOF
package com.wingame.$package_name.config;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import io.swagger.v3.oas.models.servers.Server;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * Swagger配置类
 */
@Configuration
public class SwaggerConfig {

    @Bean
    public OpenAPI customOpenAPI() {
        return new OpenAPI()
                .info(new Info()
                        .title("WinGame $service_name API")
                        .description("$service_name API文档")
                        .version("1.0.0")
                        .contact(new Contact()
                                .name("WinGame Team")
                                .email("<EMAIL>")
                                .url("https://wingame.com"))
                        .license(new License()
                                .name("MIT License")
                                .url("https://opensource.org/licenses/MIT")))
                .servers(List.of(
                        new Server()
                                .url("http://localhost:$port")
                                .description("本地开发环境"),
                        new Server()
                                .url("http://localhost:8080/$package_name")
                                .description("通过网关访问")
                ));
    }
}
EOF
            
            echo -e "${GREEN}✅ $service Swagger配置创建完成${NC}"
        fi
    done
}

# 更新application.yml配置
update_application_configs() {
    echo -e "${BLUE}更新application.yml配置...${NC}"
    
    services=("game-user" "game-hall" "game-agentGame" "game-activity" "game-account")
    
    for service in "${services[@]}"; do
        config_file="$service/src/main/resources/application.yml"
        
        if [ -f "$config_file" ]; then
            # 检查是否已经有springdoc配置
            if grep -q "springdoc" "$config_file"; then
                echo -e "${GREEN}✅ $service 已有springdoc配置${NC}"
            else
                echo -e "${YELLOW}添加springdoc配置到 $service...${NC}"
                
                # 添加springdoc配置
                cat >> "$config_file" << EOF

# Swagger/OpenAPI配置
springdoc:
  api-docs:
    path: /v3/api-docs
  swagger-ui:
    path: /swagger-ui.html
    enabled: true
  show-actuator: true
EOF
                
                echo -e "${GREEN}✅ $service springdoc配置添加完成${NC}"
            fi
        else
            echo -e "${RED}❌ $service/src/main/resources/application.yml 不存在${NC}"
        fi
    done
}

# 重新编译项目
rebuild_project() {
    echo -e "${BLUE}重新编译项目...${NC}"
    
    if mvn clean compile -q; then
        echo -e "${GREEN}✅ 编译成功${NC}"
        return 0
    else
        echo -e "${RED}❌ 编译失败${NC}"
        return 1
    fi
}

# 测试Swagger访问
test_swagger_access() {
    echo -e "${BLUE}测试Swagger访问...${NC}"
    
    services=(
        "用户服务:8081"
        "大厅服务:8082"
        "代理游戏服务:8083"
        "活动服务:8084"
        "账户服务:8085"
    )
    
    for service in "${services[@]}"; do
        IFS=':' read -r name port <<< "$service"
        
        swagger_url="http://localhost:$port/swagger-ui.html"
        api_docs_url="http://localhost:$port/v3/api-docs"
        
        echo -e "${CYAN}测试 $name Swagger...${NC}"
        
        # 测试API文档
        if curl -s "$api_docs_url" > /dev/null 2>&1; then
            echo -e "${GREEN}  ✅ API文档可访问: $api_docs_url${NC}"
        else
            echo -e "${YELLOW}  ⚠️ API文档暂不可访问: $api_docs_url${NC}"
        fi
        
        # 测试Swagger UI
        if curl -s "$swagger_url" > /dev/null 2>&1; then
            echo -e "${GREEN}  ✅ Swagger UI可访问: $swagger_url${NC}"
        else
            echo -e "${YELLOW}  ⚠️ Swagger UI暂不可访问: $swagger_url${NC}"
        fi
        
        echo ""
    done
}

# 主函数
main() {
    echo -e "${CYAN}开始修复Swagger配置...${NC}"
    echo ""
    
    # 1. 添加Swagger依赖
    add_swagger_dependencies
    echo ""
    
    # 2. 创建Swagger配置类
    create_swagger_configs
    echo ""
    
    # 3. 更新application.yml配置
    update_application_configs
    echo ""
    
    # 4. 重新编译项目
    if rebuild_project; then
        echo ""
        echo -e "${GREEN}🎉 Swagger配置修复完成！${NC}"
        echo ""
        echo -e "${BLUE}下一步操作:${NC}"
        echo "1. 重启服务: ./start-services.sh"
        echo "2. 测试Swagger访问"
        echo ""
        echo -e "${BLUE}Swagger访问地址:${NC}"
        echo "• 用户服务: http://localhost:8081/swagger-ui.html"
        echo "• 大厅服务: http://localhost:8082/swagger-ui.html"
        echo "• 代理游戏服务: http://localhost:8083/swagger-ui.html"
        echo "• 活动服务: http://localhost:8084/swagger-ui.html"
        echo "• 账户服务: http://localhost:8085/swagger-ui.html"
        echo ""
        echo -e "${BLUE}通过网关访问:${NC}"
        echo "• 用户服务: http://localhost:8080/user/swagger-ui.html"
        echo "• 大厅服务: http://localhost:8080/hall/swagger-ui.html"
        echo "• 等等..."
    else
        echo ""
        echo -e "${RED}❌ 编译失败，请检查错误信息${NC}"
        exit 1
    fi
}

# 运行主函数
main
