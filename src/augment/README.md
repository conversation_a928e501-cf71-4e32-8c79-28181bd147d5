# WinGame项目 AI协作规则文件

## 📋 概述

本目录包含了专门为WinGame Java游戏服务器项目定制的AI协作规则文件，帮助开发团队更高效地与AI助手协作。

## 📁 文件说明

### 1. `ai_collaboration_guide.md`
**WinGame Java游戏服务器 AI 协作指南**

- **用途**: 提供与AI助手协作的基本原则和方法
- **适用场景**: 所有与WinGame项目相关的AI协作
- **核心内容**:
  - 项目技术栈和架构介绍
  - 有效沟通技巧和模板
  - 常见开发场景的协作方式
  - 项目特定的最佳实践

### 2. `instruction_optimization_workflow.md`
**WinGame项目指令优化工作流**

- **用途**: 自动优化用户指令，补充项目上下文
- **适用场景**: 需要AI助手理解项目特定要求的任务
- **核心内容**:
  - 指令分析和优化流程
  - 项目特定的技术要求补充
  - 常见指令的优化示例
  - WinGame架构约束的自动添加

### 3. `prompt_optimization_guide.md`
**WinGame项目提示词优化指南**

- **用途**: 编写高质量、项目特定的提示词
- **适用场景**: 需要精确控制AI输出质量的复杂任务
- **核心内容**:
  - 提示词优化原则和技巧
  - WinGame项目特定的上下文模板
  - 常见场景的提示词优化示例
  - 代码风格和架构约束的集成

## 🚀 使用方法

### 快速开始

1. **选择合适的指南**:
   - 日常协作 → `ai_collaboration_guide.md`
   - 指令优化 → `instruction_optimization_workflow.md`
   - 高级定制 → `prompt_optimization_guide.md`

2. **应用到实际工作**:
   - 复制相关模板
   - 填入具体的项目信息
   - 根据任务类型调整内容

3. **持续改进**:
   - 根据效果调整模板
   - 记录成功的协作模式
   - 分享给团队成员

### 使用场景示例

#### 场景1: 新功能开发
```markdown
# 使用 ai_collaboration_guide.md 中的功能开发模板
请为WinGame项目实现玩家匹配功能：

## 功能需求
- 支持多种匹配模式（快速匹配、排位匹配）
- 实时匹配状态更新
- 匹配超时处理

## 技术实现要求
- 使用项目的Engine框架
- 集成Redis缓存匹配队列
- 使用MongoDB记录匹配历史
...
```

#### 场景2: 问题排查
```markdown
# 使用 instruction_optimization_workflow.md 的优化流程
原始指令: "服务器有性能问题"

优化后指令: "WinGame项目game-hall模块性能问题排查：
- 响应时间从100ms增加到500ms
- 主要发生在玩家匹配功能
- 影响1000+并发用户
..."
```

#### 场景3: 代码审查
```markdown
# 使用 prompt_optimization_guide.md 的代码审查模板
请审查以下WinGame项目代码：

## 审查重点
- 是否符合项目架构规范
- 是否正确使用Engine框架
- 网络通信是否安全可靠
...
```

## 🎯 项目特定配置

### 技术栈信息
- **语言**: Java (JDK 18+)
- **构建工具**: Maven
- **网络框架**: Netty
- **数据库**: MongoDB
- **缓存**: Redis
- **消息队列**: Kafka
- **架构**: 微服务架构

### 主要模块
- `game-agentGame`: 代理游戏服务器
- `game-loginsr`: 登录服务器
- `game-hall`: 大厅服务器
- `game-billingsr`: 计费服务器
- `game-proxy`: 代理服务器
- `game-model`: 数据模型
- `game-message`: 消息定义

### 常用Maven命令
```bash
# 编译项目
mvn clean compile

# 运行测试
mvn test

# 打包项目
mvn clean package

# 使用特定profile
mvn clean package -P debug_local    # 本地开发
mvn clean package -P debug_wan      # 测试环境
mvn clean package -P debug_formal   # 生产环境
```

## 💡 最佳实践

### 1. **选择合适的文件**
- **简单任务**: 直接使用 `ai_collaboration_guide.md` 的模板
- **复杂任务**: 先用 `instruction_optimization_workflow.md` 优化指令
- **精确控制**: 使用 `prompt_optimization_guide.md` 定制提示词

### 2. **模板定制**
- 根据具体模块调整技术要求
- 添加项目特定的约束条件
- 包含相关的现有代码示例

### 3. **团队协作**
- 分享有效的协作模式
- 建立团队特定的模板库
- 定期更新和优化规则文件

### 4. **持续改进**
- 记录AI协作的效果和问题
- 根据项目发展更新技术栈信息
- 收集团队反馈优化模板

## 🔄 维护和更新

### 定期检查项目
- 技术栈版本更新
- 架构模式变化
- 新增模块和功能
- 编码规范调整

### 模板优化
- 根据使用效果调整模板
- 添加新的场景和示例
- 优化指令的准确性和完整性

### 团队反馈
- 收集开发团队的使用反馈
- 识别常见问题和改进点
- 分享成功的协作案例

## 📞 支持和反馈

如果在使用过程中遇到问题或有改进建议，请：

1. 检查是否正确使用了项目特定的模板
2. 确认技术栈和架构信息是否准确
3. 尝试不同的模板组合
4. 记录问题和改进建议，用于后续优化

---

**提示**: 这些规则文件是活文档，应该随着项目的发展和团队经验的积累不断更新和完善。
