# WinGame项目指令优化工作流

## 🔄 工作流程

针对WinGame Java游戏服务器项目，每次收到用户指令时，我将按以下步骤进行：

### 1. **指令分析阶段**
- 识别指令类型（功能开发/问题排查/性能优化/代码审查）
- 分析涉及的游戏服务器模块
- 识别缺失的技术细节
- 评估与项目架构的匹配度

### 2. **项目上下文补充**
- 确认涉及的微服务模块
- 补充相关的技术栈信息
- 添加项目架构约束
- 明确数据流和通信协议

### 3. **优化建议阶段**
- 提供结构化的指令版本
- 补充WinGame项目特定要求
- 添加技术实现细节
- 明确输出格式和验收标准

### 4. **执行确认阶段**
- 确认优化后的指令是否符合预期
- 询问是否需要进一步调整
- 记录项目特定的偏好设置

## 📋 优化模板

### 指令分析模板
```
## 📝 原始指令分析
**指令类型**: [功能开发/问题排查/性能优化/代码审查/架构设计]
**涉及模块**: [game-agentGame/game-loginsr/game-hall/game-billingsr/game-proxy/其他]
**技术栈**: [Java/Netty/MongoDB/Redis/Kafka/Maven]
**完整性评分**: [1-10分]

**主要问题**: 
- 缺失的项目上下文
- 不明确的技术要求
- 模糊的架构约束
- 缺少的输出格式

## ✨ 优化建议
**优化后的指令**:
[提供结构化、项目特定的指令版本]

**补充的项目信息**:
- WinGame架构规范
- 相关微服务模块
- 数据模型要求
- 网络通信协议
- 错误处理机制

## 🎯 执行确认
是否使用优化后的指令执行任务？
- [ ] 使用优化版本
- [ ] 使用原始指令
- [ ] 需要进一步调整
```

## 🔧 WinGame项目优化规则

### 1. **技术栈自动补充**
当指令涉及开发任务时，自动添加：
```markdown
## 技术实现要求
- 使用WinGame Engine框架
- 遵循微服务架构模式
- 网络通信使用Netty框架
- 数据持久化使用MongoDB
- 缓存操作使用Redis
- 日志记录集成Kafka
- 消息协议使用Protobuf
```

### 2. **架构规范补充**
```markdown
## 架构约束
- 继承项目基类（World、Manager、Handler等）
- 使用统一的包结构：com.game.[module].[layer]
- 遵循项目的错误处理机制
- 集成项目的配置管理系统
- 使用项目定义的常量和枚举
```

### 3. **Maven命令规范**
```markdown
## 构建和测试命令
- 编译: `mvn clean compile`
- 测试: `mvn test`
- 打包: `mvn clean package`
- 本地调试: `mvn clean package -P debug_local`
- 测试环境: `mvn clean package -P debug_wan`
- 生产环境: `mvn clean package -P debug_formal`
```

### 4. **输出格式标准化**
```markdown
## 期望输出
1. 完整的Java实现代码
2. 相关的数据模型Entity类
3. 网络消息Handler实现
4. DAO数据访问层代码
5. 相关的配置文件
6. 单元测试代码
7. 使用说明和注释
```

## 📊 优化示例

### 示例 1: 功能开发指令优化

**原始指令**:
"帮我实现一个玩家登录功能"

**优化分析**:
- 指令类型: 功能开发
- 涉及模块: game-loginsr
- 完整性评分: 3/10
- 主要问题: 缺少具体需求、技术实现细节、安全要求

**优化后的指令**:
```markdown
请为WinGame项目的game-loginsr模块实现玩家登录功能：

## 功能需求
- 支持手机号+验证码登录
- 支持账号密码登录
- 登录状态验证和维护
- 防重复登录机制
- 登录日志记录

## 技术实现要求
- 使用WinGame Engine框架
- 继承LoginServer的World基类
- 网络通信使用Netty TCP
- 消息协议使用Protobuf
- 数据存储使用MongoDB
- 会话管理使用Redis
- 日志记录使用Kafka

## 安全要求
- 密码加密存储（使用项目的加密工具）
- 验证码有效期控制
- 登录失败次数限制
- 防暴力破解机制

## 架构要求
- 创建LoginManager业务逻辑类
- 实现LoginHandler网络消息处理
- 定义Account数据模型
- 实现AccountDao数据访问层
- 集成SessionManager会话管理

## 输出要求
1. LoginManager.java - 登录业务逻辑
2. LoginHandler.java - 网络消息处理
3. Account.java - 账号数据模型
4. AccountDao.java - 数据访问层
5. 相关的Protobuf消息定义
6. 登录相关配置文件
7. 单元测试代码
```

### 示例 2: 性能优化指令优化

**原始指令**:
"游戏服务器响应很慢，帮我优化一下"

**优化分析**:
- 指令类型: 性能优化
- 涉及模块: 未明确
- 完整性评分: 2/10
- 主要问题: 缺少具体症状、性能数据、影响范围

**优化后的指令**:
```markdown
WinGame项目性能优化需求：

## 问题描述
- 具体模块: [game-hall/game-agentGame/其他]
- 响应时间: [具体毫秒数]
- 并发用户数: [具体数量]
- 主要症状: [CPU高/内存泄漏/数据库慢查询/网络延迟]
- 影响范围: [特定功能/整体性能]

## 环境信息
- 部署环境: [开发/测试/生产]
- 服务器配置: [CPU/内存/磁盘]
- JVM参数: [堆内存设置等]
- 数据库状态: [MongoDB连接数/查询性能]
- Redis状态: [内存使用/命中率]

## 性能数据
- JVM监控数据
- MongoDB慢查询日志
- Redis性能指标
- Netty网络统计
- 应用日志分析

## 优化目标
- 响应时间目标: [具体毫秒数]
- 并发处理能力: [目标QPS]
- 资源使用限制: [CPU/内存上限]

## 技术约束
- 不能破坏现有功能
- 保持数据一致性
- 兼容现有客户端协议
- 遵循WinGame架构规范

## 期望输出
1. 性能瓶颈分析报告
2. 优化方案设计
3. 代码优化实现
4. 配置参数调优
5. 性能测试方案
6. 监控和预警机制
```

## 🎯 使用方式

从现在开始，当您发送关于WinGame项目的指令时，我会：

1. **立即分析**指令的项目相关性
2. **补充项目上下文**和技术要求
3. **提供优化建议**
4. **等待确认**后执行任务

### 控制选项
- 说"直接执行"跳过优化步骤
- 说"详细分析"获取更深入的项目分析
- 说"简化版本"获取精简的优化建议

## 📈 项目特定改进

### WinGame架构适配
- 自动识别微服务模块依赖关系
- 补充服务间通信协议要求
- 添加数据一致性保证机制
- 集成项目的监控和日志系统

### 游戏服务器特性
- 考虑高并发和实时性要求
- 添加游戏逻辑相关的约束
- 集成游戏数据模型规范
- 考虑游戏安全和防作弊机制

准备好开始优化您的WinGame项目指令了吗？请发送您的下一个指令！
