# WinGame项目提示词优化指南

## 🎯 提示词优化原则

### 1. **项目特定性原则**
- 明确指定WinGame项目相关的技术栈
- 引用项目现有的架构模式和代码规范
- 使用项目特定的术语和概念
- 提供项目上下文信息

**示例对比**：
```
❌ 差的提示词：
"帮我写个游戏服务器"

✅ 好的提示词：
"请为WinGame项目的game-hall模块实现房间管理功能，使用项目的Engine框架，集成Netty网络通信和MongoDB数据存储"
```

### 2. **技术栈明确原则**
明确指定WinGame项目的技术要求：
- Java + Maven构建
- Netty网络框架
- MongoDB数据存储
- Redis缓存
- Kafka消息队列
- Protobuf消息协议

### 3. **架构约束原则**
提供WinGame项目的架构约束：
- 微服务架构模式
- 统一的包结构规范
- 继承项目基类要求
- 服务间通信协议

## 🔧 WinGame项目优化技巧

### 1. **角色定义优化**
```markdown
# 项目特定角色定义
你是WinGame Java游戏服务器项目的资深开发工程师，具有以下专业技能：

## 技术专长
- Java微服务架构设计和实现
- Netty高性能网络编程
- MongoDB数据库设计和优化
- Redis缓存架构和性能调优
- Kafka消息队列集成
- 游戏服务器高并发处理
- Protobuf消息协议设计

## 项目经验
- 熟悉WinGame项目的整体架构
- 了解各微服务模块的职责和交互
- 掌握项目的编码规范和最佳实践
- 具备游戏服务器安全和性能优化经验
```

### 2. **技术上下文提供**
```markdown
# WinGame项目技术上下文
## 项目结构
- game-agentGame: 代理游戏服务器
- game-loginsr: 登录认证服务
- game-hall: 游戏大厅服务
- game-billingsr: 计费结算服务
- game-proxy: 网络代理服务
- game-model: 数据模型定义
- game-message: 消息协议定义

## 核心框架
- Engine: 项目自定义游戏引擎
- World: 服务器主循环基类
- Manager: 业务逻辑管理器基类
- Handler: 网络消息处理基类
- DAO: 数据访问对象基类

## 通信协议
- 服务间通信: Netty TCP + Protobuf
- 客户端通信: WebSocket + JSON/Protobuf
- 内部消息: InnerMessage协议
- 外部API: HTTP RESTful接口
```

### 3. **约束条件明确**
```markdown
# WinGame项目开发约束
## 必须遵循
- 使用Maven进行项目构建和依赖管理
- 继承项目定义的基类（World、Manager、Handler等）
- 遵循com.game.[module].[layer]包结构
- 使用项目统一的错误处理机制（ErrorCode枚举）
- 集成项目的日志系统（Kafka LogProducer）
- 使用项目配置的数据库连接池

## 禁止使用
- 直接使用第三方ORM框架（项目有自定义DAO层）
- 硬编码配置信息（使用配置文件和环境变量）
- 阻塞主线程的同步操作
- 不安全的网络通信方式
```

## 📝 常见场景优化

### 1. **功能开发提示词优化**
```markdown
# 优化前
"实现一个游戏房间功能"

# 优化后
"请为WinGame项目的game-hall模块实现游戏房间管理功能：

## 功能需求
- 创建/销毁游戏房间
- 玩家加入/离开房间
- 房间状态管理（等待/游戏中/结算）
- 房间信息广播
- 支持不同游戏类型

## 技术实现
- 继承HallServer的World基类
- 创建RoomManager业务逻辑类
- 实现RoomHandler网络消息处理
- 使用Redis缓存房间状态
- MongoDB持久化房间历史数据
- 集成Kafka记录房间事件日志

## 架构设计
- Room实体类定义
- RoomDao数据访问层
- 房间状态机设计
- 玩家-房间关系管理
- 房间事件通知机制

## 消息协议
- 定义房间相关的Protobuf消息
- 实现客户端-服务器通信协议
- 设计服务器间房间同步协议

## 输出要求
1. RoomManager.java - 房间管理器
2. RoomHandler.java - 消息处理器
3. Room.java - 房间实体类
4. RoomDao.java - 数据访问层
5. 相关Protobuf消息定义
6. 房间配置文件
7. 单元测试代码"
```

### 2. **问题排查提示词优化**
```markdown
# 优化前
"服务器有内存泄漏问题"

# 优化后
"WinGame项目内存泄漏问题排查：

## 问题现象
- 影响模块: [game-hall/game-agentGame/其他]
- 内存增长趋势: [持续增长/阶段性增长]
- 触发条件: [特定操作/时间相关/负载相关]
- 系统表现: [GC频繁/响应变慢/OOM异常]

## 环境信息
- JVM版本和参数配置
- 堆内存设置: -Xms -Xmx
- GC策略配置
- 服务器负载情况
- 最近的代码变更

## 监控数据
- JVM内存使用趋势图
- GC日志分析结果
- 堆转储文件（如有）
- 应用性能监控数据

## 怀疑区域
- 缓存管理（Redis连接池）
- 数据库连接（MongoDB连接池）
- 网络连接（Netty Channel管理）
- 定时任务和线程池
- 游戏对象生命周期管理

## 期望输出
1. 内存泄漏根因分析
2. 问题代码定位
3. 修复方案实现
4. 内存监控优化建议
5. 预防措施和最佳实践"
```

### 3. **性能优化提示词优化**
```markdown
# 优化前
"优化数据库查询性能"

# 优化后
"WinGame项目MongoDB查询性能优化：

## 性能问题
- 慢查询模块: [具体DAO类]
- 查询响应时间: [当前/目标毫秒数]
- 查询频率: [QPS数据]
- 数据量规模: [集合大小/文档数量]

## 当前实现
- 查询语句和索引使用情况
- 数据模型设计
- 分页和排序逻辑
- 缓存策略

## 优化目标
- 响应时间目标: [具体毫秒数]
- 并发处理能力: [目标QPS]
- 资源使用限制: [CPU/内存/网络]

## 技术约束
- 保持数据一致性
- 兼容现有业务逻辑
- 遵循WinGame DAO层规范
- 不影响其他模块性能

## 期望输出
1. 查询性能分析报告
2. 索引优化方案
3. 数据模型调整建议
4. 缓存策略优化
5. 代码实现改进
6. 性能测试方案"
```

## 🚀 高级优化技巧

### 1. **模块化提示**
```markdown
# 第一步：架构设计
"请为WinGame项目设计[功能]的微服务架构，包括：
- 服务职责划分
- 数据模型设计
- 服务间通信协议
- 缓存和存储策略"

# 第二步：核心实现
"基于上面的架构设计，实现[功能]的核心业务逻辑Manager类"

# 第三步：网络层
"实现[功能]的网络消息处理Handler和Protobuf协议定义"

# 第四步：数据层
"实现[功能]的数据访问DAO层和MongoDB集合设计"
```

### 2. **上下文延续**
```markdown
"基于我们刚才实现的WinGame [功能]模块，现在请添加以下扩展功能：
- [扩展功能1]
- [扩展功能2]
请保持与现有代码的一致性和兼容性"
```

## 💡 WinGame项目实用建议

### 1. **代码风格参考**
```markdown
请参考以下WinGame项目的代码风格：
- 类命名: PascalCase（如PlayerManager）
- 方法命名: camelCase（如updatePlayerInfo）
- 常量命名: UPPER_SNAKE_CASE（如MAX_PLAYER_COUNT）
- 包结构: com.game.[module].[layer]
- 注释风格: JavaDoc标准格式
```

### 2. **错误处理规范**
```markdown
使用WinGame项目的错误处理机制：
- 使用ErrorCode枚举定义错误码
- 统一的异常处理和日志记录
- 客户端友好的错误消息
- 服务器间错误传播协议
```

### 3. **性能考虑**
```markdown
遵循WinGame项目的性能最佳实践：
- 合理使用Redis缓存减少数据库访问
- 优化MongoDB查询和索引设计
- 避免阻塞主线程的同步操作
- 使用对象池减少GC压力
- 合理设计消息批处理机制
```

## 📊 效果评估

### 好的提示词特征（WinGame项目）
- ✅ 明确指定项目模块和技术栈
- ✅ 遵循项目架构和编码规范
- ✅ 包含完整的实现要求
- ✅ 考虑项目特定的约束条件
- ✅ 提供项目上下文信息

### 需要优化的信号
- ❌ 使用通用的技术方案，不符合项目架构
- ❌ 忽略项目的基类和框架要求
- ❌ 缺少游戏服务器特定的考虑
- ❌ 不符合项目的编码和命名规范

---

**提示**: 在编写WinGame项目的提示词时，始终考虑项目的微服务架构、技术栈约束和游戏服务器的特殊需求，这样可以获得更准确和实用的AI协助。
