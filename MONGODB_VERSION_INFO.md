# MongoDB 版本配置说明

## 当前配置

### ✅ 优化后的版本管理策略

项目已经采用了最佳的MongoDB版本管理方式：

1. **根pom.xml**: 移除了显式的MongoDB版本定义
2. **子模块**: 所有子模块都使用Spring Boot Parent管理的版本
3. **版本继承**: 自动使用Spring Boot 2.7.14兼容的MongoDB版本

### 📋 当前版本信息

- **Spring Boot**: 2.7.14
- **Spring Data MongoDB**: 3.4.14 (由Spring Boot管理)
- **MongoDB Java Driver**: 4.6.1 (由Spring Data MongoDB管理)
- **支持的MongoDB服务器版本**: 3.6+ (推荐 4.4+)

## 🔧 配置详情

### 根pom.xml
```xml
<parent>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-parent</artifactId>
    <version>2.7.14</version>
    <relativePath/>
</parent>

<!-- 不需要在dependencyManagement中指定MongoDB版本 -->
<!-- Spring Boot Parent会自动管理兼容的版本 -->
```

### 子模块pom.xml
```xml
<!-- MongoDB依赖 - 无需指定版本 -->
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-data-mongodb</artifactId>
    <!-- 版本由Spring Boot Parent管理 -->
</dependency>
```

## 🎯 优势

### 1. **版本兼容性保证**
- Spring Boot团队确保所有依赖版本之间的兼容性
- 避免版本冲突和兼容性问题

### 2. **自动更新**
- 升级Spring Boot版本时，MongoDB版本会自动更新到兼容版本
- 减少手动维护版本的工作量

### 3. **最佳实践**
- 遵循Spring Boot官方推荐的依赖管理方式
- 简化项目配置

## 📊 版本对应关系

| Spring Boot | Spring Data MongoDB | MongoDB Driver | 支持的MongoDB服务器 |
|-------------|-------------------|----------------|-------------------|
| 2.7.14      | 3.4.14           | 4.6.1          | 3.6 - 6.0        |
| 2.6.x       | 3.3.x            | 4.4.x          | 3.6 - 5.0        |
| 2.5.x       | 3.2.x            | 4.2.x          | 3.6 - 5.0        |

## 🔍 如何查看实际使用的版本

### 1. 使用Maven命令
```bash
# 查看依赖树
mvn dependency:tree | grep mongodb

# 查看有效POM
mvn help:effective-pom | grep mongodb
```

### 2. 在IDE中查看
- IntelliJ IDEA: 在pom.xml中按Ctrl+点击依赖
- Eclipse: 在Dependencies视图中查看

### 3. 运行时查看
```java
@Component
public class MongoVersionChecker {
    
    @Autowired
    private MongoTemplate mongoTemplate;
    
    @PostConstruct
    public void checkVersion() {
        try {
            Document buildInfo = mongoTemplate.getDb()
                .runCommand(new Document("buildInfo", 1));
            String version = buildInfo.getString("version");
            log.info("MongoDB Server Version: {}", version);
            
            // 查看Driver版本
            String driverVersion = MongoDriverInformation.driverVersion();
            log.info("MongoDB Driver Version: {}", driverVersion);
        } catch (Exception e) {
            log.error("Failed to get MongoDB version", e);
        }
    }
}
```

## 🚀 如果需要自定义版本

如果确实需要使用特定的MongoDB版本，可以在根pom.xml中添加：

```xml
<properties>
    <!-- 覆盖Spring Boot管理的版本 -->
    <spring-data-mongodb.version>3.4.15</spring-data-mongodb.version>
    <mongodb-driver-sync.version>4.7.2</mongodb-driver-sync.version>
</properties>
```

**⚠️ 注意**: 自定义版本可能导致兼容性问题，建议充分测试。

## 🐳 Docker MongoDB配置

### docker-compose.yml
```yaml
mongodb:
  image: mongo:6.0  # 推荐使用6.0版本
  container_name: mongodb
  environment:
    - MONGO_INITDB_ROOT_USERNAME=admin
    - MONGO_INITDB_ROOT_PASSWORD=admin123
  ports:
    - "27017:27017"
  volumes:
    - mongodb_data:/data/db
```

### 连接配置
```yaml
spring:
  data:
    mongodb:
      host: localhost
      port: 27017
      database: wingame_db
      username: admin
      password: admin123
      authentication-database: admin
```

## 📝 最佳实践建议

1. **保持默认版本**: 除非有特殊需求，建议使用Spring Boot管理的版本
2. **定期更新**: 定期升级Spring Boot版本以获得最新的MongoDB支持
3. **测试兼容性**: 升级前在测试环境验证兼容性
4. **监控性能**: 升级后监控应用性能和数据库连接

## 🔗 相关文档

- [Spring Data MongoDB Reference](https://docs.spring.io/spring-data/mongodb/docs/current/reference/html/)
- [MongoDB Java Driver Documentation](https://mongodb.github.io/mongo-java-driver/)
- [Spring Boot Dependency Versions](https://docs.spring.io/spring-boot/docs/2.7.14/reference/html/dependency-versions.html)

---

**总结**: 当前项目已经采用了最佳的MongoDB版本管理策略，无需手动指定版本，Spring Boot会自动管理兼容的版本。
