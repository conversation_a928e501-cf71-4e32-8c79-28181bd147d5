#!/bin/bash

echo "=== WinGame 网关层聚合演示 ==="
echo "演示用户服务通过Feign调用账户服务，实现数据聚合"
echo ""

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

GATEWAY_URL="http://localhost:8080"
USER_SERVICE_URL="http://localhost:8081"
ACCOUNT_SERVICE_URL="http://localhost:8085"

# 测试用户ID
TEST_USER_ID="demo_user_123"

# 检查服务状态
check_services() {
    echo -e "${BLUE}检查服务状态...${NC}"
    
    services=(
        "网关服务:$GATEWAY_URL"
        "用户服务:$USER_SERVICE_URL"
        "账户服务:$ACCOUNT_SERVICE_URL"
    )
    
    all_up=true
    
    for service in "${services[@]}"; do
        IFS=':' read -r name url <<< "$service"
        
        if curl -s "$url" > /dev/null 2>&1; then
            echo -e "${GREEN}✅ $name 运行正常${NC}"
        else
            echo -e "${RED}❌ $name 无法访问${NC}"
            all_up=false
        fi
    done
    
    if [ "$all_up" = false ]; then
        echo ""
        echo -e "${YELLOW}⚠️ 部分服务未启动，请先运行:${NC}"
        echo "  ./start-infrastructure.sh"
        echo "  ./start-services.sh"
        echo ""
        echo -e "${BLUE}继续演示（可能会有降级响应）...${NC}"
    fi
    
    echo ""
}

# 执行API测试
test_api() {
    local method=$1
    local url=$2
    local description=$3
    local data=$4
    
    echo -e "${CYAN}🚀 测试: $description${NC}"
    echo -e "${YELLOW}   请求: $method $url${NC}"
    
    if [ -n "$data" ]; then
        response=$(curl -s -X $method "$url" \
            -H "Content-Type: application/json" \
            -d "$data" \
            -w "\nHTTP_CODE:%{http_code}")
    else
        response=$(curl -s -X $method "$url" \
            -w "\nHTTP_CODE:%{http_code}")
    fi
    
    http_code=$(echo "$response" | grep "HTTP_CODE:" | cut -d: -f2)
    body=$(echo "$response" | sed '/HTTP_CODE:/d')
    
    if [ "$http_code" = "200" ]; then
        echo -e "${GREEN}   ✅ 成功 (HTTP $http_code)${NC}"
        echo "$body" | python3 -m json.tool 2>/dev/null | head -20 || echo "$body" | head -10
    else
        echo -e "${RED}   ❌ 失败 (HTTP $http_code)${NC}"
        echo "$body" | head -5
    fi
    echo ""
}

# 演示聚合功能
demo_aggregation() {
    echo -e "${PURPLE}=== 网关层聚合功能演示 ===${NC}"
    echo ""
    
    echo -e "${BLUE}1. 聚合服务健康检查${NC}"
    test_api "GET" "$GATEWAY_URL/user/api/user/aggregation/health" "聚合服务健康检查"
    
    echo -e "${BLUE}2. 获取用户完整档案（聚合用户信息+账户余额+统计信息）${NC}"
    test_api "GET" "$GATEWAY_URL/user/api/user/aggregation/profile/$TEST_USER_ID" "获取用户完整档案"
    
    echo -e "${BLUE}3. 获取用户信息和账户余额${NC}"
    test_api "GET" "$GATEWAY_URL/user/api/user/aggregation/profile/$TEST_USER_ID/balance" "获取用户信息和余额"
    
    echo -e "${BLUE}4. 获取用户信息和账户统计${NC}"
    test_api "GET" "$GATEWAY_URL/user/api/user/aggregation/profile/$TEST_USER_ID/statistics" "获取用户信息和统计"
    
    echo -e "${BLUE}5. 批量获取用户档案${NC}"
    test_api "GET" "$GATEWAY_URL/user/api/user/aggregation/profiles?userIds=user001,user002,user003" "批量获取用户档案"
}

# 演示Feign调用链路
demo_feign_chain() {
    echo -e "${PURPLE}=== Feign调用链路演示 ===${NC}"
    echo ""
    
    echo -e "${BLUE}调用链路: 网关 -> 用户服务 -> (Feign) -> 账户服务${NC}"
    echo ""
    
    echo -e "${CYAN}步骤1: 直接调用账户服务${NC}"
    test_api "GET" "$GATEWAY_URL/account/api/account/$TEST_USER_ID/balance" "直接获取账户余额"
    
    echo -e "${CYAN}步骤2: 通过用户服务Feign调用账户服务${NC}"
    test_api "GET" "$GATEWAY_URL/user/api/user/aggregation/profile/$TEST_USER_ID/balance" "通过Feign获取聚合信息"
    
    echo -e "${CYAN}步骤3: 模拟账户服务不可用的降级处理${NC}"
    echo -e "${YELLOW}   (如果账户服务不可用，会返回默认数据)${NC}"
    test_api "GET" "$GATEWAY_URL/user/api/user/aggregation/profile/fallback_test_user/balance" "测试降级处理"
}

# 演示并发聚合
demo_concurrent_aggregation() {
    echo -e "${PURPLE}=== 并发聚合演示 ===${NC}"
    echo ""
    
    echo -e "${BLUE}并发调用多个用户的档案信息...${NC}"
    
    # 创建临时文件存储结果
    temp_file="/tmp/aggregation_test_$$"
    
    # 并发请求
    for i in {1..5}; do
        (
            user_id="concurrent_user_$i"
            start_time=$(date +%s%N)
            
            response=$(curl -s "$GATEWAY_URL/user/api/user/aggregation/profile/$user_id")
            
            end_time=$(date +%s%N)
            duration=$(( (end_time - start_time) / 1000000 ))
            
            echo "用户 $user_id: ${duration}ms" >> "$temp_file"
        ) &
    done
    
    # 等待所有请求完成
    wait
    
    # 显示结果
    if [ -f "$temp_file" ]; then
        echo -e "${GREEN}并发聚合结果:${NC}"
        cat "$temp_file"
        rm -f "$temp_file"
    fi
    
    echo ""
}

# 演示错误处理和降级
demo_fallback() {
    echo -e "${PURPLE}=== 降级处理演示 ===${NC}"
    echo ""
    
    echo -e "${BLUE}测试各种异常情况的降级处理...${NC}"
    echo ""
    
    echo -e "${CYAN}1. 正常用户${NC}"
    test_api "GET" "$GATEWAY_URL/user/api/user/aggregation/profile/normal_user" "正常用户档案"
    
    echo -e "${CYAN}2. 不存在的用户${NC}"
    test_api "GET" "$GATEWAY_URL/user/api/user/aggregation/profile/non_existent_user" "不存在的用户"
    
    echo -e "${CYAN}3. 特殊字符用户ID${NC}"
    test_api "GET" "$GATEWAY_URL/user/api/user/aggregation/profile/user@#$%" "特殊字符用户ID"
}

# 性能测试
performance_test() {
    echo -e "${PURPLE}=== 性能测试 ===${NC}"
    echo ""
    
    echo -e "${BLUE}测试聚合接口性能...${NC}"
    
    total_requests=10
    success_count=0
    total_time=0
    
    for i in $(seq 1 $total_requests); do
        start_time=$(date +%s%N)
        
        response=$(curl -s -o /dev/null -w "%{http_code}" \
            "$GATEWAY_URL/user/api/user/aggregation/profile/perf_test_user_$i")
        
        end_time=$(date +%s%N)
        duration=$(( (end_time - start_time) / 1000000 ))
        total_time=$((total_time + duration))
        
        if [ "$response" = "200" ]; then
            success_count=$((success_count + 1))
        fi
        
        echo -n "."
    done
    
    echo ""
    echo ""
    echo -e "${GREEN}性能测试结果:${NC}"
    echo "  总请求数: $total_requests"
    echo "  成功请求: $success_count"
    echo "  成功率: $(( success_count * 100 / total_requests ))%"
    echo "  平均响应时间: $(( total_time / total_requests ))ms"
    echo "  总耗时: ${total_time}ms"
    echo ""
}

# 主函数
main() {
    echo -e "${CYAN}开始网关层聚合演示...${NC}"
    echo ""
    
    # 1. 检查服务状态
    check_services
    
    # 2. 演示聚合功能
    demo_aggregation
    
    # 3. 演示Feign调用链路
    demo_feign_chain
    
    # 4. 演示并发聚合
    demo_concurrent_aggregation
    
    # 5. 演示降级处理
    demo_fallback
    
    # 6. 性能测试
    performance_test
    
    echo -e "${GREEN}🎉 网关层聚合演示完成！${NC}"
    echo ""
    echo -e "${BLUE}演示总结:${NC}"
    echo "✅ 用户服务通过Feign调用账户服务"
    echo "✅ 网关层统一入口，服务间透明调用"
    echo "✅ 数据聚合，减少客户端请求次数"
    echo "✅ 降级处理，保证服务可用性"
    echo "✅ 并发调用，提高响应性能"
    echo ""
    echo -e "${BLUE}技术特点:${NC}"
    echo "• Spring Cloud OpenFeign 服务间调用"
    echo "• 异步并发聚合，提高性能"
    echo "• 熔断降级，保证系统稳定性"
    echo "• 统一错误处理和响应格式"
    echo "• 支持批量操作和性能优化"
    echo ""
    echo -e "${YELLOW}🔗 相关API文档:${NC}"
    echo "• 用户聚合API: $GATEWAY_URL/user/api/user/aggregation/"
    echo "• 健康检查: $GATEWAY_URL/user/api/user/aggregation/health"
    echo "• Swagger文档: $GATEWAY_URL/user/swagger-ui.html"
}

# 运行主函数
main
