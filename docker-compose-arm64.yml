version: '3.8'

services:
  # Nacos (ARM64兼容版本)
  nacos:
    image: nacos/nacos-server:v2.2.1
    container_name: nacos
    environment:
      - MODE=standalone
      - PREFER_HOST_MODE=hostname
      - NACOS_SERVER_PORT=8848
      - NACOS_APPLICATION_PORT=8848
    ports:
      - "8848:8848"
      - "9848:9848"
    restart: always
    networks:
      - wingame-network

  # MongoDB (ARM64原生支持)
  mongodb:
    image: mongo:6.0
    container_name: mongodb
    environment:
      - MONGO_INITDB_ROOT_USERNAME=admin
      - MONGO_INITDB_ROOT_PASSWORD=admin123
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
    restart: always
    networks:
      - wingame-network

  # Redis (ARM64原生支持)
  redis:
    image: redis:7.0-alpine
    container_name: redis
    command: redis-server --requirepass redis123
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: always
    networks:
      - wingame-network

volumes:
  mongodb_data:
  redis_data:

networks:
  wingame-network:
    driver: bridge
