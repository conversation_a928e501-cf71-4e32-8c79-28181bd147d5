#!/bin/bash

echo "=== Docker ARM64 兼容性测试 ==="
echo ""

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 检测系统架构
ARCH=$(uname -m)
echo -e "${BLUE}系统架构: $ARCH${NC}"

# 检测操作系统
OS=$(uname -s)
echo -e "${BLUE}操作系统: $OS${NC}"

# 检测是否为Apple Silicon
if [[ "$ARCH" == "arm64" ]] && [[ "$OS" == "Darwin" ]]; then
    echo -e "${YELLOW}检测到 Apple Silicon Mac${NC}"
    IS_APPLE_SILICON=true
else
    echo -e "${BLUE}检测到 Intel/AMD 架构${NC}"
    IS_APPLE_SILICON=false
fi

echo ""

# 检查Docker是否安装和运行
echo -e "${BLUE}检查Docker环境...${NC}"
if ! command -v docker &> /dev/null; then
    echo -e "${RED}❌ Docker未安装${NC}"
    exit 1
fi

if ! docker info > /dev/null 2>&1; then
    echo -e "${RED}❌ Docker未运行，请先启动Docker Desktop${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Docker环境正常${NC}"

# 检查Docker版本
DOCKER_VERSION=$(docker --version)
echo -e "${BLUE}Docker版本: $DOCKER_VERSION${NC}"

echo ""

# 测试镜像拉取
echo -e "${BLUE}测试镜像兼容性...${NC}"

test_image() {
    local image=$1
    local name=$2
    
    echo -n "测试 $name ($image): "
    
    if docker pull "$image" > /dev/null 2>&1; then
        echo -e "${GREEN}✅ 成功${NC}"
        return 0
    else
        echo -e "${RED}❌ 失败${NC}"
        return 1
    fi
}

# 测试各个镜像
test_image "mongo:6.0" "MongoDB"
test_image "redis:7.0-alpine" "Redis"
test_image "nacos/nacos-server:v2.2.1" "Nacos"

if [[ "$IS_APPLE_SILICON" == true ]]; then
    test_image "mysql:8.0" "MySQL"
fi

echo ""

# 推荐配置
echo -e "${BLUE}推荐配置:${NC}"
if [[ "$IS_APPLE_SILICON" == true ]]; then
    echo -e "${YELLOW}建议使用: docker-compose-arm64.yml${NC}"
    echo "原因: Apple Silicon原生支持，性能更好"
    RECOMMENDED_FILE="docker-compose-arm64.yml"
else
    echo -e "${YELLOW}建议使用: docker-compose.yml${NC}"
    echo "原因: Intel/AMD架构，完整功能支持"
    RECOMMENDED_FILE="docker-compose.yml"
fi

echo ""

# 检查配置文件是否存在
echo -e "${BLUE}检查配置文件...${NC}"
if [[ -f "$RECOMMENDED_FILE" ]]; then
    echo -e "${GREEN}✅ $RECOMMENDED_FILE 存在${NC}"
else
    echo -e "${RED}❌ $RECOMMENDED_FILE 不存在${NC}"
    exit 1
fi

if [[ -f "start-infrastructure.sh" ]]; then
    echo -e "${GREEN}✅ start-infrastructure.sh 存在${NC}"
else
    echo -e "${RED}❌ start-infrastructure.sh 不存在${NC}"
    exit 1
fi

echo ""

# 提供启动建议
echo -e "${BLUE}启动建议:${NC}"
echo "1. 自动启动（推荐）:"
echo -e "   ${YELLOW}./start-infrastructure.sh${NC}"
echo ""
echo "2. 手动启动:"
echo -e "   ${YELLOW}docker-compose -f $RECOMMENDED_FILE up -d${NC}"
echo ""
echo "3. 查看日志:"
echo -e "   ${YELLOW}docker-compose -f $RECOMMENDED_FILE logs${NC}"
echo ""

# 性能提示
if [[ "$IS_APPLE_SILICON" == true ]]; then
    echo -e "${BLUE}Apple Silicon 优化提示:${NC}"
    echo "• 使用ARM64原生镜像可获得最佳性能"
    echo "• 避免使用 platform: linux/amd64 除非必要"
    echo "• 如遇问题，可尝试 Rosetta 2 模拟模式"
    echo ""
fi

# 故障排除提示
echo -e "${BLUE}常见问题解决:${NC}"
echo "• 如果镜像拉取失败，检查网络连接"
echo "• 如果容器启动失败，查看 docker logs <container_name>"
echo "• 如果端口冲突，使用 lsof -i :<port> 检查占用"
echo "• 详细指南请查看 ARM64_COMPATIBILITY_GUIDE.md"

echo ""
echo -e "${GREEN}🎉 兼容性测试完成！${NC}"
