package com.wingame.hall.config;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import io.swagger.v3.oas.models.servers.Server;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * Swagger配置类
 */
@Configuration
public class SwaggerConfig {

    @Bean
    public OpenAPI customOpenAPI() {
        return new OpenAPI()
                .info(new Info()
                        .title("WinGame 大厅服务 API")
                        .description("大厅服务 API文档")
                        .version("1.0.0")
                        .contact(new Contact()
                                .name("WinGame Team")
                                .email("<EMAIL>")
                                .url("https://wingame.com"))
                        .license(new License()
                                .name("MIT License")
                                .url("https://opensource.org/licenses/MIT")))
                .servers(List.of(
                        new Server()
                                .url("http://localhost:8082")
                                .description("本地开发环境"),
                        new Server()
                                .url("http://localhost:8080/hall")
                                .description("通过网关访问")
                ));
    }
}
