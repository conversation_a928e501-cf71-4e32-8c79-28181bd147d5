package com.wingame.hall.controller;

import com.wingame.common.result.Result;
import com.wingame.hall.entity.GameRoom;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * 大厅控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/hall")
@RequiredArgsConstructor
public class HallController {
    
    /**
     * 获取所有游戏房间
     */
    @GetMapping("/rooms")
    public Result<List<GameRoom>> getAllRooms() {
        log.info("获取所有游戏房间");
        
        // 模拟数据
        List<GameRoom> rooms = new ArrayList<>();
        
        GameRoom room1 = new GameRoom();
        room1.setId(UUID.randomUUID().toString());
        room1.setRoomName("德州扑克房间1");
        room1.setGameType("TEXAS_HOLDEM");
        room1.setRoomType(1);
        room1.setMaxPlayers(6);
        room1.setCurrentPlayers(3);
        room1.setMinBet(100L);
        room1.setMaxBet(10000L);
        room1.setStatus(1);
        room1.setPlayers(List.of("user1", "user2", "user3"));
        room1.setCreateTime(LocalDateTime.now());
        room1.setUpdateTime(LocalDateTime.now());
        rooms.add(room1);
        
        GameRoom room2 = new GameRoom();
        room2.setId(UUID.randomUUID().toString());
        room2.setRoomName("百家乐VIP房间");
        room2.setGameType("BACCARAT");
        room2.setRoomType(2);
        room2.setMaxPlayers(8);
        room2.setCurrentPlayers(5);
        room2.setMinBet(1000L);
        room2.setMaxBet(100000L);
        room2.setStatus(2);
        room2.setPlayers(List.of("vip1", "vip2", "vip3", "vip4", "vip5"));
        room2.setCreateTime(LocalDateTime.now());
        room2.setUpdateTime(LocalDateTime.now());
        rooms.add(room2);
        
        return Result.success("获取房间列表成功", rooms);
    }
    
    /**
     * 根据ID获取房间信息
     */
    @GetMapping("/rooms/{roomId}")
    public Result<GameRoom> getRoomById(@PathVariable String roomId) {
        log.info("获取房间信息: {}", roomId);
        
        GameRoom room = new GameRoom();
        room.setId(roomId);
        room.setRoomName("测试房间");
        room.setGameType("POKER");
        room.setRoomType(1);
        room.setMaxPlayers(6);
        room.setCurrentPlayers(2);
        room.setMinBet(50L);
        room.setMaxBet(5000L);
        room.setStatus(1);
        room.setPlayers(List.of("player1", "player2"));
        room.setCreateTime(LocalDateTime.now());
        room.setUpdateTime(LocalDateTime.now());
        
        return Result.success("获取房间信息成功", room);
    }
    
    /**
     * 创建游戏房间
     */
    @PostMapping("/rooms")
    public Result<GameRoom> createRoom(@RequestBody GameRoom room) {
        log.info("创建游戏房间: {}", room.getRoomName());
        
        room.setId(UUID.randomUUID().toString());
        room.setCurrentPlayers(0);
        room.setStatus(1);
        room.setPlayers(new ArrayList<>());
        room.setCreateTime(LocalDateTime.now());
        room.setUpdateTime(LocalDateTime.now());
        
        return Result.success("创建房间成功", room);
    }
    
    /**
     * 加入房间
     */
    @PostMapping("/rooms/{roomId}/join")
    public Result<String> joinRoom(@PathVariable String roomId, @RequestParam String userId) {
        log.info("用户 {} 加入房间 {}", userId, roomId);
        
        return Result.success("加入房间成功", "用户 " + userId + " 已成功加入房间 " + roomId);
    }
    
    /**
     * 离开房间
     */
    @PostMapping("/rooms/{roomId}/leave")
    public Result<String> leaveRoom(@PathVariable String roomId, @RequestParam String userId) {
        log.info("用户 {} 离开房间 {}", userId, roomId);
        
        return Result.success("离开房间成功", "用户 " + userId + " 已离开房间 " + roomId);
    }
    
    /**
     * 获取房间统计信息
     */
    @GetMapping("/stats")
    public Result<Object> getHallStats() {
        log.info("获取大厅统计信息");
        
        return Result.success("获取统计信息成功", new Object() {
            public final int totalRooms = 25;
            public final int activeRooms = 18;
            public final int totalPlayers = 156;
            public final int onlinePlayers = 89;
        });
    }
    
    /**
     * 健康检查
     */
    @GetMapping("/health")
    public Result<String> health() {
        return Result.success("大厅服务运行正常");
    }
}
