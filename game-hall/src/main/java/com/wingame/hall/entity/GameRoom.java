package com.wingame.hall.entity;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 游戏房间实体类
 */
@Data
@Document(collection = "game_rooms")
public class GameRoom {
    
    @Id
    private String id;
    
    @Field("room_name")
    private String roomName;
    
    @Field("game_type")
    private String gameType; // 游戏类型
    
    @Field("room_type")
    private Integer roomType; // 房间类型：1-普通房间 2-VIP房间
    
    @Field("max_players")
    private Integer maxPlayers; // 最大玩家数
    
    @Field("current_players")
    private Integer currentPlayers; // 当前玩家数
    
    @Field("min_bet")
    private Long minBet; // 最小下注金额
    
    @Field("max_bet")
    private Long maxBet; // 最大下注金额
    
    @Field("status")
    private Integer status; // 房间状态：0-关闭 1-等待中 2-游戏中
    
    @Field("players")
    private List<String> players; // 玩家ID列表
    
    @Field("create_time")
    private LocalDateTime createTime;
    
    @Field("update_time")
    private LocalDateTime updateTime;
}
