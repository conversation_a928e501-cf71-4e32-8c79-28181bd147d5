package com.wingame.hall;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;

/**
 * 大厅服务启动类
 */
@SpringBootApplication(scanBasePackages = {"com.wingame.hall", "com.wingame.common"})
@EnableDiscoveryClient
@EnableFeignClients
public class HallApplication {
    
    public static void main(String[] args) {
        SpringApplication.run(HallApplication.class, args);
    }
}
