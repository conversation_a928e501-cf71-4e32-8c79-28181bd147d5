#!/bin/bash

echo "=== 停止Java服务脚本 ==="
echo ""

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 停止指定的Java服务
stop_specific_java_services() {
    echo -e "${BLUE}停止WinGame Java服务...${NC}"
    
    # 查找所有game相关的Java进程
    game_pids=$(pgrep -f "game-.*jar" 2>/dev/null)
    
    if [ -n "$game_pids" ]; then
        echo -e "${CYAN}发现运行中的游戏服务:${NC}"
        
        # 显示进程详情
        echo "$game_pids" | while read pid; do
            if [ -n "$pid" ]; then
                cmd=$(ps -p "$pid" -o command= 2>/dev/null | grep -o "game-[^/]*")
                port=$(ps -p "$pid" -o command= 2>/dev/null | grep -o "server.port=[0-9]*" | cut -d= -f2)
                echo "  PID $pid: $cmd ${port:+端口:$port}"
            fi
        done
        
        echo ""
        echo -e "${YELLOW}正在停止游戏服务...${NC}"
        
        # 优雅停止 (SIGTERM)
        echo "发送TERM信号..."
        echo "$game_pids" | xargs kill -TERM 2>/dev/null
        
        # 等待进程停止
        echo "等待进程停止..."
        sleep 5
        
        # 检查是否还有进程运行
        remaining_pids=$(pgrep -f "game-.*jar" 2>/dev/null)
        
        if [ -n "$remaining_pids" ]; then
            echo -e "${YELLOW}强制停止剩余进程...${NC}"
            echo "$remaining_pids" | xargs kill -KILL 2>/dev/null
            sleep 2
        fi
        
        # 最终检查
        final_pids=$(pgrep -f "game-.*jar" 2>/dev/null)
        
        if [ -z "$final_pids" ]; then
            echo -e "${GREEN}✅ 所有游戏服务已停止${NC}"
        else
            echo -e "${RED}❌ 部分服务停止失败${NC}"
            echo "剩余进程: $final_pids"
        fi
    else
        echo -e "${YELLOW}⚠️ 没有发现运行中的游戏服务${NC}"
    fi
    
    echo ""
}

# 停止所有Java进程（危险操作）
stop_all_java_processes() {
    echo -e "${BLUE}检查所有Java进程...${NC}"
    
    # 查找所有Java进程
    java_pids=$(pgrep -f "java.*jar" 2>/dev/null)
    
    if [ -n "$java_pids" ]; then
        echo -e "${CYAN}发现的Java进程:${NC}"
        
        echo "$java_pids" | while read pid; do
            if [ -n "$pid" ]; then
                cmd=$(ps -p "$pid" -o command= 2>/dev/null | head -c 80)
                echo "  PID $pid: $cmd..."
            fi
        done
        
        echo ""
        echo -e "${RED}⚠️ 警告: 这将停止所有Java进程，包括IDE等其他应用！${NC}"
        echo -n "确定要停止所有Java进程吗? (y/N): "
        read -r confirm
        
        if [[ $confirm =~ ^[Yy]$ ]]; then
            echo -e "${YELLOW}停止所有Java进程...${NC}"
            
            # 优雅停止
            echo "$java_pids" | xargs kill -TERM 2>/dev/null
            sleep 3
            
            # 强制停止仍在运行的进程
            remaining_pids=$(pgrep -f "java.*jar" 2>/dev/null)
            if [ -n "$remaining_pids" ]; then
                echo "强制停止剩余进程..."
                echo "$remaining_pids" | xargs kill -KILL 2>/dev/null
            fi
            
            echo -e "${GREEN}✅ 所有Java进程已停止${NC}"
        else
            echo "取消操作"
        fi
    else
        echo -e "${GREEN}✅ 没有发现Java进程${NC}"
    fi
    
    echo ""
}

# 按服务名停止
stop_by_service_name() {
    echo -e "${BLUE}按服务名停止Java服务...${NC}"
    
    services=(
        "game-gateway:网关服务"
        "game-user:用户服务"
        "game-hall:大厅服务"
        "game-agentGame:代理游戏服务"
        "game-activity:活动服务"
        "game-account:账户服务"
    )
    
    echo "可用的服务:"
    for i in "${!services[@]}"; do
        IFS=':' read -r service_name service_desc <<< "${services[$i]}"
        echo "  $((i+1)). $service_desc ($service_name)"
    done
    echo "  7. 全部停止"
    echo "  0. 退出"
    echo ""
    
    echo -n "请选择要停止的服务 (1-7, 0退出): "
    read -r choice
    
    case $choice in
        [1-6])
            index=$((choice-1))
            IFS=':' read -r service_name service_desc <<< "${services[$index]}"
            
            echo -e "${CYAN}停止 $service_desc...${NC}"
            
            pids=$(pgrep -f "$service_name.*jar" 2>/dev/null)
            
            if [ -n "$pids" ]; then
                echo "发现进程: $pids"
                echo "$pids" | xargs kill -TERM 2>/dev/null
                sleep 3
                
                # 检查是否还在运行
                remaining_pids=$(pgrep -f "$service_name.*jar" 2>/dev/null)
                if [ -n "$remaining_pids" ]; then
                    echo "强制停止..."
                    echo "$remaining_pids" | xargs kill -KILL 2>/dev/null
                fi
                
                echo -e "${GREEN}✅ $service_desc 已停止${NC}"
            else
                echo -e "${YELLOW}⚠️ $service_desc 未运行${NC}"
            fi
            ;;
        7)
            stop_specific_java_services
            ;;
        0)
            echo "退出"
            ;;
        *)
            echo -e "${RED}❌ 无效选择${NC}"
            ;;
    esac
    
    echo ""
}

# 检查Java进程状态
check_java_processes() {
    echo -e "${BLUE}检查Java进程状态...${NC}"
    
    # 检查游戏服务
    echo -e "${CYAN}游戏服务状态:${NC}"
    
    services=(
        "game-gateway:8080:网关服务"
        "game-user:8081:用户服务"
        "game-hall:8082:大厅服务"
        "game-agentGame:8083:代理游戏服务"
        "game-activity:8084:活动服务"
        "game-account:8085:账户服务"
    )
    
    running_count=0
    
    for service in "${services[@]}"; do
        IFS=':' read -r service_name port service_desc <<< "$service"
        
        if pgrep -f "$service_name.*jar" > /dev/null 2>&1; then
            pid=$(pgrep -f "$service_name.*jar" 2>/dev/null | head -1)
            echo -e "${GREEN}  ✅ $service_desc (PID: $pid, 端口: $port)${NC}"
            ((running_count++))
        else
            echo -e "${RED}  ❌ $service_desc (未运行)${NC}"
        fi
    done
    
    echo ""
    echo "运行中的游戏服务: $running_count/${#services[@]}"
    
    # 检查其他Java进程
    echo ""
    echo -e "${CYAN}其他Java进程:${NC}"
    
    other_java=$(pgrep -f "java" 2>/dev/null | while read pid; do
        cmd=$(ps -p "$pid" -o command= 2>/dev/null)
        if ! echo "$cmd" | grep -q "game-.*jar"; then
            echo "  PID $pid: $(echo "$cmd" | head -c 60)..."
        fi
    done)
    
    if [ -n "$other_java" ]; then
        echo "$other_java"
    else
        echo "  没有其他Java进程"
    fi
    
    echo ""
}

# 清理端口占用
cleanup_ports() {
    echo -e "${BLUE}清理端口占用...${NC}"
    
    ports=(8080 8081 8082 8083 8084 8085)
    
    occupied_ports=()
    
    for port in "${ports[@]}"; do
        if lsof -i :$port > /dev/null 2>&1; then
            occupied_ports+=($port)
        fi
    done
    
    if [ ${#occupied_ports[@]} -gt 0 ]; then
        echo -e "${YELLOW}发现占用的端口: ${occupied_ports[*]}${NC}"
        
        for port in "${occupied_ports[@]}"; do
            echo ""
            echo "端口 $port 占用情况:"
            lsof -i :$port | head -3
            
            pids=$(lsof -ti :$port 2>/dev/null)
            if [ -n "$pids" ]; then
                echo -n "是否释放端口 $port? (y/N): "
                read -r confirm
                
                if [[ $confirm =~ ^[Yy]$ ]]; then
                    echo "释放端口 $port..."
                    echo "$pids" | xargs kill -TERM 2>/dev/null
                    sleep 2
                    
                    # 检查是否还被占用
                    if lsof -i :$port > /dev/null 2>&1; then
                        echo "强制释放..."
                        lsof -ti :$port 2>/dev/null | xargs kill -KILL 2>/dev/null
                    fi
                    
                    echo -e "${GREEN}✅ 端口 $port 已释放${NC}"
                fi
            fi
        done
    else
        echo -e "${GREEN}✅ 没有发现端口占用${NC}"
    fi
    
    echo ""
}

# 显示菜单
show_menu() {
    echo -e "${CYAN}请选择操作:${NC}"
    echo "1. 停止所有游戏服务"
    echo "2. 按服务名停止"
    echo "3. 停止所有Java进程 (危险)"
    echo "4. 检查Java进程状态"
    echo "5. 清理端口占用"
    echo "0. 退出"
    echo ""
}

# 主函数
main() {
    while true; do
        show_menu
        echo -n "请选择 (0-5): "
        read -r choice
        
        echo ""
        
        case $choice in
            1)
                stop_specific_java_services
                ;;
            2)
                stop_by_service_name
                ;;
            3)
                stop_all_java_processes
                ;;
            4)
                check_java_processes
                ;;
            5)
                cleanup_ports
                ;;
            0)
                echo -e "${GREEN}退出Java服务管理脚本${NC}"
                break
                ;;
            *)
                echo -e "${RED}❌ 无效选择，请重新输入${NC}"
                echo ""
                ;;
        esac
        
        if [ "$choice" != "0" ]; then
            echo -n "按回车键继续..."
            read -r
            echo ""
        fi
    done
}

# 如果有命令行参数，直接执行对应操作
if [ $# -gt 0 ]; then
    case $1 in
        "stop")
            stop_specific_java_services
            ;;
        "all")
            stop_all_java_processes
            ;;
        "status")
            check_java_processes
            ;;
        "ports")
            cleanup_ports
            ;;
        *)
            echo "用法: $0 [stop|all|status|ports]"
            echo "  stop   - 停止游戏服务"
            echo "  all    - 停止所有Java进程"
            echo "  status - 检查进程状态"
            echo "  ports  - 清理端口占用"
            ;;
    esac
else
    main
fi
