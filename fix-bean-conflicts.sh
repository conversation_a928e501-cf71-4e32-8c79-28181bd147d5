#!/bin/bash

echo "=== Bean冲突修复脚本 ==="
echo ""

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 检查Bean冲突
check_bean_conflicts() {
    echo -e "${BLUE}检查潜在的Bean冲突...${NC}"
    
    # 查找所有@Service、@Component、@Bean注解
    echo -e "${CYAN}扫描所有Bean定义...${NC}"
    
    services=("game-user" "game-hall" "game-agentGame" "game-activity" "game-account" "game-gateway")
    
    for service in "${services[@]}"; do
        if [ -d "$service/src/main/java" ]; then
            echo -e "${YELLOW}检查 $service 服务...${NC}"
            
            # 查找@Service注解
            service_beans=$(find "$service/src/main/java" -name "*.java" -exec grep -l "@Service" {} \; 2>/dev/null)
            if [ -n "$service_beans" ]; then
                echo "  @Service beans:"
                echo "$service_beans" | sed 's/^/    /'
            fi
            
            # 查找@Component注解
            component_beans=$(find "$service/src/main/java" -name "*.java" -exec grep -l "@Component" {} \; 2>/dev/null)
            if [ -n "$component_beans" ]; then
                echo "  @Component beans:"
                echo "$component_beans" | sed 's/^/    /'
            fi
            
            # 查找@Bean注解
            bean_methods=$(find "$service/src/main/java" -name "*.java" -exec grep -l "@Bean" {} \; 2>/dev/null)
            if [ -n "$bean_methods" ]; then
                echo "  @Bean methods:"
                echo "$bean_methods" | sed 's/^/    /'
            fi
            
            echo ""
        fi
    done
}

# 检查重复的接口实现
check_duplicate_implementations() {
    echo -e "${BLUE}检查重复的接口实现...${NC}"
    
    # 查找Feign客户端
    echo -e "${CYAN}检查Feign客户端...${NC}"
    find . -name "*.java" -exec grep -l "@FeignClient" {} \; 2>/dev/null | while read file; do
        echo "  Feign客户端: $file"
        
        # 检查fallback配置
        fallback=$(grep -o "fallback = [^.]*\.class" "$file" 2>/dev/null || true)
        if [ -n "$fallback" ]; then
            echo "    Fallback: $fallback"
        fi
    done
    
    echo ""
    
    # 查找Service实现
    echo -e "${CYAN}检查Service实现...${NC}"
    find . -name "*ServiceImpl.java" 2>/dev/null | while read file; do
        echo "  Service实现: $file"
        
        # 检查实现的接口
        interface=$(grep -o "implements [A-Za-z]*Service" "$file" 2>/dev/null || true)
        if [ -n "$interface" ]; then
            echo "    实现接口: $interface"
        fi
    done
    
    echo ""
}

# 修复常见的Bean冲突
fix_common_conflicts() {
    echo -e "${BLUE}修复常见的Bean冲突...${NC}"
    
    # 1. 检查是否有重复的Feign客户端
    echo -e "${CYAN}检查Feign客户端冲突...${NC}"
    
    feign_clients=$(find . -name "*FeignClient.java" -o -name "*ServiceClient.java" 2>/dev/null)
    
    if [ -n "$feign_clients" ]; then
        echo "发现的Feign客户端:"
        echo "$feign_clients" | sed 's/^/  /'
        
        # 检查是否有重复的服务名
        echo ""
        echo "检查服务名冲突..."
        
        for client in $feign_clients; do
            service_name=$(grep -o 'name = "[^"]*"' "$client" 2>/dev/null || true)
            if [ -n "$service_name" ]; then
                echo "  $client: $service_name"
            fi
        done
    fi
    
    echo ""
    
    # 2. 检查RedisTemplate Bean冲突
    echo -e "${CYAN}检查RedisTemplate配置...${NC}"
    
    redis_configs=$(find . -name "*RedisConfig.java" 2>/dev/null)
    
    if [ -n "$redis_configs" ]; then
        echo "发现的Redis配置:"
        echo "$redis_configs" | sed 's/^/  /'
        
        # 检查是否有重复的Bean名称
        for config in $redis_configs; do
            bean_names=$(grep -o '@Bean[^(]*([^)]*)' "$config" 2>/dev/null || true)
            if [ -n "$bean_names" ]; then
                echo "  $config Bean方法:"
                echo "$bean_names" | sed 's/^/    /'
            fi
        done
    fi
    
    echo ""
}

# 添加Bean名称限定符
add_bean_qualifiers() {
    echo -e "${BLUE}建议添加Bean名称限定符...${NC}"
    
    echo -e "${YELLOW}对于重复的Bean，建议使用以下方式解决:${NC}"
    echo ""
    echo "1. 使用@Qualifier注解:"
    echo "   @Autowired"
    echo "   @Qualifier(\"specificBeanName\")"
    echo "   private SomeService someService;"
    echo ""
    echo "2. 使用@Primary注解标记主要Bean:"
    echo "   @Service"
    echo "   @Primary"
    echo "   public class PrimaryServiceImpl implements SomeService {"
    echo ""
    echo "3. 使用@Bean的name属性:"
    echo "   @Bean(name = \"customBeanName\")"
    echo "   public SomeBean someBean() {"
    echo ""
    echo "4. 启用Bean覆盖（不推荐）:"
    echo "   spring.main.allow-bean-definition-overriding=true"
    echo ""
}

# 生成Bean冲突报告
generate_conflict_report() {
    echo -e "${BLUE}生成Bean冲突报告...${NC}"
    
    report_file="bean_conflict_report.txt"
    
    cat > "$report_file" << EOF
# Bean冲突检查报告
生成时间: $(date)

## 扫描的服务
EOF
    
    services=("game-user" "game-hall" "game-agentGame" "game-activity" "game-account" "game-gateway")
    
    for service in "${services[@]}"; do
        if [ -d "$service" ]; then
            echo "- $service" >> "$report_file"
        fi
    done
    
    echo "" >> "$report_file"
    echo "## Bean定义统计" >> "$report_file"
    
    # 统计各种Bean类型
    service_count=$(find . -name "*.java" -exec grep -l "@Service" {} \; 2>/dev/null | wc -l)
    component_count=$(find . -name "*.java" -exec grep -l "@Component" {} \; 2>/dev/null | wc -l)
    feign_count=$(find . -name "*.java" -exec grep -l "@FeignClient" {} \; 2>/dev/null | wc -l)
    config_count=$(find . -name "*.java" -exec grep -l "@Configuration" {} \; 2>/dev/null | wc -l)
    
    echo "- @Service: $service_count" >> "$report_file"
    echo "- @Component: $component_count" >> "$report_file"
    echo "- @FeignClient: $feign_count" >> "$report_file"
    echo "- @Configuration: $config_count" >> "$report_file"
    
    echo "" >> "$report_file"
    echo "## 详细Bean列表" >> "$report_file"
    
    # 详细列出所有Bean
    find . -name "*.java" -exec grep -l "@Service\|@Component\|@FeignClient" {} \; 2>/dev/null | while read file; do
        echo "- $file" >> "$report_file"
        
        # 提取类名
        class_name=$(basename "$file" .java)
        echo "  类名: $class_name" >> "$report_file"
        
        # 提取注解
        annotations=$(grep -o "@[A-Za-z]*" "$file" 2>/dev/null | head -5 | tr '\n' ' ')
        echo "  注解: $annotations" >> "$report_file"
        echo "" >> "$report_file"
    done
    
    echo -e "${GREEN}✅ 报告已生成: $report_file${NC}"
}

# 测试Bean加载
test_bean_loading() {
    echo -e "${BLUE}测试Bean加载...${NC}"
    
    echo -e "${CYAN}编译项目...${NC}"
    if mvn clean compile -q; then
        echo -e "${GREEN}✅ 编译成功，没有Bean冲突${NC}"
    else
        echo -e "${RED}❌ 编译失败，可能存在Bean冲突${NC}"
        echo ""
        echo -e "${YELLOW}建议解决方案:${NC}"
        echo "1. 检查重复的Bean定义"
        echo "2. 使用@Qualifier注解"
        echo "3. 使用@Primary注解"
        echo "4. 重命名冲突的Bean"
        return 1
    fi
    
    echo ""
    echo -e "${CYAN}尝试启动应用上下文...${NC}"
    
    # 创建简单的测试类来验证Bean加载
    test_class="BeanLoadingTest.java"
    
    cat > "$test_class" << 'EOF'
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.ConfigurableApplicationContext;

@SpringBootApplication
public class BeanLoadingTest {
    public static void main(String[] args) {
        try {
            ConfigurableApplicationContext context = SpringApplication.run(BeanLoadingTest.class, args);
            System.out.println("✅ Bean加载测试成功");
            context.close();
        } catch (Exception e) {
            System.err.println("❌ Bean加载测试失败: " + e.getMessage());
            System.exit(1);
        }
    }
}
EOF
    
    # 编译并运行测试
    if javac -cp "$(mvn dependency:build-classpath -q -Dmdep.outputFile=/dev/stdout):target/classes" "$test_class" 2>/dev/null; then
        echo -e "${GREEN}✅ Bean加载测试编译成功${NC}"
        rm -f "$test_class" BeanLoadingTest.class
    else
        echo -e "${YELLOW}⚠️ 无法运行Bean加载测试${NC}"
        rm -f "$test_class"
    fi
}

# 主函数
main() {
    echo -e "${CYAN}开始Bean冲突检查和修复...${NC}"
    echo ""
    
    # 1. 检查Bean冲突
    check_bean_conflicts
    
    # 2. 检查重复实现
    check_duplicate_implementations
    
    # 3. 修复常见冲突
    fix_common_conflicts
    
    # 4. 添加限定符建议
    add_bean_qualifiers
    
    # 5. 生成报告
    generate_conflict_report
    
    # 6. 测试Bean加载
    test_bean_loading
    
    echo ""
    echo -e "${GREEN}🎉 Bean冲突检查完成！${NC}"
    echo ""
    echo -e "${BLUE}如果仍有Bean冲突，请检查:${NC}"
    echo "1. 重复的@Service或@Component类"
    echo "2. 相同名称的@Bean方法"
    echo "3. 多个实现同一接口的类"
    echo "4. Feign客户端的fallback配置"
    echo ""
    echo -e "${BLUE}解决方案:${NC}"
    echo "• 使用@Qualifier指定具体Bean"
    echo "• 使用@Primary标记主要Bean"
    echo "• 重命名冲突的Bean"
    echo "• 删除重复的类"
}

# 运行主函数
main
