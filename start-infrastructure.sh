#!/bin/bash

echo "启动基础设施服务..."

# 检测系统架构
ARCH=$(uname -m)
echo "检测到系统架构: $ARCH"

# 根据架构选择合适的docker-compose文件
if [[ "$ARCH" == "arm64" ]] || [[ "$ARCH" == "aarch64" ]]; then
    echo "使用ARM64兼容配置..."
    COMPOSE_FILE="docker-compose-arm64.yml"
else
    echo "使用标准配置..."
    COMPOSE_FILE="docker-compose.yml"
fi

# 检查Docker是否运行
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker未运行，请先启动Docker"
    exit 1
fi

echo "使用配置文件: $COMPOSE_FILE"

# 启动基础设施
if docker-compose -f "$COMPOSE_FILE" up -d; then
    echo "✅ 基础设施启动成功"
else
    echo "❌ 基础设施启动失败，尝试使用备用配置..."
    if [[ "$COMPOSE_FILE" == "docker-compose.yml" ]]; then
        echo "尝试使用ARM64配置..."
        docker-compose -f "docker-compose-arm64.yml" up -d
    else
        echo "尝试使用标准配置..."
        docker-compose -f "docker-compose.yml" up -d
    fi
fi

echo "等待服务启动..."
sleep 30

echo "检查服务状态..."
docker-compose -f "$COMPOSE_FILE" ps

echo ""
echo "🎉 基础设施服务启动完成！"
echo ""
echo "📋 服务访问信息:"
echo "• Nacos控制台: http://localhost:8848/nacos"
echo "  用户名/密码: nacos/nacos"
echo "• MongoDB: localhost:27017"
echo "  用户名/密码: admin/admin123"
echo "• Redis: localhost:6379"
echo "  密码: redis123"
echo ""
echo "💡 提示:"
echo "• 如果Nacos启动失败，请等待1-2分钟后重试"
echo "• 可以使用 'docker-compose -f $COMPOSE_FILE logs nacos' 查看Nacos日志"
