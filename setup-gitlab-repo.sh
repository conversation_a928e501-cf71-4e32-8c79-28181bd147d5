#!/bin/bash

echo "=== WinGame项目GitLab版本管理配置 ==="
echo ""

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# GitLab仓库信息
GITLAB_URL="https://gitlab.ttgamelab.com/daniel/wingame-saas-server-new.git"
PROJECT_NAME="wingame-saas-server-new"

# 检查Git配置
check_git_config() {
    echo -e "${BLUE}检查Git配置...${NC}"
    
    # 检查Git是否安装
    if ! command -v git &> /dev/null; then
        echo -e "${RED}❌ Git未安装，请先安装Git${NC}"
        return 1
    fi
    
    # 检查Git用户配置
    git_user=$(git config --global user.name 2>/dev/null)
    git_email=$(git config --global user.email 2>/dev/null)
    
    if [ -z "$git_user" ] || [ -z "$git_email" ]; then
        echo -e "${YELLOW}⚠️ Git用户信息未配置${NC}"
        echo -n "请输入Git用户名: "
        read -r user_name
        echo -n "请输入Git邮箱: "
        read -r user_email
        
        git config --global user.name "$user_name"
        git config --global user.email "$user_email"
        
        echo -e "${GREEN}✅ Git用户信息已配置${NC}"
    else
        echo -e "${GREEN}✅ Git用户信息: $git_user <$git_email>${NC}"
    fi
    
    return 0
}

# 创建.gitignore文件
create_gitignore() {
    echo -e "${BLUE}创建.gitignore文件...${NC}"
    
    cat > .gitignore << 'EOF'
# Maven
target/
pom.xml.tag
pom.xml.releaseBackup
pom.xml.versionsBackup
pom.xml.next
release.properties
dependency-reduced-pom.xml
buildNumber.properties
.mvn/timing.properties
.mvn/wrapper/maven-wrapper.jar

# IDE
.idea/
*.iws
*.iml
*.ipr
.vscode/
.settings/
.project
.classpath

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs/
*.log
*.log.*

# Runtime
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# nyc test coverage
.nyc_output

# Dependency directories
node_modules/

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env

# Application specific
application-local.yml
application-dev.yml
application-prod.yml

# Backup files
*.backup
*.bak
*.tmp

# Docker
.docker/

# Kubernetes
k8s/secrets/

# Temporary files
temp/
tmp/
EOF
    
    echo -e "${GREEN}✅ .gitignore文件已创建${NC}"
}

# 检查项目文件
check_project_files() {
    echo -e "${BLUE}检查项目文件...${NC}"
    
    # 检查关键文件
    key_files=("pom.xml" "README.md")
    
    for file in "${key_files[@]}"; do
        if [ -f "$file" ]; then
            echo -e "${GREEN}✅ $file 存在${NC}"
        else
            echo -e "${RED}❌ $file 不存在${NC}"
        fi
    done
    
    # 检查微服务模块
    services=("game-gateway" "game-user" "game-hall" "game-agentGame" "game-activity" "game-account")
    
    echo -e "${CYAN}微服务模块:${NC}"
    for service in "${services[@]}"; do
        if [ -d "$service" ]; then
            echo -e "${GREEN}✅ $service${NC}"
        else
            echo -e "${RED}❌ $service${NC}"
        fi
    done
    
    # 检查脚本文件
    scripts=("start-infrastructure.sh" "start-services.sh" "gateway-aggregation-demo.sh")
    
    echo -e "${CYAN}脚本文件:${NC}"
    for script in "${scripts[@]}"; do
        if [ -f "$script" ]; then
            echo -e "${GREEN}✅ $script${NC}"
        else
            echo -e "${RED}❌ $script${NC}"
        fi
    done
    
    echo ""
}

# 清理不需要的文件
cleanup_files() {
    echo -e "${BLUE}清理不需要的文件...${NC}"
    
    # 清理日志文件
    if [ -d "logs" ]; then
        echo -e "${CYAN}清理日志文件...${NC}"
        rm -rf logs/*.log
        echo -e "${GREEN}✅ 日志文件已清理${NC}"
    fi
    
    # 清理Maven target目录
    echo -e "${CYAN}清理Maven编译文件...${NC}"
    find . -name "target" -type d -exec rm -rf {} + 2>/dev/null || true
    echo -e "${GREEN}✅ Maven编译文件已清理${NC}"
    
    # 清理IDE文件
    if [ -d ".idea" ]; then
        echo -e "${CYAN}清理IDE配置文件...${NC}"
        rm -rf .idea/workspace.xml .idea/tasks.xml .idea/usage.statistics.xml .idea/shelf/
        echo -e "${GREEN}✅ IDE配置文件已清理${NC}"
    fi
    
    echo ""
}

# 初始化Git仓库
init_git_repo() {
    echo -e "${BLUE}初始化Git仓库...${NC}"
    
    # 检查是否已经是Git仓库
    if [ -d ".git" ]; then
        echo -e "${YELLOW}⚠️ 已存在Git仓库${NC}"
        
        # 检查是否有远程仓库
        if git remote get-url origin &>/dev/null; then
            current_remote=$(git remote get-url origin)
            echo -e "${YELLOW}当前远程仓库: $current_remote${NC}"
            
            if [ "$current_remote" != "$GITLAB_URL" ]; then
                echo -n "是否更换远程仓库为GitLab? (y/N): "
                read -r confirm
                
                if [[ $confirm =~ ^[Yy]$ ]]; then
                    git remote set-url origin "$GITLAB_URL"
                    echo -e "${GREEN}✅ 远程仓库已更新${NC}"
                fi
            else
                echo -e "${GREEN}✅ 远程仓库已正确配置${NC}"
            fi
        else
            git remote add origin "$GITLAB_URL"
            echo -e "${GREEN}✅ 远程仓库已添加${NC}"
        fi
    else
        git init
        git remote add origin "$GITLAB_URL"
        echo -e "${GREEN}✅ Git仓库已初始化${NC}"
    fi
    
    echo ""
}

# 添加文件到Git
add_files_to_git() {
    echo -e "${BLUE}添加文件到Git...${NC}"
    
    # 添加所有文件
    git add .
    
    # 显示状态
    echo -e "${CYAN}Git状态:${NC}"
    git status --short
    
    echo ""
}

# 创建初始提交
create_initial_commit() {
    echo -e "${BLUE}创建初始提交...${NC}"
    
    # 检查是否有提交
    if git rev-parse --verify HEAD &>/dev/null; then
        echo -e "${YELLOW}⚠️ 已存在提交历史${NC}"
        echo -n "是否创建新的提交? (y/N): "
        read -r confirm
        
        if [[ ! $confirm =~ ^[Yy]$ ]]; then
            echo "跳过提交"
            return
        fi
    fi
    
    # 创建提交
    commit_message="feat: 初始化WinGame微服务项目

- 添加Spring Cloud Gateway网关服务
- 添加用户服务(game-user)，支持聚合功能
- 添加账户服务(game-account)
- 添加大厅服务(game-hall)
- 添加活动服务(game-activity)
- 添加代理游戏服务(game-agentGame)
- 配置Nacos服务注册与发现
- 实现Feign服务间调用
- 添加LoadBalancer负载均衡
- 实现网关层数据聚合
- 添加熔断降级处理
- 配置Docker基础设施
- 添加演示脚本和工具"
    
    git commit -m "$commit_message"
    
    echo -e "${GREEN}✅ 初始提交已创建${NC}"
    echo ""
}

# 推送到GitLab
push_to_gitlab() {
    echo -e "${BLUE}推送到GitLab...${NC}"
    
    # 检查远程仓库连接
    echo -e "${CYAN}测试GitLab连接...${NC}"
    
    if git ls-remote origin &>/dev/null; then
        echo -e "${GREEN}✅ GitLab连接正常${NC}"
    else
        echo -e "${RED}❌ 无法连接到GitLab${NC}"
        echo -e "${YELLOW}请检查:${NC}"
        echo "1. 网络连接"
        echo "2. GitLab访问权限"
        echo "3. SSH密钥配置"
        return 1
    fi
    
    # 推送代码
    echo -e "${CYAN}推送代码到GitLab...${NC}"
    
    # 设置上游分支并推送
    if git push -u origin main; then
        echo -e "${GREEN}✅ 代码已成功推送到GitLab${NC}"
    else
        echo -e "${RED}❌ 推送失败${NC}"
        echo -e "${YELLOW}可能的解决方案:${NC}"
        echo "1. 检查GitLab仓库是否存在"
        echo "2. 确认有推送权限"
        echo "3. 检查分支名称是否正确"
        return 1
    fi
    
    echo ""
}

# 创建分支策略
setup_branch_strategy() {
    echo -e "${BLUE}设置分支策略...${NC}"
    
    # 创建开发分支
    echo -e "${CYAN}创建develop分支...${NC}"
    git checkout -b develop
    git push -u origin develop
    
    # 切换回主分支
    git checkout main
    
    echo -e "${GREEN}✅ 分支策略已设置${NC}"
    echo -e "${CYAN}分支说明:${NC}"
    echo "• main: 主分支，用于生产环境"
    echo "• develop: 开发分支，用于日常开发"
    echo ""
}

# 生成项目文档
generate_project_docs() {
    echo -e "${BLUE}生成项目文档...${NC}"
    
    # 更新README.md
    cat >> README.md << 'EOF'

## Git工作流程

### 分支策略
- `main`: 主分支，用于生产环境
- `develop`: 开发分支，用于日常开发
- `feature/*`: 功能分支
- `hotfix/*`: 热修复分支

### 提交规范
```
feat: 新功能
fix: 修复bug
docs: 文档更新
style: 代码格式调整
refactor: 代码重构
test: 测试相关
chore: 构建过程或辅助工具的变动
```

### 开发流程
1. 从develop分支创建feature分支
2. 在feature分支上开发
3. 提交代码并推送到远程
4. 创建Merge Request到develop分支
5. 代码审查通过后合并
6. 定期将develop合并到main

## 部署说明

### 本地开发环境
```bash
# 启动基础设施
./start-infrastructure.sh

# 启动微服务
./start-services.sh

# 运行演示
./gateway-aggregation-demo.sh
```

### 生产环境部署
详见部署文档...

EOF
    
    echo -e "${GREEN}✅ 项目文档已更新${NC}"
    echo ""
}

# 显示GitLab信息
show_gitlab_info() {
    echo -e "${GREEN}🎉 GitLab版本管理配置完成！${NC}"
    echo ""
    echo -e "${BLUE}GitLab仓库信息:${NC}"
    echo "• 仓库地址: $GITLAB_URL"
    echo "• 项目名称: $PROJECT_NAME"
    echo "• 主分支: main"
    echo "• 开发分支: develop"
    echo ""
    echo -e "${BLUE}常用Git命令:${NC}"
    echo "• 查看状态: git status"
    echo "• 添加文件: git add ."
    echo "• 提交代码: git commit -m \"提交信息\""
    echo "• 推送代码: git push"
    echo "• 拉取代码: git pull"
    echo "• 切换分支: git checkout <分支名>"
    echo "• 创建分支: git checkout -b <新分支名>"
    echo ""
    echo -e "${BLUE}下一步操作:${NC}"
    echo "1. 访问GitLab查看项目: $GITLAB_URL"
    echo "2. 配置CI/CD流水线"
    echo "3. 设置分支保护规则"
    echo "4. 邀请团队成员"
    echo ""
}

# 主函数
main() {
    echo -e "${CYAN}开始配置GitLab版本管理...${NC}"
    echo ""
    
    # 1. 检查Git配置
    if ! check_git_config; then
        echo -e "${RED}❌ Git配置检查失败${NC}"
        exit 1
    fi
    
    echo ""
    
    # 2. 创建.gitignore
    create_gitignore
    
    echo ""
    
    # 3. 检查项目文件
    check_project_files
    
    # 4. 清理文件
    cleanup_files
    
    # 5. 初始化Git仓库
    init_git_repo
    
    # 6. 添加文件
    add_files_to_git
    
    # 7. 创建初始提交
    create_initial_commit
    
    # 8. 推送到GitLab
    if push_to_gitlab; then
        # 9. 设置分支策略
        setup_branch_strategy
        
        # 10. 生成项目文档
        generate_project_docs
        
        # 11. 显示GitLab信息
        show_gitlab_info
    else
        echo -e "${RED}❌ GitLab配置未完成${NC}"
        exit 1
    fi
}

# 运行主函数
main
