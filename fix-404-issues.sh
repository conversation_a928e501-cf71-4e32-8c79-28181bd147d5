#!/bin/bash

echo "=== 404问题诊断和修复脚本 ==="
echo ""

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 检查服务状态
check_services_status() {
    echo -e "${BLUE}检查服务状态...${NC}"
    
    services=(
        "网关服务:8080"
        "用户服务:8081"
        "大厅服务:8082"
        "代理游戏服务:8083"
        "活动服务:8084"
        "账户服务:8085"
    )
    
    for service in "${services[@]}"; do
        IFS=':' read -r name port <<< "$service"
        
        if curl -s "http://localhost:$port/actuator/health" > /dev/null 2>&1; then
            echo -e "${GREEN}✅ $name (端口$port) 运行正常${NC}"
        else
            echo -e "${RED}❌ $name (端口$port) 无法访问${NC}"
        fi
    done
    
    echo ""
}

# 检查网关路由配置
check_gateway_routes() {
    echo -e "${BLUE}检查网关路由配置...${NC}"
    
    # 检查网关是否可访问
    if ! curl -s http://localhost:8080/actuator/health > /dev/null 2>&1; then
        echo -e "${RED}❌ 网关服务不可访问${NC}"
        return 1
    fi
    
    # 获取路由信息
    routes_response=$(curl -s "http://localhost:8080/actuator/gateway/routes")
    
    if [ -z "$routes_response" ] || [ "$routes_response" = "[]" ]; then
        echo -e "${RED}❌ 网关没有配置任何路由${NC}"
        echo -e "${YELLOW}可能原因:${NC}"
        echo "  1. 网关配置文件中的gateway部分被注释"
        echo "  2. 路由配置语法错误"
        echo "  3. 服务发现配置问题"
        return 1
    else
        route_count=$(echo "$routes_response" | grep -o '"route_id"' | wc -l)
        echo -e "${GREEN}✅ 网关配置了 $route_count 个路由${NC}"
        
        # 显示路由详情
        echo -e "${CYAN}路由详情:${NC}"
        echo "$routes_response" | python3 -m json.tool 2>/dev/null | grep -E '"route_id"|"uri"|"predicate"' | head -20
    fi
    
    echo ""
}

# 测试具体的API路径
test_api_paths() {
    echo -e "${BLUE}测试API路径...${NC}"
    
    # 测试路径列表
    test_paths=(
        "网关健康检查:http://localhost:8080/actuator/health"
        "用户服务健康检查:http://localhost:8081/actuator/health"
        "用户聚合健康检查(直接):http://localhost:8081/api/user/aggregation/health"
        "用户聚合健康检查(网关):http://localhost:8080/user/api/user/aggregation/health"
        "用户档案(网关):http://localhost:8080/user/api/user/aggregation/profile/test_user"
        "账户余额(直接):http://localhost:8085/api/account/test_user/balance"
        "账户余额(网关):http://localhost:8080/account/api/account/test_user/balance"
    )
    
    for test_path in "${test_paths[@]}"; do
        IFS=':' read -r description url <<< "$test_path"
        
        echo -e "${CYAN}测试: $description${NC}"
        echo "  URL: $url"
        
        response=$(curl -s -w "HTTP_CODE:%{http_code}" "$url")
        http_code=$(echo "$response" | grep "HTTP_CODE:" | cut -d: -f2)
        body=$(echo "$response" | sed '/HTTP_CODE:/d')
        
        if [ "$http_code" = "200" ]; then
            echo -e "${GREEN}  ✅ 成功 (HTTP $http_code)${NC}"
        elif [ "$http_code" = "404" ]; then
            echo -e "${RED}  ❌ 404 Not Found${NC}"
        else
            echo -e "${YELLOW}  ⚠️ HTTP $http_code${NC}"
        fi
        
        echo ""
    done
}

# 检查网关配置文件
check_gateway_config() {
    echo -e "${BLUE}检查网关配置文件...${NC}"
    
    config_file="game-gateway/src/main/resources/application.yml"
    
    if [ ! -f "$config_file" ]; then
        echo -e "${RED}❌ 网关配置文件不存在${NC}"
        return 1
    fi
    
    echo -e "${CYAN}检查关键配置项:${NC}"
    
    # 检查gateway配置
    if grep -q "^[[:space:]]*gateway:" "$config_file"; then
        echo -e "${GREEN}✅ gateway配置存在${NC}"
    else
        echo -e "${RED}❌ gateway配置缺失或被注释${NC}"
    fi
    
    # 检查routes配置
    if grep -q "routes:" "$config_file"; then
        echo -e "${GREEN}✅ routes配置存在${NC}"
        
        # 统计路由数量
        route_count=$(grep -A 20 "routes:" "$config_file" | grep -c "- id:")
        echo "  配置的路由数量: $route_count"
    else
        echo -e "${RED}❌ routes配置缺失${NC}"
    fi
    
    # 检查discovery配置
    if grep -q "discovery:" "$config_file"; then
        echo -e "${GREEN}✅ discovery配置存在${NC}"
        
        # 检查locator是否启用
        if grep -A 5 "discovery:" "$config_file" | grep -q "enabled: true"; then
            echo -e "${GREEN}  ✅ 服务发现已启用${NC}"
        else
            echo -e "${YELLOW}  ⚠️ 服务发现可能未启用${NC}"
        fi
    else
        echo -e "${YELLOW}⚠️ discovery配置缺失${NC}"
    fi
    
    echo ""
}

# 检查服务注册情况
check_service_registration() {
    echo -e "${BLUE}检查服务注册情况...${NC}"
    
    # 检查Nacos服务列表
    services_response=$(curl -s "http://localhost:8848/nacos/v1/ns/service/list?pageNo=1&pageSize=20")
    
    if [ -z "$services_response" ]; then
        echo -e "${RED}❌ 无法连接到Nacos${NC}"
        return 1
    fi
    
    service_count=$(echo "$services_response" | grep -o '"count":[0-9]*' | cut -d: -f2)
    echo "注册的服务数量: $service_count"
    
    if [ "$service_count" -gt 0 ]; then
        echo -e "${GREEN}已注册的服务:${NC}"
        echo "$services_response" | grep -o '"doms":\[[^]]*\]' | sed 's/"doms":\[//;s/\]//;s/"//g' | tr ',' '\n' | sed 's/^/  - /'
    else
        echo -e "${RED}❌ 没有服务注册到Nacos${NC}"
    fi
    
    echo ""
}

# 修复常见404问题
fix_common_404_issues() {
    echo -e "${BLUE}修复常见404问题...${NC}"
    
    echo -n "是否尝试修复网关配置问题? (y/N): "
    read -r confirm
    
    if [[ ! $confirm =~ ^[Yy]$ ]]; then
        echo "跳过修复"
        return
    fi
    
    config_file="game-gateway/src/main/resources/application.yml"
    
    # 备份配置文件
    cp "$config_file" "$config_file.backup"
    echo -e "${CYAN}已备份配置文件: $config_file.backup${NC}"
    
    # 修复gateway配置被注释的问题
    if grep -q "^[[:space:]]*#[[:space:]]*gateway:" "$config_file"; then
        echo -e "${CYAN}修复gateway配置被注释的问题...${NC}"
        sed -i.tmp 's/^[[:space:]]*#[[:space:]]*gateway:/    gateway:/' "$config_file"
        sed -i.tmp 's/^[[:space:]]*#[[:space:]]*discovery:/      discovery:/' "$config_file"
        sed -i.tmp 's/^[[:space:]]*#[[:space:]]*locator:/        locator:/' "$config_file"
        sed -i.tmp 's/^[[:space:]]*#[[:space:]]*enabled: true/          enabled: true/' "$config_file"
        sed -i.tmp 's/^[[:space:]]*#[[:space:]]*lower-case-service-id: true/          lower-case-service-id: true/' "$config_file"
        rm -f "$config_file.tmp"
        echo -e "${GREEN}✅ gateway配置已修复${NC}"
    fi
    
    # 重新启动网关服务
    echo -e "${CYAN}重新启动网关服务...${NC}"
    
    # 停止现有网关
    pkill -f "game-gateway.*jar" 2>/dev/null || true
    sleep 3
    
    # 重新编译和启动
    cd game-gateway
    if mvn clean package -DskipTests -q; then
        echo -e "${GREEN}✅ 网关编译成功${NC}"
        
        nohup java -jar target/game-gateway-1.0-SNAPSHOT.jar > ../logs/gateway.log 2>&1 &
        gateway_pid=$!
        echo "网关启动中，PID: $gateway_pid"
        
        cd ..
        
        # 等待启动
        echo -e "${CYAN}等待网关启动...${NC}"
        for i in {1..30}; do
            if curl -s http://localhost:8080/actuator/health > /dev/null 2>&1; then
                echo -e "${GREEN}✅ 网关启动成功${NC}"
                break
            else
                echo -n "."
                sleep 2
            fi
            
            if [ $i -eq 30 ]; then
                echo ""
                echo -e "${RED}❌ 网关启动超时${NC}"
                return 1
            fi
        done
        
        echo ""
    else
        echo -e "${RED}❌ 网关编译失败${NC}"
        cd ..
        return 1
    fi
}

# 测试修复结果
test_fix_results() {
    echo -e "${BLUE}测试修复结果...${NC}"
    
    # 等待一下让网关完全启动
    sleep 10
    
    # 测试关键API
    test_apis=(
        "网关健康检查:http://localhost:8080/actuator/health"
        "网关路由信息:http://localhost:8080/actuator/gateway/routes"
        "用户聚合健康检查:http://localhost:8080/user/api/user/aggregation/health"
        "用户档案:http://localhost:8080/user/api/user/aggregation/profile/test_user"
    )
    
    success_count=0
    
    for test_api in "${test_apis[@]}"; do
        IFS=':' read -r description url <<< "$test_api"
        
        response=$(curl -s -w "HTTP_CODE:%{http_code}" "$url")
        http_code=$(echo "$response" | grep "HTTP_CODE:" | cut -d: -f2)
        
        if [ "$http_code" = "200" ]; then
            echo -e "${GREEN}✅ $description${NC}"
            ((success_count++))
        else
            echo -e "${RED}❌ $description (HTTP $http_code)${NC}"
        fi
    done
    
    echo ""
    echo "测试结果: $success_count/${#test_apis[@]} 个API正常"
    
    if [ $success_count -eq ${#test_apis[@]} ]; then
        echo -e "${GREEN}🎉 所有API测试通过！${NC}"
        return 0
    else
        echo -e "${YELLOW}⚠️ 部分API仍有问题${NC}"
        return 1
    fi
}

# 生成404问题报告
generate_404_report() {
    echo -e "${BLUE}生成404问题报告...${NC}"
    
    report_file="404_diagnosis_report.txt"
    
    cat > "$report_file" << EOF
# 404问题诊断报告
生成时间: $(date)

## 服务状态检查
EOF
    
    # 检查服务状态
    services=(
        "网关服务:8080"
        "用户服务:8081"
        "账户服务:8085"
    )
    
    for service in "${services[@]}"; do
        IFS=':' read -r name port <<< "$service"
        
        if curl -s "http://localhost:$port/actuator/health" > /dev/null 2>&1; then
            echo "- $name: 运行正常 ✅" >> "$report_file"
        else
            echo "- $name: 无法访问 ❌" >> "$report_file"
        fi
    done
    
    # 检查路由配置
    echo "" >> "$report_file"
    echo "## 网关路由检查" >> "$report_file"
    
    routes_response=$(curl -s "http://localhost:8080/actuator/gateway/routes" 2>/dev/null)
    
    if [ -z "$routes_response" ] || [ "$routes_response" = "[]" ]; then
        echo "- 路由配置: 无路由 ❌" >> "$report_file"
    else
        route_count=$(echo "$routes_response" | grep -o '"route_id"' | wc -l)
        echo "- 路由配置: $route_count 个路由 ✅" >> "$report_file"
    fi
    
    echo "" >> "$report_file"
    echo "## 建议" >> "$report_file"
    echo "1. 检查网关配置文件中的gateway部分是否被注释" >> "$report_file"
    echo "2. 确保所有微服务都已启动并注册到Nacos" >> "$report_file"
    echo "3. 验证路由路径配置是否正确" >> "$report_file"
    echo "4. 检查服务间的网络连接" >> "$report_file"
    
    echo -e "${GREEN}✅ 报告已生成: $report_file${NC}"
}

# 主函数
main() {
    echo -e "${CYAN}开始404问题诊断...${NC}"
    echo ""
    
    # 1. 检查服务状态
    check_services_status
    
    # 2. 检查网关路由
    check_gateway_routes
    
    # 3. 检查网关配置文件
    check_gateway_config
    
    # 4. 检查服务注册
    check_service_registration
    
    # 5. 测试API路径
    test_api_paths
    
    # 6. 修复常见问题
    fix_common_404_issues
    
    # 7. 测试修复结果
    if test_fix_results; then
        echo -e "${GREEN}🎉 404问题已解决！${NC}"
    else
        echo -e "${YELLOW}⚠️ 部分问题仍需手动处理${NC}"
    fi
    
    # 8. 生成报告
    generate_404_report
    
    echo ""
    echo -e "${GREEN}🎉 404问题诊断完成！${NC}"
    echo ""
    echo -e "${BLUE}常见404问题解决方案:${NC}"
    echo "• 网关配置被注释: 取消gateway配置的注释"
    echo "• 服务未启动: 启动相关微服务"
    echo "• 路由配置错误: 检查路径匹配规则"
    echo "• 服务未注册: 检查Nacos连接和配置"
}

# 运行主函数
main
