server:
  port: 8080

spring:
  application:
    name: game-gateway
  profiles:
    active: dev
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true
  config:
    import:
      - optional:nacos:game-gateway.yml
  cloud:
    nacos:
      discovery:
        server-addr: localhost:8848
        namespace: public
      config:
        server-addr: localhost:8848
        namespace: public
        file-extension: yml
        group: DEFAULT_GROUP
    gateway:
      discovery:
        locator:
          enabled: true
          lower-case-service-id: true
      routes:
        # 用户服务路由
        - id: game-user
          uri: lb://game-user
          predicates:
            - Path=/user/**
          filters:
            - StripPrefix=1

        # 大厅服务路由
        - id: game-hall
          uri: lb://game-hall
          predicates:
            - Path=/hall/**
          filters:
            - StripPrefix=1

        # 代理游戏服务路由
        - id: game-agentgame
          uri: lb://game-agentgame
          predicates:
            - Path=/agentgame/**
          filters:
            - StripPrefix=1

        # 活动服务路由
        - id: game-activity
          uri: lb://game-activity
          predicates:
            - Path=/activity/**
          filters:
            - StripPrefix=1

        # 账户服务路由
        - id: game-account
          uri: lb://game-account
          predicates:
            - Path=/account/**
          filters:
            - StripPrefix=1

  # WebFlux配置
  webflux:
    # 设置最大内存大小
    multipart:
      max-in-memory-size: 1MB
  codec:
    # HTTP消息编解码器配置
    max-in-memory-size: 1MB

logging:
  level:
    com.wingame.gateway: debug
    org.springframework.cloud.gateway: debug

# Actuator配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,gateway
  endpoint:
    health:
      show-details: always

# Swagger/OpenAPI配置
springdoc:
  api-docs:
    path: /v3/api-docs
  swagger-ui:
    path: /swagger-ui.html
    enabled: true
  show-actuator: true


