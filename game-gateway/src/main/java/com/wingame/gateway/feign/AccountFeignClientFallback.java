package com.wingame.gateway.feign;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 账户服务Feign客户端降级处理
 */
@Slf4j
@Component
public class AccountFeignClientFallback implements AccountFeignClient {
    
    @Override
    public Object getBalance(String userId) {
        log.warn("账户余额服务调用失败，返回默认数据，userId: {}", userId);
        
        Map<String, Object> result = new HashMap<>();
        result.put("code", 200);
        result.put("message", "账户服务暂时不可用，返回默认数据");
        
        Map<String, Object> data = new HashMap<>();
        data.put("userId", userId);
        data.put("totalBalance", BigDecimal.ZERO);
        data.put("availableBalance", BigDecimal.ZERO);
        data.put("frozenBalance", BigDecimal.ZERO);
        data.put("coins", BigDecimal.ZERO);
        data.put("points", BigDecimal.ZERO);
        data.put("status", "SERVICE_UNAVAILABLE");
        data.put("lastUpdated", LocalDateTime.now());
        
        result.put("data", data);
        return result;
    }
    
    @Override
    public Object getStatistics(String userId) {
        log.warn("账户统计服务调用失败，返回默认数据，userId: {}", userId);
        
        Map<String, Object> result = new HashMap<>();
        result.put("code", 200);
        result.put("message", "账户统计服务暂时不可用，返回默认数据");
        
        Map<String, Object> data = new HashMap<>();
        data.put("userId", userId);
        data.put("totalRecharge", BigDecimal.ZERO);
        data.put("totalWithdraw", BigDecimal.ZERO);
        data.put("totalGameWin", BigDecimal.ZERO);
        data.put("totalGameLose", BigDecimal.ZERO);
        data.put("netProfit", BigDecimal.ZERO);
        data.put("rechargeCount", 0);
        data.put("withdrawCount", 0);
        data.put("gameCount", 0);
        data.put("status", "SERVICE_UNAVAILABLE");
        
        result.put("data", data);
        return result;
    }
    
    @Override
    public Object getTransactions(String userId) {
        log.warn("账户交易记录服务调用失败，返回默认数据，userId: {}", userId);
        
        Map<String, Object> result = new HashMap<>();
        result.put("code", 200);
        result.put("message", "账户交易记录服务暂时不可用，返回默认数据");
        
        Map<String, Object> data = new HashMap<>();
        data.put("userId", userId);
        data.put("transactions", new Object[0]);
        data.put("total", 0);
        data.put("status", "SERVICE_UNAVAILABLE");
        
        result.put("data", data);
        return result;
    }
}
