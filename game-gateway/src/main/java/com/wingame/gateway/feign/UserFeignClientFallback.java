package com.wingame.gateway.feign;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 用户服务Feign客户端降级处理
 */
@Slf4j
@Component
public class UserFeignClientFallback implements UserFeignClient {
    
    @Override
    public Object getUserProfile(String userId) {
        log.warn("用户服务调用失败，返回默认档案信息，userId: {}", userId);
        
        Map<String, Object> result = new HashMap<>();
        result.put("code", 200);
        result.put("message", "用户服务暂时不可用，返回默认数据");
        
        Map<String, Object> data = new HashMap<>();
        data.put("userId", userId);
        data.put("username", "user_" + userId);
        data.put("nickname", "用户" + userId);
        data.put("status", "SERVICE_UNAVAILABLE");
        data.put("aggregationStatus", "FALLBACK");
        data.put("aggregationTime", LocalDateTime.now());
        
        result.put("data", data);
        return result;
    }
    
    @Override
    public Object getUserWithBalance(String userId) {
        log.warn("用户余额服务调用失败，返回默认数据，userId: {}", userId);
        
        Map<String, Object> result = new HashMap<>();
        result.put("code", 200);
        result.put("message", "用户余额服务暂时不可用，返回默认数据");
        
        Map<String, Object> data = new HashMap<>();
        data.put("userId", userId);
        data.put("username", "user_" + userId);
        data.put("status", "SERVICE_UNAVAILABLE");
        data.put("aggregationStatus", "FALLBACK");
        
        result.put("data", data);
        return result;
    }
    
    @Override
    public Object getUserWithStatistics(String userId) {
        log.warn("用户统计服务调用失败，返回默认数据，userId: {}", userId);
        
        Map<String, Object> result = new HashMap<>();
        result.put("code", 200);
        result.put("message", "用户统计服务暂时不可用，返回默认数据");
        
        Map<String, Object> data = new HashMap<>();
        data.put("userId", userId);
        data.put("username", "user_" + userId);
        data.put("status", "SERVICE_UNAVAILABLE");
        data.put("aggregationStatus", "FALLBACK");
        
        result.put("data", data);
        return result;
    }
}
