package com.wingame.gateway.feign;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

/**
 * 账户服务Feign客户端
 */
@FeignClient(name = "game-account", fallback = GatewayAccountFeignClientFallback.class)
public interface GatewayAccountFeignClient {
    
    /**
     * 获取用户账户余额
     */
    @GetMapping("/api/account/{userId}/balance")
    Object getBalance(@PathVariable("userId") String userId);
    
    /**
     * 获取用户账户统计
     */
    @GetMapping("/api/account/{userId}/statistics")
    Object getStatistics(@PathVariable("userId") String userId);
    
    /**
     * 获取用户交易记录
     */
    @GetMapping("/api/account/{userId}/transactions")
    Object getTransactions(@PathVariable("userId") String userId);
}
