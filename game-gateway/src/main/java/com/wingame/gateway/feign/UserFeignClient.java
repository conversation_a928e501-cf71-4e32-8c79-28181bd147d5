package com.wingame.gateway.feign;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

/**
 * 用户服务Feign客户端
 */
@FeignClient(name = "game-user", fallback = UserFeignClientFallback.class)
public interface UserFeignClient {
    
    /**
     * 获取用户完整档案信息
     */
    @GetMapping("/api/user/{userId}/profile")
    Object getUserProfile(@PathVariable("userId") String userId);
    
    /**
     * 获取用户信息和余额
     */
    @GetMapping("/api/user/{userId}/balance")
    Object getUserWithBalance(@PathVariable("userId") String userId);
    
    /**
     * 获取用户信息和统计
     */
    @GetMapping("/api/user/{userId}/statistics")
    Object getUserWithStatistics(@PathVariable("userId") String userId);
}
