package com.wingame.gateway.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.codec.ServerCodecConfigurer;
import org.springframework.http.codec.support.DefaultServerCodecConfigurer;
import org.springframework.web.reactive.config.WebFluxConfigurer;

/**
 * WebFlux配置类
 * Spring Cloud Gateway自动配置WebFlux，无需@EnableWebFlux
 */
@Configuration
public class WebFluxConfig implements WebFluxConfigurer {

    // ServerCodecConfigurer Bean由Spring WebFlux自动配置，无需手动定义

    /**
     * 配置HTTP消息编解码器
     */
    @Override
    public void configureHttpMessageCodecs(org.springframework.http.codec.ServerCodecConfigurer configurer) {
        // 设置内存缓冲区大小 (默认256KB，这里设置为1MB)
        configurer.defaultCodecs().maxInMemorySize(1024 * 1024);
        
        // 启用日志记录
        configurer.defaultCodecs().enableLoggingRequestDetails(true);
    }
}
