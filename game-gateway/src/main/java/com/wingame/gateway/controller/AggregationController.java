package com.wingame.gateway.controller;

import com.wingame.gateway.feign.AccountFeignClient;
import com.wingame.gateway.feign.UserFeignClient;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * 网关聚合控制器
 * 在网关层聚合多个微服务的数据
 */
@Slf4j
@RestController
@RequestMapping("/api/aggregation")
@RequiredArgsConstructor
public class AggregationController {
    
    private final UserFeignClient userFeignClient;
    private final AccountFeignClient accountFeignClient;
    
    /**
     * 获取用户完整信息（通过用户服务聚合）
     */
    @GetMapping("/user/{userId}/profile")
    public Object getUserProfile(@PathVariable String userId) {
        log.info("网关聚合：获取用户完整档案信息，userId: {}", userId);
        
        try {
            // 直接调用用户服务的聚合接口
            return userFeignClient.getUserProfile(userId);
        } catch (Exception e) {
            log.error("获取用户档案失败，userId: {}, error: {}", userId, e.getMessage());
            return createErrorResponse("获取用户档案失败", e.getMessage());
        }
    }
    
    /**
     * 在网关层聚合用户基本信息和账户信息
     */
    @GetMapping("/user/{userId}/dashboard")
    public Object getUserDashboard(@PathVariable String userId) {
        log.info("网关聚合：获取用户仪表板信息，userId: {}", userId);
        
        Map<String, Object> result = new HashMap<>();
        result.put("code", 200);
        result.put("message", "获取用户仪表板信息成功");
        result.put("aggregationTime", LocalDateTime.now());
        result.put("aggregationLevel", "GATEWAY");
        
        try {
            // 并行调用多个服务
            CompletableFuture<Object> userFuture = CompletableFuture.supplyAsync(() -> {
                try {
                    return userFeignClient.getUserWithBalance(userId);
                } catch (Exception e) {
                    log.error("获取用户信息失败: {}", e.getMessage());
                    return null;
                }
            });
            
            CompletableFuture<Object> accountStatsFuture = CompletableFuture.supplyAsync(() -> {
                try {
                    return accountFeignClient.getStatistics(userId);
                } catch (Exception e) {
                    log.error("获取账户统计失败: {}", e.getMessage());
                    return null;
                }
            });
            
            CompletableFuture<Object> transactionsFuture = CompletableFuture.supplyAsync(() -> {
                try {
                    return accountFeignClient.getTransactions(userId);
                } catch (Exception e) {
                    log.error("获取交易记录失败: {}", e.getMessage());
                    return null;
                }
            });
            
            // 等待所有调用完成（设置超时时间）
            Object userInfo = userFuture.get(3, TimeUnit.SECONDS);
            Object accountStats = accountStatsFuture.get(3, TimeUnit.SECONDS);
            Object transactions = transactionsFuture.get(3, TimeUnit.SECONDS);
            
            // 聚合数据
            Map<String, Object> dashboard = new HashMap<>();
            dashboard.put("userId", userId);
            dashboard.put("userInfo", extractData(userInfo));
            dashboard.put("accountStatistics", extractData(accountStats));
            dashboard.put("recentTransactions", extractData(transactions));
            dashboard.put("aggregationStatus", "SUCCESS");
            
            result.put("data", dashboard);
            
            log.info("用户仪表板信息聚合成功，userId: {}", userId);
            
        } catch (Exception e) {
            log.error("用户仪表板信息聚合失败，userId: {}, error: {}", userId, e.getMessage());
            
            Map<String, Object> dashboard = new HashMap<>();
            dashboard.put("userId", userId);
            dashboard.put("aggregationStatus", "PARTIAL_FAILURE");
            dashboard.put("error", e.getMessage());
            
            result.put("data", dashboard);
            result.put("message", "部分信息获取失败");
        }
        
        return result;
    }
    
    /**
     * 获取用户财务概览（网关层聚合）
     */
    @GetMapping("/user/{userId}/financial-overview")
    public Object getUserFinancialOverview(@PathVariable String userId) {
        log.info("网关聚合：获取用户财务概览，userId: {}", userId);
        
        Map<String, Object> result = new HashMap<>();
        result.put("code", 200);
        result.put("message", "获取用户财务概览成功");
        result.put("aggregationTime", LocalDateTime.now());
        result.put("aggregationLevel", "GATEWAY");
        
        try {
            // 并行调用账户相关服务
            CompletableFuture<Object> balanceFuture = CompletableFuture.supplyAsync(() -> {
                try {
                    return accountFeignClient.getBalance(userId);
                } catch (Exception e) {
                    log.error("获取余额信息失败: {}", e.getMessage());
                    return null;
                }
            });
            
            CompletableFuture<Object> statsFuture = CompletableFuture.supplyAsync(() -> {
                try {
                    return accountFeignClient.getStatistics(userId);
                } catch (Exception e) {
                    log.error("获取统计信息失败: {}", e.getMessage());
                    return null;
                }
            });
            
            // 等待调用完成
            Object balance = balanceFuture.get(3, TimeUnit.SECONDS);
            Object statistics = statsFuture.get(3, TimeUnit.SECONDS);
            
            // 聚合财务数据
            Map<String, Object> financial = new HashMap<>();
            financial.put("userId", userId);
            financial.put("balance", extractData(balance));
            financial.put("statistics", extractData(statistics));
            financial.put("aggregationStatus", "SUCCESS");
            
            // 计算一些衍生指标
            financial.put("riskLevel", calculateRiskLevel(extractData(statistics)));
            financial.put("creditScore", calculateCreditScore(extractData(statistics)));
            
            result.put("data", financial);
            
            log.info("用户财务概览聚合成功，userId: {}", userId);
            
        } catch (Exception e) {
            log.error("用户财务概览聚合失败，userId: {}, error: {}", userId, e.getMessage());
            
            Map<String, Object> financial = new HashMap<>();
            financial.put("userId", userId);
            financial.put("aggregationStatus", "FAILURE");
            financial.put("error", e.getMessage());
            
            result.put("data", financial);
            result.put("message", "财务概览获取失败");
        }
        
        return result;
    }
    
    /**
     * 健康检查接口
     */
    @GetMapping("/health")
    public Object health() {
        Map<String, Object> health = new HashMap<>();
        health.put("status", "UP");
        health.put("service", "gateway-aggregation");
        health.put("timestamp", LocalDateTime.now());
        
        // 检查依赖服务状态
        Map<String, String> dependencies = new HashMap<>();
        dependencies.put("user-service", "UP");
        dependencies.put("account-service", "UP");
        health.put("dependencies", dependencies);
        
        return health;
    }
    
    /**
     * 提取响应数据
     */
    private Object extractData(Object response) {
        if (response == null) {
            return null;
        }
        
        if (response instanceof Map) {
            Map<?, ?> responseMap = (Map<?, ?>) response;
            return responseMap.get("data");
        }
        
        return response;
    }
    
    /**
     * 计算风险等级
     */
    private String calculateRiskLevel(Object statistics) {
        // 简单的风险评估逻辑
        if (statistics == null) {
            return "UNKNOWN";
        }
        
        // 这里可以根据统计数据计算风险等级
        return "LOW";
    }
    
    /**
     * 计算信用分数
     */
    private int calculateCreditScore(Object statistics) {
        // 简单的信用分数计算逻辑
        if (statistics == null) {
            return 0;
        }
        
        // 这里可以根据统计数据计算信用分数
        return 750;
    }
    
    /**
     * 创建错误响应
     */
    private Object createErrorResponse(String message, String error) {
        Map<String, Object> result = new HashMap<>();
        result.put("code", 500);
        result.put("message", message);
        result.put("error", error);
        result.put("timestamp", LocalDateTime.now());
        return result;
    }
}
