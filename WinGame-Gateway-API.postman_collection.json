{"info": {"name": "WinGame Gateway API Collection", "description": "Spring Cloud Gateway 路由测试集合", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "gateway_url", "value": "http://localhost:8080", "type": "string"}, {"key": "user_id", "value": "demo_user_123", "type": "string"}], "item": [{"name": "用户服务", "item": [{"name": "用户注册", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"username\": \"testuser001\",\n    \"password\": \"123456\",\n    \"confirmPassword\": \"123456\",\n    \"nickname\": \"测试用户001\",\n    \"email\": \"<EMAIL>\",\n    \"phone\": \"13800138001\"\n}"}, "url": {"raw": "{{gateway_url}}/user/api/user/register", "host": ["{{gateway_url}}"], "path": ["user", "api", "user", "register"]}}}, {"name": "用户登录", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"username\": \"testuser001\",\n    \"password\": \"123456\"\n}"}, "url": {"raw": "{{gateway_url}}/user/api/user/login", "host": ["{{gateway_url}}"], "path": ["user", "api", "user", "login"]}}}, {"name": "检查用户名", "request": {"method": "GET", "url": {"raw": "{{gateway_url}}/user/api/user/check/username/testuser001", "host": ["{{gateway_url}}"], "path": ["user", "api", "user", "check", "username", "testuser001"]}}}]}, {"name": "大厅服务", "item": [{"name": "获取所有房间", "request": {"method": "GET", "url": {"raw": "{{gateway_url}}/hall/api/hall/rooms", "host": ["{{gateway_url}}"], "path": ["hall", "api", "hall", "rooms"]}}}, {"name": "获取大厅统计", "request": {"method": "GET", "url": {"raw": "{{gateway_url}}/hall/api/hall/stats", "host": ["{{gateway_url}}"], "path": ["hall", "api", "hall", "stats"]}}}, {"name": "创建房间", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"roomName\": \"测试房间001\",\n    \"gameType\": \"POKER\",\n    \"roomType\": 1,\n    \"maxPlayers\": 6,\n    \"minBet\": 100,\n    \"maxBet\": 5000\n}"}, "url": {"raw": "{{gateway_url}}/hall/api/hall/rooms", "host": ["{{gateway_url}}"], "path": ["hall", "api", "hall", "rooms"]}}}, {"name": "加入房间", "request": {"method": "POST", "url": {"raw": "{{gateway_url}}/hall/api/hall/rooms/test-room-123/join?userId={{user_id}}", "host": ["{{gateway_url}}"], "path": ["hall", "api", "hall", "rooms", "test-room-123", "join"], "query": [{"key": "userId", "value": "{{user_id}}"}]}}}]}, {"name": "代理游戏服务", "item": [{"name": "获取可用游戏", "request": {"method": "GET", "url": {"raw": "{{gateway_url}}/agentgame/api/agentgame/games", "host": ["{{gateway_url}}"], "path": ["agentgame", "api", "agentgame", "games"]}}}, {"name": "获取游戏详情", "request": {"method": "GET", "url": {"raw": "{{gateway_url}}/agentgame/api/agentgame/games/texas_holdem", "host": ["{{gateway_url}}"], "path": ["agentgame", "api", "agentgame", "games", "texas_holdem"]}}}, {"name": "启动游戏", "request": {"method": "POST", "url": {"raw": "{{gateway_url}}/agentgame/api/agentgame/games/texas_holdem/launch?userId={{user_id}}", "host": ["{{gateway_url}}"], "path": ["agentgame", "api", "agentgame", "games", "texas_holdem", "launch"], "query": [{"key": "userId", "value": "{{user_id}}"}]}}}, {"name": "获取游戏历史", "request": {"method": "GET", "url": {"raw": "{{gateway_url}}/agentgame/api/agentgame/history?userId={{user_id}}&page=0&size=5", "host": ["{{gateway_url}}"], "path": ["agentgame", "api", "agentgame", "history"], "query": [{"key": "userId", "value": "{{user_id}}"}, {"key": "page", "value": "0"}, {"key": "size", "value": "5"}]}}}]}, {"name": "活动服务", "item": [{"name": "获取活动列表", "request": {"method": "GET", "url": {"raw": "{{gateway_url}}/activity/api/activity/list", "host": ["{{gateway_url}}"], "path": ["activity", "api", "activity", "list"]}}}, {"name": "参与活动", "request": {"method": "POST", "url": {"raw": "{{gateway_url}}/activity/api/activity/test-activity-123/participate?userId={{user_id}}", "host": ["{{gateway_url}}"], "path": ["activity", "api", "activity", "test-activity-123", "participate"], "query": [{"key": "userId", "value": "{{user_id}}"}]}}}, {"name": "每日签到", "request": {"method": "POST", "url": {"raw": "{{gateway_url}}/activity/api/activity/checkin?userId={{user_id}}", "host": ["{{gateway_url}}"], "path": ["activity", "api", "activity", "checkin"], "query": [{"key": "userId", "value": "{{user_id}}"}]}}}]}, {"name": "账户服务", "item": [{"name": "获取账户余额", "request": {"method": "GET", "url": {"raw": "{{gateway_url}}/account/api/account/{{user_id}}/balance", "host": ["{{gateway_url}}"], "path": ["account", "api", "account", "{{user_id}}", "balance"]}}}, {"name": "账户充值", "request": {"method": "POST", "url": {"raw": "{{gateway_url}}/account/api/account/{{user_id}}/recharge?amount=1000&paymentMethod=alipay", "host": ["{{gateway_url}}"], "path": ["account", "api", "account", "{{user_id}}", "recharge"], "query": [{"key": "amount", "value": "1000"}, {"key": "paymentMethod", "value": "alipay"}]}}}, {"name": "获取交易记录", "request": {"method": "GET", "url": {"raw": "{{gateway_url}}/account/api/account/{{user_id}}/transactions?page=0&size=5", "host": ["{{gateway_url}}"], "path": ["account", "api", "account", "{{user_id}}", "transactions"], "query": [{"key": "page", "value": "0"}, {"key": "size", "value": "5"}]}}}]}]}