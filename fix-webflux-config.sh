#!/bin/bash

echo "=== WebFlux配置修复脚本 ==="
echo ""

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 检查WebFlux相关依赖
check_webflux_dependencies() {
    echo -e "${BLUE}检查WebFlux相关依赖...${NC}"
    
    gateway_pom="game-gateway/pom.xml"
    
    if [ -f "$gateway_pom" ]; then
        echo -e "${CYAN}检查网关服务依赖...${NC}"
        
        # 检查Spring Cloud Gateway依赖
        if grep -q "spring-cloud-starter-gateway" "$gateway_pom"; then
            echo -e "${GREEN}✅ Spring Cloud Gateway依赖存在${NC}"
        else
            echo -e "${RED}❌ 缺少Spring Cloud Gateway依赖${NC}"
        fi
        
        # 检查WebFlux Swagger依赖
        if grep -q "springdoc-openapi-webflux-ui" "$gateway_pom"; then
            echo -e "${GREEN}✅ WebFlux Swagger依赖存在${NC}"
        else
            echo -e "${YELLOW}⚠️ 缺少WebFlux Swagger依赖${NC}"
        fi
        
        # 检查Actuator依赖
        if grep -q "spring-boot-starter-actuator" "$gateway_pom"; then
            echo -e "${GREEN}✅ Actuator依赖存在${NC}"
        else
            echo -e "${YELLOW}⚠️ 缺少Actuator依赖${NC}"
        fi
    else
        echo -e "${RED}❌ 网关服务pom.xml不存在${NC}"
    fi
    
    echo ""
}

# 检查WebFlux配置类
check_webflux_config() {
    echo -e "${BLUE}检查WebFlux配置类...${NC}"
    
    config_file="game-gateway/src/main/java/com/wingame/gateway/config/WebFluxConfig.java"
    
    if [ -f "$config_file" ]; then
        echo -e "${GREEN}✅ WebFlux配置类存在${NC}"
        
        # 检查关键配置
        if grep -q "ServerCodecConfigurer" "$config_file"; then
            echo -e "${GREEN}✅ ServerCodecConfigurer配置存在${NC}"
        else
            echo -e "${RED}❌ 缺少ServerCodecConfigurer配置${NC}"
        fi
        
        if grep -q "@EnableWebFlux" "$config_file"; then
            echo -e "${GREEN}✅ @EnableWebFlux注解存在${NC}"
        else
            echo -e "${YELLOW}⚠️ 缺少@EnableWebFlux注解${NC}"
        fi
    else
        echo -e "${RED}❌ WebFlux配置类不存在${NC}"
        echo -e "${YELLOW}建议创建WebFlux配置类${NC}"
    fi
    
    echo ""
}

# 检查application.yml配置
check_application_config() {
    echo -e "${BLUE}检查application.yml配置...${NC}"
    
    config_file="game-gateway/src/main/resources/application.yml"
    
    if [ -f "$config_file" ]; then
        echo -e "${GREEN}✅ application.yml存在${NC}"
        
        # 检查WebFlux配置
        if grep -q "webflux:" "$config_file"; then
            echo -e "${GREEN}✅ WebFlux配置存在${NC}"
        else
            echo -e "${YELLOW}⚠️ 缺少WebFlux配置${NC}"
        fi
        
        # 检查codec配置
        if grep -q "codec:" "$config_file"; then
            echo -e "${GREEN}✅ Codec配置存在${NC}"
        else
            echo -e "${YELLOW}⚠️ 缺少Codec配置${NC}"
        fi
        
        # 检查springdoc配置
        if grep -q "springdoc:" "$config_file"; then
            echo -e "${GREEN}✅ SpringDoc配置存在${NC}"
        else
            echo -e "${YELLOW}⚠️ 缺少SpringDoc配置${NC}"
        fi
    else
        echo -e "${RED}❌ application.yml不存在${NC}"
    fi
    
    echo ""
}

# 编译测试
test_compilation() {
    echo -e "${BLUE}测试编译...${NC}"
    
    echo -e "${CYAN}编译网关服务...${NC}"
    if mvn clean compile -pl game-gateway -am -q; then
        echo -e "${GREEN}✅ 网关服务编译成功${NC}"
    else
        echo -e "${RED}❌ 网关服务编译失败${NC}"
        echo ""
        echo -e "${YELLOW}常见编译错误解决方案:${NC}"
        echo "1. 检查ServerCodecConfigurer Bean配置"
        echo "2. 确保WebFlux依赖正确"
        echo "3. 检查@EnableWebFlux注解"
        echo "4. 验证Swagger WebFlux依赖"
        return 1
    fi
    
    echo ""
    echo -e "${CYAN}编译整个项目...${NC}"
    if mvn clean compile -q; then
        echo -e "${GREEN}✅ 整个项目编译成功${NC}"
    else
        echo -e "${RED}❌ 项目编译失败${NC}"
        return 1
    fi
    
    return 0
}

# 测试网关启动
test_gateway_startup() {
    echo -e "${BLUE}测试网关启动...${NC}"
    
    # 检查是否有Java进程在运行网关
    if pgrep -f "game-gateway.*jar" > /dev/null; then
        echo -e "${YELLOW}⚠️ 网关服务已在运行，停止现有进程...${NC}"
        pkill -f "game-gateway.*jar"
        sleep 3
    fi
    
    # 编译并打包
    echo -e "${CYAN}编译打包网关服务...${NC}"
    if mvn clean package -pl game-gateway -am -DskipTests -q; then
        echo -e "${GREEN}✅ 网关服务打包成功${NC}"
    else
        echo -e "${RED}❌ 网关服务打包失败${NC}"
        return 1
    fi
    
    # 尝试启动网关（后台运行）
    echo -e "${CYAN}启动网关服务...${NC}"
    cd game-gateway
    nohup java -jar target/game-gateway-1.0-SNAPSHOT.jar > ../logs/gateway.log 2>&1 &
    GATEWAY_PID=$!
    cd ..
    
    echo "网关服务启动中，PID: $GATEWAY_PID"
    
    # 等待启动
    echo -e "${CYAN}等待网关启动...${NC}"
    for i in {1..30}; do
        if curl -s http://localhost:8080/actuator/health > /dev/null 2>&1; then
            echo -e "${GREEN}✅ 网关服务启动成功！${NC}"
            
            # 测试基本功能
            echo -e "${CYAN}测试网关基本功能...${NC}"
            
            # 测试健康检查
            health_response=$(curl -s http://localhost:8080/actuator/health)
            if echo "$health_response" | grep -q "UP"; then
                echo -e "${GREEN}✅ 健康检查通过${NC}"
            else
                echo -e "${YELLOW}⚠️ 健康检查异常${NC}"
            fi
            
            # 测试Swagger UI
            if curl -s http://localhost:8080/swagger-ui.html > /dev/null 2>&1; then
                echo -e "${GREEN}✅ Swagger UI可访问${NC}"
            else
                echo -e "${YELLOW}⚠️ Swagger UI不可访问${NC}"
            fi
            
            return 0
        else
            echo -n "."
            sleep 2
        fi
    done
    
    echo ""
    echo -e "${RED}❌ 网关服务启动超时${NC}"
    echo -e "${YELLOW}查看启动日志: tail -f logs/gateway.log${NC}"
    return 1
}

# 提供修复建议
provide_fix_suggestions() {
    echo -e "${BLUE}WebFlux配置修复建议:${NC}"
    echo ""
    
    echo -e "${CYAN}1. ServerCodecConfigurer Bean缺失:${NC}"
    echo "   - 创建WebFluxConfig配置类"
    echo "   - 添加@Bean ServerCodecConfigurer方法"
    echo "   - 使用DefaultServerCodecConfigurer实现"
    echo ""
    
    echo -e "${CYAN}2. WebFlux依赖问题:${NC}"
    echo "   - 确保使用spring-cloud-starter-gateway"
    echo "   - 添加springdoc-openapi-webflux-ui依赖"
    echo "   - 避免混用WebMVC和WebFlux依赖"
    echo ""
    
    echo -e "${CYAN}3. 配置文件优化:${NC}"
    echo "   - 添加spring.webflux配置"
    echo "   - 配置spring.codec.max-in-memory-size"
    echo "   - 设置合适的缓冲区大小"
    echo ""
    
    echo -e "${CYAN}4. 常见错误解决:${NC}"
    echo "   - 检查@EnableWebFlux注解"
    echo "   - 验证WebFluxConfigurer实现"
    echo "   - 确保没有WebMVC相关配置冲突"
    echo ""
}

# 生成WebFlux配置报告
generate_webflux_report() {
    echo -e "${BLUE}生成WebFlux配置报告...${NC}"
    
    report_file="webflux_config_report.txt"
    
    cat > "$report_file" << EOF
# WebFlux配置检查报告
生成时间: $(date)

## 网关服务配置状态
EOF
    
    # 检查依赖
    echo "" >> "$report_file"
    echo "### 依赖检查" >> "$report_file"
    
    if [ -f "game-gateway/pom.xml" ]; then
        echo "- Spring Cloud Gateway: $(grep -q "spring-cloud-starter-gateway" game-gateway/pom.xml && echo "✅" || echo "❌")" >> "$report_file"
        echo "- WebFlux Swagger: $(grep -q "springdoc-openapi-webflux-ui" game-gateway/pom.xml && echo "✅" || echo "❌")" >> "$report_file"
        echo "- Actuator: $(grep -q "spring-boot-starter-actuator" game-gateway/pom.xml && echo "✅" || echo "❌")" >> "$report_file"
    fi
    
    # 检查配置文件
    echo "" >> "$report_file"
    echo "### 配置文件检查" >> "$report_file"
    
    if [ -f "game-gateway/src/main/java/com/wingame/gateway/config/WebFluxConfig.java" ]; then
        echo "- WebFlux配置类: ✅" >> "$report_file"
    else
        echo "- WebFlux配置类: ❌" >> "$report_file"
    fi
    
    if [ -f "game-gateway/src/main/resources/application.yml" ]; then
        echo "- application.yml: ✅" >> "$report_file"
        echo "  - WebFlux配置: $(grep -q "webflux:" game-gateway/src/main/resources/application.yml && echo "✅" || echo "❌")" >> "$report_file"
        echo "  - SpringDoc配置: $(grep -q "springdoc:" game-gateway/src/main/resources/application.yml && echo "✅" || echo "❌")" >> "$report_file"
    else
        echo "- application.yml: ❌" >> "$report_file"
    fi
    
    echo "" >> "$report_file"
    echo "### 建议" >> "$report_file"
    echo "1. 确保WebFlux配置类存在并正确配置ServerCodecConfigurer" >> "$report_file"
    echo "2. 验证所有WebFlux相关依赖" >> "$report_file"
    echo "3. 检查application.yml中的WebFlux配置" >> "$report_file"
    echo "4. 测试网关服务启动和基本功能" >> "$report_file"
    
    echo -e "${GREEN}✅ 报告已生成: $report_file${NC}"
}

# 主函数
main() {
    echo -e "${CYAN}开始WebFlux配置检查和修复...${NC}"
    echo ""
    
    # 1. 检查WebFlux依赖
    check_webflux_dependencies
    
    # 2. 检查WebFlux配置类
    check_webflux_config
    
    # 3. 检查application.yml配置
    check_application_config
    
    # 4. 测试编译
    if test_compilation; then
        echo ""
        echo -e "${GREEN}🎉 编译测试通过！${NC}"
        
        # 5. 测试网关启动
        echo ""
        echo -n "是否测试网关启动? (y/N): "
        read -r test_startup
        
        if [[ $test_startup =~ ^[Yy]$ ]]; then
            test_gateway_startup
        fi
    else
        echo ""
        echo -e "${RED}❌ 编译失败，需要修复配置${NC}"
    fi
    
    # 6. 提供修复建议
    echo ""
    provide_fix_suggestions
    
    # 7. 生成报告
    generate_webflux_report
    
    echo ""
    echo -e "${GREEN}🎉 WebFlux配置检查完成！${NC}"
    echo ""
    echo -e "${BLUE}关键配置文件:${NC}"
    echo "• WebFlux配置: game-gateway/src/main/java/com/wingame/gateway/config/WebFluxConfig.java"
    echo "• 应用配置: game-gateway/src/main/resources/application.yml"
    echo "• Swagger配置: game-gateway/src/main/java/com/wingame/gateway/config/SwaggerConfig.java"
    echo ""
    echo -e "${BLUE}测试命令:${NC}"
    echo "• 编译: mvn clean compile -pl game-gateway -am"
    echo "• 启动: cd game-gateway && java -jar target/game-gateway-1.0-SNAPSHOT.jar"
    echo "• 健康检查: curl http://localhost:8080/actuator/health"
    echo "• Swagger UI: curl http://localhost:8080/swagger-ui.html"
}

# 运行主函数
main
