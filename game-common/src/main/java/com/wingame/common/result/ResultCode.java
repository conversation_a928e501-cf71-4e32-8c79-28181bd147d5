package com.wingame.common.result;

/**
 * 枚举了一些常用API操作码
 */
public enum ResultCode {
    SUCCESS(200, "操作成功"),
    FAILED(500, "操作失败"),
    VALIDATE_FAILED(404, "参数检验失败"),
    UNAUTHORIZED(401, "暂未登录或token已经过期"),
    FORBIDDEN(403, "没有相关权限"),
    
    // 用户相关错误码
    USER_NOT_FOUND(1001, "用户不存在"),
    USER_ALREADY_EXISTS(1002, "用户已存在"),
    USER_PASSWORD_ERROR(1003, "密码错误"),
    USER_ACCOUNT_LOCKED(1004, "账户已锁定"),
    USER_ACCOUNT_DISABLED(1005, "账户已禁用"),
    
    // 游戏相关错误码
    GAME_NOT_FOUND(2001, "游戏不存在"),
    GAME_ROOM_FULL(2002, "游戏房间已满"),
    GAME_ROOM_NOT_FOUND(2003, "游戏房间不存在"),
    GAME_ALREADY_STARTED(2004, "游戏已开始"),
    GAME_NOT_STARTED(2005, "游戏未开始"),
    
    // 账户相关错误码
    ACCOUNT_BALANCE_INSUFFICIENT(3001, "账户余额不足"),
    ACCOUNT_FROZEN(3002, "账户已冻结"),
    ACCOUNT_TRANSACTION_FAILED(3003, "账户交易失败"),
    
    // 活动相关错误码
    ACTIVITY_NOT_FOUND(4001, "活动不存在"),
    ACTIVITY_NOT_STARTED(4002, "活动未开始"),
    ACTIVITY_ENDED(4003, "活动已结束"),
    ACTIVITY_LIMIT_EXCEEDED(4004, "活动参与次数已达上限"),
    
    // 系统相关错误码
    SYSTEM_ERROR(9001, "系统错误"),
    NETWORK_ERROR(9002, "网络错误"),
    DATABASE_ERROR(9003, "数据库错误"),
    CACHE_ERROR(9004, "缓存错误");
    
    private final Integer code;
    private final String message;
    
    ResultCode(Integer code, String message) {
        this.code = code;
        this.message = message;
    }
    
    public Integer getCode() {
        return code;
    }
    
    public String getMessage() {
        return message;
    }
}
