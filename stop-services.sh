#!/bin/bash

echo "=== 快速停止WinGame服务 ==="
echo ""

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 停止所有Java微服务
echo -e "${BLUE}停止Java微服务...${NC}"

# 查找所有game相关的Java进程
game_pids=$(pgrep -f "game-.*jar" 2>/dev/null)

if [ -n "$game_pids" ]; then
    echo "发现运行中的服务进程:"
    echo "$game_pids" | while read pid; do
        cmd=$(ps -p "$pid" -o command= 2>/dev/null | grep -o "game-[^/]*")
        echo "  PID $pid: $cmd"
    done
    
    echo ""
    echo "停止微服务..."
    
    # 优雅停止
    echo "$game_pids" | xargs kill -TERM 2>/dev/null
    sleep 3
    
    # 检查是否还有进程运行
    remaining_pids=$(pgrep -f "game-.*jar" 2>/dev/null)
    
    if [ -n "$remaining_pids" ]; then
        echo "强制停止剩余进程..."
        echo "$remaining_pids" | xargs kill -KILL 2>/dev/null
        sleep 1
    fi
    
    # 最终检查
    final_pids=$(pgrep -f "game-.*jar" 2>/dev/null)
    
    if [ -z "$final_pids" ]; then
        echo -e "${GREEN}✅ 所有微服务已停止${NC}"
    else
        echo -e "${RED}❌ 部分服务停止失败${NC}"
        echo "剩余进程: $final_pids"
    fi
else
    echo -e "${YELLOW}⚠️ 没有发现运行中的微服务${NC}"
fi

echo ""

# 停止Docker基础设施
echo -e "${BLUE}停止Docker基础设施...${NC}"

# 检测架构选择配置文件
ARCH=$(uname -m)
if [[ "$ARCH" == "arm64" ]] || [[ "$ARCH" == "aarch64" ]]; then
    COMPOSE_FILE="docker-compose-arm64.yml"
else
    COMPOSE_FILE="docker-compose.yml"
fi

if [ -f "$COMPOSE_FILE" ]; then
    echo "使用配置文件: $COMPOSE_FILE"
    
    if docker-compose -f "$COMPOSE_FILE" down > /dev/null 2>&1; then
        echo -e "${GREEN}✅ Docker容器已停止${NC}"
    else
        echo -e "${YELLOW}⚠️ Docker停止可能有问题，尝试手动停止...${NC}"
        
        # 手动停止容器
        containers=("nacos" "mysql" "mongodb" "redis")
        for container in "${containers[@]}"; do
            if docker ps -q -f name="$container" | grep -q .; then
                echo "  停止容器: $container"
                docker stop "$container" > /dev/null 2>&1
            fi
        done
    fi
else
    echo -e "${YELLOW}⚠️ Docker Compose文件不存在，跳过${NC}"
fi

echo ""

# 检查最终状态
echo -e "${BLUE}检查停止状态...${NC}"

# 检查Java进程
java_count=$(pgrep -f "java.*jar" 2>/dev/null | wc -l)
echo "剩余Java进程: $java_count"

# 检查Docker容器
container_count=$(docker ps -q 2>/dev/null | wc -l)
echo "运行中的Docker容器: $container_count"

echo ""

if [ $java_count -eq 0 ] && [ $container_count -eq 0 ]; then
    echo -e "${GREEN}🎉 所有服务已停止！${NC}"
else
    echo -e "${YELLOW}⚠️ 部分服务可能仍在运行${NC}"
    
    if [ $java_count -gt 0 ]; then
        echo "如需强制停止所有Java进程，运行: pkill -f java"
    fi
    
    if [ $container_count -gt 0 ]; then
        echo "如需停止所有Docker容器，运行: docker stop \$(docker ps -q)"
    fi
fi

echo ""
echo -e "${BLUE}重新启动服务:${NC}"
echo "• 启动基础设施: ./start-infrastructure.sh"
echo "• 启动微服务: ./start-services.sh"
echo "• 完整停止: ./stop-all-services.sh"
