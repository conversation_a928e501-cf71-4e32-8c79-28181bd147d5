# Maven
target/
pom.xml.tag
pom.xml.releaseBackup
pom.xml.versionsBackup
pom.xml.next
release.properties
dependency-reduced-pom.xml
buildNumber.properties
.mvn/timing.properties
.mvn/wrapper/maven-wrapper.jar

# IDE - IntelliJ IDEA
.idea/
.idea/*
.idea/**/*
*.iws
*.iml
*.ipr
*.idea
.idea_modules/

# IDE - VSCode
.vscode/
.vscode/*

# IDE - Eclipse
.settings/
.project
.classpath
.metadata/
bin/
tmp/
*.tmp
*.bak
*.swp
*~.nib
local.properties
.loadpath
.recommenders

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs/
*.log
*.log.*

# Runtime
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# nyc test coverage
.nyc_output

# Dependency directories
node_modules/

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env

# Application specific
application-local.yml
application-dev.yml
application-prod.yml

# Backup files
*.backup
*.bak
*.tmp

# Docker
.docker/

# Kubernetes
k8s/secrets/

# Temporary files
temp/
tmp/
