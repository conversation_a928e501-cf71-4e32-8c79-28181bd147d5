#!/bin/bash

echo "=== WebMVC/WebFlux冲突修复脚本 ==="
echo ""

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 检查WebMVC和WebFlux冲突
check_webmvc_webflux_conflict() {
    echo -e "${BLUE}检查WebMVC和WebFlux冲突...${NC}"
    echo ""
    
    services=("game-gateway" "game-user" "game-hall" "game-agentGame" "game-activity" "game-account")
    
    for service in "${services[@]}"; do
        if [ -d "$service" ]; then
            echo -e "${CYAN}检查 $service 服务...${NC}"
            
            # 检查启动类注解
            main_class=$(find "$service/src/main/java" -name "*Application.java" 2>/dev/null | head -1)
            
            if [ -f "$main_class" ]; then
                echo "  启动类: $(basename "$main_class")"
                
                # 检查@EnableWebMvc
                if grep -q "@EnableWebMvc" "$main_class"; then
                    echo -e "${YELLOW}    ⚠️ 发现@EnableWebMvc注解${NC}"
                fi
                
                # 检查@EnableWebFlux
                if grep -q "@EnableWebFlux" "$main_class"; then
                    echo -e "${YELLOW}    ⚠️ 发现@EnableWebFlux注解${NC}"
                fi
                
                # 检查同时存在的情况
                if grep -q "@EnableWebMvc" "$main_class" && grep -q "@EnableWebFlux" "$main_class"; then
                    echo -e "${RED}    ❌ 同时存在@EnableWebMvc和@EnableWebFlux注解！${NC}"
                fi
            fi
            
            # 检查pom.xml依赖
            pom_file="$service/pom.xml"
            
            if [ -f "$pom_file" ]; then
                has_web_starter=false
                has_webflux_starter=false
                
                if grep -q "spring-boot-starter-web" "$pom_file"; then
                    echo -e "${YELLOW}    ⚠️ 发现spring-boot-starter-web依赖${NC}"
                    has_web_starter=true
                fi
                
                if grep -q "spring-boot-starter-webflux" "$pom_file"; then
                    echo -e "${YELLOW}    ⚠️ 发现spring-boot-starter-webflux依赖${NC}"
                    has_webflux_starter=true
                fi
                
                if grep -q "spring-cloud-starter-gateway" "$pom_file"; then
                    echo -e "${BLUE}    ℹ️ 发现spring-cloud-starter-gateway依赖（基于WebFlux）${NC}"
                    
                    if [ "$has_web_starter" = true ]; then
                        echo -e "${RED}    ❌ Gateway项目不应该包含spring-boot-starter-web！${NC}"
                    fi
                fi
                
                if [ "$has_web_starter" = true ] && [ "$has_webflux_starter" = true ]; then
                    echo -e "${RED}    ❌ 同时存在WebMVC和WebFlux依赖！${NC}"
                fi
            fi
            
            echo ""
        fi
    done
}

# 检查配置类冲突
check_config_conflicts() {
    echo -e "${BLUE}检查配置类冲突...${NC}"
    echo ""
    
    # 查找WebMVC配置类
    webmvc_configs=$(find . -name "*.java" -exec grep -l "@EnableWebMvc\|WebMvcConfigurer" {} \; 2>/dev/null)
    
    if [ -n "$webmvc_configs" ]; then
        echo -e "${YELLOW}发现WebMVC配置类:${NC}"
        echo "$webmvc_configs" | sed 's/^/  /'
        echo ""
    fi
    
    # 查找WebFlux配置类
    webflux_configs=$(find . -name "*.java" -exec grep -l "@EnableWebFlux\|WebFluxConfigurer" {} \; 2>/dev/null)
    
    if [ -n "$webflux_configs" ]; then
        echo -e "${YELLOW}发现WebFlux配置类:${NC}"
        echo "$webflux_configs" | sed 's/^/  /'
        echo ""
    fi
    
    # 检查同一项目中的冲突
    services=("game-gateway" "game-user" "game-hall" "game-agentGame" "game-activity" "game-account")
    
    for service in "${services[@]}"; do
        if [ -d "$service" ]; then
            webmvc_in_service=$(find "$service" -name "*.java" -exec grep -l "@EnableWebMvc\|WebMvcConfigurer" {} \; 2>/dev/null)
            webflux_in_service=$(find "$service" -name "*.java" -exec grep -l "@EnableWebFlux\|WebFluxConfigurer" {} \; 2>/dev/null)
            
            if [ -n "$webmvc_in_service" ] && [ -n "$webflux_in_service" ]; then
                echo -e "${RED}❌ $service 服务同时包含WebMVC和WebFlux配置！${NC}"
                echo "  WebMVC: $webmvc_in_service"
                echo "  WebFlux: $webflux_in_service"
                echo ""
            fi
        fi
    done
}

# 提供修复建议
provide_fix_suggestions() {
    echo -e "${BLUE}修复建议:${NC}"
    echo ""
    
    echo -e "${CYAN}1. Spring Cloud Gateway项目:${NC}"
    echo "   - 移除@EnableWebMvc注解"
    echo "   - 移除spring-boot-starter-web依赖"
    echo "   - 保留WebFlux相关配置"
    echo "   - 使用springdoc-openapi-webflux-ui"
    echo ""
    
    echo -e "${CYAN}2. 普通微服务项目:${NC}"
    echo "   - 使用@EnableWebMvc或spring-boot-starter-web"
    echo "   - 移除@EnableWebFlux注解"
    echo "   - 使用springdoc-openapi-ui"
    echo ""
    
    echo -e "${CYAN}3. 通用规则:${NC}"
    echo "   - 一个项目只能选择WebMVC或WebFlux其中之一"
    echo "   - Gateway项目必须使用WebFlux"
    echo "   - 普通微服务推荐使用WebMVC"
    echo ""
    
    echo -e "${CYAN}4. 依赖选择:${NC}"
    echo "   - WebMVC: spring-boot-starter-web"
    echo "   - WebFlux: spring-boot-starter-webflux"
    echo "   - Gateway: spring-cloud-starter-gateway (自带WebFlux)"
    echo ""
}

# 自动修复常见问题
auto_fix_common_issues() {
    echo -e "${BLUE}自动修复常见问题...${NC}"
    echo ""
    
    echo -n "是否自动修复发现的问题? (y/N): "
    read -r confirm
    
    if [[ ! $confirm =~ ^[Yy]$ ]]; then
        echo "跳过自动修复"
        return
    fi
    
    # 修复网关项目
    gateway_main="game-gateway/src/main/java/com/wingame/gateway/GatewayApplication.java"
    
    if [ -f "$gateway_main" ]; then
        echo -e "${CYAN}修复网关启动类...${NC}"
        
        # 移除@EnableWebMvc
        if grep -q "@EnableWebMvc" "$gateway_main"; then
            sed -i.bak '/@EnableWebMvc/d' "$gateway_main"
            echo "  ✅ 移除@EnableWebMvc注解"
        fi
        
        # 移除不必要的@EnableWebFlux（Gateway自动配置）
        if grep -q "@EnableWebFlux" "$gateway_main"; then
            sed -i.bak '/@EnableWebFlux/d' "$gateway_main"
            echo "  ✅ 移除@EnableWebFlux注解（Gateway自动配置）"
        fi
        
        # 移除相关import
        sed -i.bak '/import.*EnableWebMvc/d' "$gateway_main"
        sed -i.bak '/import.*EnableWebFlux/d' "$gateway_main"
    fi
    
    # 修复网关pom.xml
    gateway_pom="game-gateway/pom.xml"
    
    if [ -f "$gateway_pom" ]; then
        echo -e "${CYAN}修复网关依赖...${NC}"
        
        if grep -q "spring-boot-starter-web" "$gateway_pom"; then
            # 创建临时文件移除spring-boot-starter-web依赖
            awk '
            /<dependency>/ { in_dep = 1; dep = $0; next }
            in_dep && /<artifactId>spring-boot-starter-web<\/artifactId>/ { 
                skip_dep = 1; next 
            }
            in_dep && /<\/dependency>/ { 
                if (!skip_dep) print dep
                in_dep = 0; skip_dep = 0; dep = ""
                if (!skip_dep) print $0
                next
            }
            in_dep { 
                if (!skip_dep) dep = dep "\n" $0
                next
            }
            { print }
            ' "$gateway_pom" > "$gateway_pom.tmp" && mv "$gateway_pom.tmp" "$gateway_pom"
            
            echo "  ✅ 移除spring-boot-starter-web依赖"
        fi
    fi
    
    echo -e "${GREEN}✅ 自动修复完成${NC}"
}

# 测试修复结果
test_fix_results() {
    echo -e "${BLUE}测试修复结果...${NC}"
    echo ""
    
    echo -e "${CYAN}编译网关服务...${NC}"
    if mvn clean compile -pl game-gateway -am -q; then
        echo -e "${GREEN}✅ 网关服务编译成功${NC}"
    else
        echo -e "${RED}❌ 网关服务编译失败${NC}"
        return 1
    fi
    
    echo ""
    echo -e "${CYAN}编译其他服务...${NC}"
    if mvn clean compile -q; then
        echo -e "${GREEN}✅ 所有服务编译成功${NC}"
    else
        echo -e "${RED}❌ 部分服务编译失败${NC}"
        return 1
    fi
    
    return 0
}

# 生成冲突报告
generate_conflict_report() {
    echo -e "${BLUE}生成冲突检查报告...${NC}"
    
    report_file="webmvc_webflux_conflict_report.txt"
    
    cat > "$report_file" << EOF
# WebMVC/WebFlux冲突检查报告
生成时间: $(date)

## 检查结果
EOF
    
    # 检查每个服务
    services=("game-gateway" "game-user" "game-hall" "game-agentGame" "game-activity" "game-account")
    
    for service in "${services[@]}"; do
        if [ -d "$service" ]; then
            echo "" >> "$report_file"
            echo "### $service 服务" >> "$report_file"
            
            # 检查启动类
            main_class=$(find "$service/src/main/java" -name "*Application.java" 2>/dev/null | head -1)
            if [ -f "$main_class" ]; then
                echo "启动类: $(basename "$main_class")" >> "$report_file"
                
                if grep -q "@EnableWebMvc" "$main_class"; then
                    echo "- @EnableWebMvc: ✅" >> "$report_file"
                else
                    echo "- @EnableWebMvc: ❌" >> "$report_file"
                fi
                
                if grep -q "@EnableWebFlux" "$main_class"; then
                    echo "- @EnableWebFlux: ✅" >> "$report_file"
                else
                    echo "- @EnableWebFlux: ❌" >> "$report_file"
                fi
            fi
            
            # 检查依赖
            pom_file="$service/pom.xml"
            if [ -f "$pom_file" ]; then
                echo "依赖检查:" >> "$report_file"
                
                if grep -q "spring-boot-starter-web" "$pom_file"; then
                    echo "- spring-boot-starter-web: ✅" >> "$report_file"
                else
                    echo "- spring-boot-starter-web: ❌" >> "$report_file"
                fi
                
                if grep -q "spring-cloud-starter-gateway" "$pom_file"; then
                    echo "- spring-cloud-starter-gateway: ✅" >> "$report_file"
                else
                    echo "- spring-cloud-starter-gateway: ❌" >> "$report_file"
                fi
            fi
        fi
    done
    
    echo "" >> "$report_file"
    echo "## 建议" >> "$report_file"
    echo "1. Gateway项目应该只使用WebFlux" >> "$report_file"
    echo "2. 普通微服务推荐使用WebMVC" >> "$report_file"
    echo "3. 避免在同一项目中混用WebMVC和WebFlux" >> "$report_file"
    
    echo -e "${GREEN}✅ 报告已生成: $report_file${NC}"
}

# 主函数
main() {
    echo -e "${CYAN}开始WebMVC/WebFlux冲突检查...${NC}"
    echo ""
    
    # 1. 检查冲突
    check_webmvc_webflux_conflict
    
    # 2. 检查配置类冲突
    check_config_conflicts
    
    # 3. 提供修复建议
    provide_fix_suggestions
    
    # 4. 自动修复
    auto_fix_common_issues
    
    # 5. 测试修复结果
    if test_fix_results; then
        echo ""
        echo -e "${GREEN}🎉 修复成功！${NC}"
    else
        echo ""
        echo -e "${RED}❌ 修复后仍有问题，请检查编译错误${NC}"
    fi
    
    # 6. 生成报告
    generate_conflict_report
    
    echo ""
    echo -e "${GREEN}🎉 WebMVC/WebFlux冲突检查完成！${NC}"
    echo ""
    echo -e "${BLUE}关键原则:${NC}"
    echo "• Spring Cloud Gateway 必须使用 WebFlux"
    echo "• 普通微服务推荐使用 WebMVC"
    echo "• 同一项目不能同时使用 WebMVC 和 WebFlux"
    echo "• Gateway项目不需要spring-boot-starter-web依赖"
}

# 运行主函数
main
