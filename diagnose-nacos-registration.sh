#!/bin/bash

echo "=== Nacos服务注册诊断脚本 ==="
echo ""

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 检查Nacos服务状态
check_nacos_status() {
    echo -e "${BLUE}检查Nacos服务状态...${NC}"
    
    # 检查Nacos容器
    if docker ps | grep -q nacos; then
        echo -e "${GREEN}✅ Nacos容器正在运行${NC}"
    else
        echo -e "${RED}❌ Nacos容器未运行${NC}"
        return 1
    fi
    
    # 检查Nacos API
    if curl -s http://localhost:8848/nacos > /dev/null 2>&1; then
        echo -e "${GREEN}✅ Nacos API可访问${NC}"
    else
        echo -e "${RED}❌ Nacos API不可访问${NC}"
        return 1
    fi
    
    # 检查Nacos控制台
    if curl -s http://localhost:8848/nacos/index.html > /dev/null 2>&1; then
        echo -e "${GREEN}✅ Nacos控制台可访问${NC}"
    else
        echo -e "${YELLOW}⚠️ Nacos控制台可能不可访问${NC}"
    fi
    
    return 0
}

# 检查服务注册情况
check_service_registration() {
    echo -e "${BLUE}检查服务注册情况...${NC}"
    
    # 获取服务列表
    services_response=$(curl -s "http://localhost:8848/nacos/v1/ns/service/list?pageNo=1&pageSize=20")
    service_count=$(echo "$services_response" | grep -o '"count":[0-9]*' | cut -d: -f2)
    
    echo "注册的服务数量: $service_count"
    
    if [ "$service_count" -gt 0 ]; then
        echo -e "${GREEN}已注册的服务:${NC}"
        echo "$services_response" | grep -o '"doms":\[[^]]*\]' | sed 's/"doms":\[//;s/\]//;s/"//g' | tr ',' '\n' | sed 's/^/  - /'
    else
        echo -e "${YELLOW}⚠️ 没有服务注册到Nacos${NC}"
    fi
    
    echo ""
}

# 检查微服务运行状态
check_microservices_status() {
    echo -e "${BLUE}检查微服务运行状态...${NC}"
    
    services=(
        "game-gateway:8080:网关服务"
        "game-user:8081:用户服务"
        "game-hall:8082:大厅服务"
        "game-agentGame:8083:代理游戏服务"
        "game-activity:8084:活动服务"
        "game-account:8085:账户服务"
    )
    
    running_services=0
    
    for service in "${services[@]}"; do
        IFS=':' read -r service_name port service_desc <<< "$service"
        
        # 检查进程
        if pgrep -f "$service_name.*jar" > /dev/null 2>&1; then
            echo -e "${GREEN}✅ $service_desc 进程运行中${NC}"
            
            # 检查端口
            if curl -s "http://localhost:$port" > /dev/null 2>&1; then
                echo -e "${GREEN}  ✅ 端口 $port 可访问${NC}"
                ((running_services++))
            else
                echo -e "${YELLOW}  ⚠️ 端口 $port 不可访问${NC}"
            fi
        else
            echo -e "${RED}❌ $service_desc 未运行${NC}"
        fi
        
        echo ""
    done
    
    echo "运行中的服务: $running_services/${#services[@]}"
    echo ""
}

# 检查Nacos配置
check_nacos_config() {
    echo -e "${BLUE}检查Nacos配置...${NC}"
    
    services=("game-gateway" "game-user" "game-hall" "game-agentGame" "game-activity" "game-account")
    
    for service in "${services[@]}"; do
        config_file="$service/src/main/resources/application.yml"
        
        if [ -f "$config_file" ]; then
            echo -e "${CYAN}检查 $service 配置...${NC}"
            
            # 检查Nacos discovery配置
            if grep -q "nacos:" "$config_file"; then
                echo "  ✅ 包含Nacos配置"
                
                # 检查server-addr
                server_addr=$(grep -A5 "discovery:" "$config_file" | grep "server-addr:" | awk '{print $2}')
                if [ -n "$server_addr" ]; then
                    echo "  ✅ server-addr: $server_addr"
                else
                    echo -e "${YELLOW}  ⚠️ 缺少server-addr配置${NC}"
                fi
                
                # 检查namespace
                namespace=$(grep -A5 "discovery:" "$config_file" | grep "namespace:" | awk '{print $2}')
                if [ -n "$namespace" ]; then
                    echo "  ✅ namespace: $namespace"
                else
                    echo -e "${YELLOW}  ⚠️ 缺少namespace配置${NC}"
                fi
            else
                echo -e "${RED}  ❌ 缺少Nacos配置${NC}"
            fi
        else
            echo -e "${RED}❌ $service 配置文件不存在${NC}"
        fi
        
        echo ""
    done
}

# 检查网络连接
check_network_connectivity() {
    echo -e "${BLUE}检查网络连接...${NC}"
    
    # 检查本地Nacos连接
    echo -e "${CYAN}测试Nacos连接...${NC}"
    
    if nc -z localhost 8848 2>/dev/null; then
        echo "✅ 端口8848可连接"
    else
        echo -e "${RED}❌ 端口8848不可连接${NC}"
    fi
    
    if nc -z localhost 9848 2>/dev/null; then
        echo "✅ 端口9848可连接"
    else
        echo -e "${YELLOW}⚠️ 端口9848不可连接${NC}"
    fi
    
    # 测试Nacos API
    echo -e "${CYAN}测试Nacos API...${NC}"
    
    api_response=$(curl -s -w "%{http_code}" "http://localhost:8848/nacos/v1/ns/service/list" -o /dev/null)
    
    if [ "$api_response" = "200" ]; then
        echo "✅ Nacos API响应正常"
    else
        echo -e "${RED}❌ Nacos API响应异常 (HTTP $api_response)${NC}"
    fi
    
    echo ""
}

# 检查命名空间
check_namespaces() {
    echo -e "${BLUE}检查Nacos命名空间...${NC}"
    
    namespaces_response=$(curl -s "http://localhost:8848/nacos/v1/console/namespaces")
    
    if echo "$namespaces_response" | grep -q "dev"; then
        echo -e "${GREEN}✅ dev命名空间存在${NC}"
    else
        echo -e "${YELLOW}⚠️ dev命名空间不存在${NC}"
        echo "创建dev命名空间..."
        
        create_response=$(curl -s -X POST "http://localhost:8848/nacos/v1/console/namespaces" \
            -d "customNamespaceId=dev&namespaceName=dev&namespaceDesc=开发环境")
        
        if echo "$create_response" | grep -q "true"; then
            echo -e "${GREEN}✅ dev命名空间创建成功${NC}"
        else
            echo -e "${RED}❌ dev命名空间创建失败${NC}"
        fi
    fi
    
    echo ""
}

# 重启服务并监控注册
restart_and_monitor() {
    echo -e "${BLUE}重启服务并监控注册...${NC}"
    
    echo -n "是否重启所有微服务并监控注册过程? (y/N): "
    read -r confirm
    
    if [[ ! $confirm =~ ^[Yy]$ ]]; then
        echo "跳过重启"
        return
    fi
    
    # 停止现有服务
    echo -e "${CYAN}停止现有服务...${NC}"
    pkill -f "game-.*jar" 2>/dev/null || true
    sleep 3
    
    # 重新编译
    echo -e "${CYAN}重新编译项目...${NC}"
    if mvn clean package -DskipTests -q; then
        echo -e "${GREEN}✅ 编译成功${NC}"
    else
        echo -e "${RED}❌ 编译失败${NC}"
        return 1
    fi
    
    # 启动服务
    echo -e "${CYAN}启动微服务...${NC}"
    
    services=("game-gateway" "game-user" "game-hall" "game-agentGame" "game-activity" "game-account")
    
    for service in "${services[@]}"; do
        echo "启动 $service..."
        
        cd "$service"
        nohup java -jar target/$service-1.0-SNAPSHOT.jar > "../logs/${service#game-}.log" 2>&1 &
        service_pid=$!
        cd ..
        
        echo "  PID: $service_pid"
        
        # 等待服务启动
        sleep 10
        
        # 检查注册状态
        echo "  检查注册状态..."
        for i in {1..6}; do
            services_response=$(curl -s "http://localhost:8848/nacos/v1/ns/service/list?pageNo=1&pageSize=20")
            
            if echo "$services_response" | grep -q "$service"; then
                echo -e "${GREEN}  ✅ $service 已注册到Nacos${NC}"
                break
            else
                echo -n "."
                sleep 5
            fi
            
            if [ $i -eq 6 ]; then
                echo ""
                echo -e "${YELLOW}  ⚠️ $service 注册超时${NC}"
            fi
        done
        
        echo ""
    done
}

# 生成诊断报告
generate_diagnosis_report() {
    echo -e "${BLUE}生成诊断报告...${NC}"
    
    report_file="nacos_registration_diagnosis.txt"
    
    cat > "$report_file" << EOF
# Nacos服务注册诊断报告
生成时间: $(date)

## Nacos状态
EOF
    
    # Nacos状态
    if docker ps | grep -q nacos; then
        echo "- Nacos容器: 运行中 ✅" >> "$report_file"
    else
        echo "- Nacos容器: 未运行 ❌" >> "$report_file"
    fi
    
    if curl -s http://localhost:8848/nacos > /dev/null 2>&1; then
        echo "- Nacos API: 可访问 ✅" >> "$report_file"
    else
        echo "- Nacos API: 不可访问 ❌" >> "$report_file"
    fi
    
    # 服务注册状态
    echo "" >> "$report_file"
    echo "## 服务注册状态" >> "$report_file"
    
    services_response=$(curl -s "http://localhost:8848/nacos/v1/ns/service/list?pageNo=1&pageSize=20")
    service_count=$(echo "$services_response" | grep -o '"count":[0-9]*' | cut -d: -f2)
    
    echo "注册服务数量: $service_count" >> "$report_file"
    
    if [ "$service_count" -gt 0 ]; then
        echo "已注册服务:" >> "$report_file"
        echo "$services_response" | grep -o '"doms":\[[^]]*\]' | sed 's/"doms":\[//;s/\]//;s/"//g' | tr ',' '\n' | sed 's/^/- /' >> "$report_file"
    fi
    
    # 微服务状态
    echo "" >> "$report_file"
    echo "## 微服务状态" >> "$report_file"
    
    services=("game-gateway:8080" "game-user:8081" "game-hall:8082" "game-agentGame:8083" "game-activity:8084" "game-account:8085")
    
    for service in "${services[@]}"; do
        IFS=':' read -r service_name port <<< "$service"
        
        if pgrep -f "$service_name.*jar" > /dev/null 2>&1; then
            echo "- $service_name: 运行中 ✅" >> "$report_file"
        else
            echo "- $service_name: 未运行 ❌" >> "$report_file"
        fi
    done
    
    echo "" >> "$report_file"
    echo "## 建议" >> "$report_file"
    echo "1. 确保Nacos服务正常运行" >> "$report_file"
    echo "2. 检查微服务的Nacos配置" >> "$report_file"
    echo "3. 验证网络连接" >> "$report_file"
    echo "4. 检查命名空间配置" >> "$report_file"
    echo "5. 查看服务启动日志" >> "$report_file"
    
    echo -e "${GREEN}✅ 报告已生成: $report_file${NC}"
}

# 主函数
main() {
    echo -e "${CYAN}开始Nacos服务注册诊断...${NC}"
    echo ""
    
    # 1. 检查Nacos状态
    if ! check_nacos_status; then
        echo -e "${RED}❌ Nacos服务有问题，请先修复Nacos${NC}"
        echo "建议运行: ./start-infrastructure.sh"
        exit 1
    fi
    
    echo ""
    
    # 2. 检查服务注册情况
    check_service_registration
    
    # 3. 检查微服务状态
    check_microservices_status
    
    # 4. 检查Nacos配置
    check_nacos_config
    
    # 5. 检查网络连接
    check_network_connectivity
    
    # 6. 检查命名空间
    check_namespaces
    
    # 7. 重启并监控（可选）
    restart_and_monitor
    
    # 8. 生成报告
    generate_diagnosis_report
    
    echo ""
    echo -e "${GREEN}🎉 Nacos服务注册诊断完成！${NC}"
    echo ""
    echo -e "${BLUE}常见问题解决方案:${NC}"
    echo "• 服务未注册: 检查Nacos配置和网络连接"
    echo "• 启动失败: 查看logs/目录下的日志文件"
    echo "• 端口冲突: 使用./stop-services.sh停止服务"
    echo "• 配置错误: 检查application.yml中的Nacos配置"
    echo ""
    echo -e "${BLUE}有用的命令:${NC}"
    echo "• 查看Nacos服务: curl http://localhost:8848/nacos/v1/ns/service/list"
    echo "• 重启基础设施: ./start-infrastructure.sh"
    echo "• 重启微服务: ./start-services.sh"
    echo "• 查看日志: tail -f logs/gateway.log"
}

# 运行主函数
main
