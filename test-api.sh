#!/bin/bash

echo "测试微服务API..."

# 等待服务启动
echo "等待服务启动..."
sleep 5

# 测试网关健康检查
echo "测试网关服务..."
curl -s http://localhost:8080/actuator/health || echo "网关服务未启动"

# 测试用户服务注册
echo "测试用户注册..."
curl -X POST http://localhost:8080/user/api/user/register \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "password": "123456",
    "confirmPassword": "123456",
    "nickname": "测试用户",
    "email": "<EMAIL>",
    "phone": "13800138000"
  }' || echo "用户注册测试失败"

# 测试用户登录
echo "测试用户登录..."
curl -X POST http://localhost:8080/user/api/user/login \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "password": "123456"
  }' || echo "用户登录测试失败"

# 测试检查用户名
echo "测试检查用户名..."
curl -s http://localhost:8080/user/api/user/check/username/testuser || echo "检查用户名测试失败"

echo "API测试完成！"
